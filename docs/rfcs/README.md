# MasterCRM RFCs

Bu dizin MasterCRM projesinin RFC (Request for Comments) belgelerini içerir.

## RFC Dizin Yapısı

### 1.Shared
- [RFC-001](./1.Shared/001-modular-architecture.md) - Modü<PERSON>
- [RFC-002](./1.Shared/002-shared-kernel.md) - Shared Kernel Implementation
- [RFC-003](./1.Shared/003-database-structure.md) - Veritabanı Yapısı
- [RFC-004](./1.Shared/004-audit-logging.md) - Audit Logging
- [RFC-005](./1.Shared/005-reporting-analytics.md) - Raporlama ve Analytics
- [RFC-006](./1.Shared/006-performance.md) - Performans Optimizasyonu
- [RFC-007](./1.Shared/007-security.md) - Güvenlik Önlemleri

### 2.Users
- [RFC-001](./2.Users/001-user-management.md) - Kullanıcı Yönetimi
- [RFC-002](./2.Users/002-authentication.md) - Kimlik Doğrulama ve Yetkilendirme

### 3.Customers
- [RFC-001](./3.Customers/001-customer-management.md) - Müşteri Yönetimi
- [RFC-002](./3.Customers/002-customer-advisor.md) - Müşteri-Danışman İlişkisi

### 4.DynamicForms
- [RFC-001](./4.DynamicForms/001-dynamic-forms.md) - Dinamik Form Yapısı

### 5.Conversations
- [RFC-001](./5.Conversations/001-3cx-integration.md) - 3CX Entegrasyonu
- [RFC-002](./5.Conversations/002-call-management.md) - Çağrı Yönetimi
- [RFC-003](./5.Conversations/003-chat-system.md) - Chat Sistemi

### 6.Sales
- [RFC-001](./6.Sales/001-sales-management.md) - Satış Yönetimi

### 7.Requests
- [RFC-001](./7.Requests/001-ticket-management.md) - Ticket Yönetimi
- [RFC-002](./7.Requests/002-workflow-engine.md) - İş Akışı Motoru
- [RFC-003](./7.Requests/003-sla-management.md) - SLA Yönetimi

### 8.Tasks
- [RFC-001](./8.Tasks/001-task-management.md) - Görev Yönetimi

### 9.General
- [RFC-001](./9.General/001-language-management.md) - Dil Yönetimi
- [RFC-002](./9.General/002-country-state-city-management.md) - Ülke/Şehir Yönetimi
- [RFC-003](./9.General/003-media-management.md) - Medya Yönetimi

### 10.Calendar
- [RFC-001](./10.Calendar/001-calendar-note-management.md) - Takvim Notu Yönetimi
- [RFC-002](./10.Calendar/002-multi-department-support.md) - Çoklu Departman Desteği
- [RFC-003](./10.Calendar/003-localization-strategy.md) - Lokalizasyon Stratejisi
- [RFC-004](./10.Calendar/004-performance-optimization.md) - Performans Optimizasyonu
- [RFC-005](./10.Calendar/005-api-design.md) - API Tasarım Rehberi
