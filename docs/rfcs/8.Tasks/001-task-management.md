# RFC 001: Task Y<PERSON><PERSON>im <PERSON>lü

## Status
Tamamlandı

## Özet
Bu RFC (Request for Comments) do<PERSON><PERSON><PERSON><PERSON>, MasterCRM sistemi için Task Yönetim <PERSON>ü<PERSON>ün tasarımını ve implementasyonunu detaylandırmaktadır. Task <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> görevleri oluşturmasına, atamasına, takip etmesine ve yönetmesine olanak tanıyan bir bileşendir.

## Motivasyon
- Kullanıcıların görevleri etkili bir şekilde yönetebilmesi
- Görevlerin belirli kişilere veya departmanlara atanabilmesi
- Görevlerin önceliklendirilmesi ve durumlarının takibi
- Bildirim mekanizması ile görev takibinin kolaylaştırılması
- Ticket sistemi ile entegrasyon

## Teknik Tasarım

### Veri Modeli

```sql
-- Tasks Module Database Schema
CREATE TABLE Tasks.Task (
    Id UNIQUEIDENTIFIER PRIMARY KEY,
    InsertDate DATETIME2 NOT NULL,
    UpdateDate DATETIME2,
    InsertUserId UNIQUEIDENTIFIER,
    UpdateUserId UNIQUEIDENTIFIER,
    History NVARCHAR(MAX),
    Title NVARCHAR(200) NOT NULL,
    Description NVARCHAR(MAX),
    NotificationWay INT NOT NULL, -- Enum: 1: Email, 2: SMS, 3: Both, 4: None
    UserId UNIQUEIDENTIFIER, -- Assigned user, nullable
    ReporterUserId UNIQUEIDENTIFIER NOT NULL, -- Reporter user
    Priority INT NOT NULL, -- Enum: 1: Low, 2: Medium, 3: High, 4: Critical
    StatusId UNIQUEIDENTIFIER NOT NULL, -- FK to Tasks.Status
    EndDate DATETIME2 -- Deadline, nullable
);

CREATE TABLE Tasks.TaskDepartment (
    Id UNIQUEIDENTIFIER PRIMARY KEY,
    TaskId UNIQUEIDENTIFIER NOT NULL,
    DepartmentId UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT FK_TaskDepartment_Task FOREIGN KEY (TaskId) REFERENCES Tasks.Task(Id),
    CONSTRAINT FK_TaskDepartment_Department FOREIGN KEY (DepartmentId) REFERENCES Organization.Department(Id)
);

CREATE TABLE Tasks.Status (
    Id UNIQUEIDENTIFIER PRIMARY KEY,
    Name NVARCHAR(100) NOT NULL,
    ColorCode NVARCHAR(7) NOT NULL, -- HEX color code
    Order INT NOT NULL
);

CREATE TABLE Tasks.Watchlist (
    Id UNIQUEIDENTIFIER PRIMARY KEY,
    TaskId UNIQUEIDENTIFIER NOT NULL,
    UserId UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT FK_Watchlist_Task FOREIGN KEY (TaskId) REFERENCES Tasks.Task(Id),
    CONSTRAINT FK_Watchlist_User FOREIGN KEY (UserId) REFERENCES Users.User(Id)
);

CREATE TABLE Tasks.Comment (
    Id UNIQUEIDENTIFIER PRIMARY KEY,
    TaskId UNIQUEIDENTIFIER NOT NULL,
    UserId UNIQUEIDENTIFIER NOT NULL,
    Content NVARCHAR(MAX) NOT NULL,
    CommentDate DATETIME2 NOT NULL,
    CONSTRAINT FK_Comment_Task FOREIGN KEY (TaskId) REFERENCES Tasks.Task(Id),
    CONSTRAINT FK_Comment_User FOREIGN KEY (UserId) REFERENCES Users.User(Id)
);
```

### API Endpoints

#### Task Modülü Endpoints
```
GET    /api/v1/tasks/tasks
POST   /api/v1/tasks/tasks
GET    /api/v1/tasks/tasks/{id}
PUT    /api/v1/tasks/tasks/{id}
DELETE /api/v1/tasks/tasks/{id}
GET    /api/v1/tasks/tasks/user/{userId}
GET    /api/v1/tasks/tasks/department/{departmentId}
GET    /api/v1/tasks/tasks/status/{statusId}
POST   /api/v1/tasks/tasks/{id}/comment
GET    /api/v1/tasks/tasks/{id}/comments
GET    /api/v1/tasks/statuses
POST   /api/v1/tasks/statuses
PUT    /api/v1/tasks/statuses/{id}
DELETE /api/v1/tasks/statuses/{id}
POST   /api/v1/tasks/tasks/{id}/watchlist
DELETE /api/v1/tasks/tasks/{id}/watchlist/{userId}
```

### Proje Yapısı
```
MasterCRM/
├── src/
│   ├── Modules/
│   │   ├── Tasks/
│   │   │   ├── Application/
│   │   │   │   │   ├── Tasks/
│   │   │   │   │   │   │   ├── CreateTask/
│   │   │   │   │   │   │   │   ├── CreateTaskCommand.cs
│   │   │   │   │   │   │   │   ├── CreateTaskCommandHandler.cs
│   │   │   │   │   │   │   │   ├── CreateTaskCommandValidator.cs
│   │   │   │   │   │   │   │   └── CreateTaskEndpoint.cs
│   │   │   │   │   │   │   ├── UpdateTask/
│   │   │   │   │   │   │   ├── DeleteTask/
│   │   │   │   │   │   │   ├── AddTaskComment/
│   │   │   │   │   │   │   ├── AddTaskToWatchlist/
│   │   │   │   │   │   │   ├── RemoveTaskFromWatchlist/
│   │   │   │   │   │   │   ├── GetTaskById/
│   │   │   │   │   │   │   │   ├── GetTaskByIdQuery.cs
│   │   │   │   │   │   │   │   ├── GetTaskByIdQueryHandler.cs
│   │   │   │   │   │   │   │   └── GetTaskByIdEndpoint.cs
│   │   │   │   │   │   │   ├── GetTasksByUser/
│   │   │   │   │   │   │   ├── GetTasksByDepartment/
│   │   │   │   │   │   │   ├── GetTasksByStatus/
│   │   │   │   │   │   │   ├── GetTaskComments/
│   │   │   │   │   ├── Statuses/
│   │   │   │   │   ├── TaskDto.cs
│   │   │   │   │   ├── StatusDto.cs
│   │   │   │   │   ├── TaskCommentDto.cs
│   │   │   ├── Domain/
│   │   │   │   ├── Entities/
│   │   │   │   │   ├── Task.cs
│   │   │   │   │   ├── NotificationWay.cs
│   │   │   │   │   ├── Priority.cs
│   │   │   │   │   ├── Status.cs
│   │   │   │   │   ├── TaskDepartment.cs
│   │   │   │   │   ├── Watchlist.cs
│   │   │   │   │   └── Comment.cs
│   │   │   │   └── Events/
│   │   │   │       ├── TaskCreatedEvent.cs
│   │   │   │       ├── TaskUpdatedEvent.cs
│   │   │   │       └── TaskCommentAddedEvent.cs
│   │   │   ├── Infrastructure/
│   │   │   │   ├── Data/
│   │   │   │   │   ├── Configuration/
│   │   │   │   │   │   ├── TaskConfiguration.cs
│   │   │   │   │   │   ├── StatusConfiguration.cs
│   │   │   │   │   │   ├── TaskDepartmentConfiguration.cs
│   │   │   │   │   │   ├── WatchlistConfiguration.cs
│   │   │   │   │   │   └── CommentConfiguration.cs
│   │   │   │   │   ├── TaskDbContext.cs
│   │   │   └── ModuleInitializer.cs
```

### Örnek CQRS Implementasyonu

#### CreateTaskCommand.cs
```csharp
using MediatR;
using System;
using System.Collections.Generic;

namespace Tasks.Application.Tasks.Commands.CreateTask
{
    public class CreateTaskCommand : IRequest<Guid>
    {
        public string Title { get; set; }
        public string Description { get; set; }
        public int NotificationWay { get; set; }
        public Guid? UserId { get; set; }
        public Guid ReporterUserId { get; set; }
        public List<Guid> DepartmentIds { get; set; }
        public int Priority { get; set; }
        public Guid StatusId { get; set; }
        public DateTime? EndDate { get; set; }
        public List<WatchlistDto> Watchlist { get; set; }
    }

    public class TaskFileDto
    {
        public string FileName { get; set; }
        public string FilePath { get; set; }
        public long FileSize { get; set; }
        public string ContentType { get; set; }
    }

    public class WatchlistDto
    {
        public Guid UserId { get; set; }
    }
}
```

#### CreateTaskCommandHandler.cs
```csharp
using Tasks.Domain.Entities;
using Tasks.Infrastructure.Data;
using MediatR;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Tasks.Application.Tasks.Commands.CreateTask
{
    public class CreateTaskCommandHandler : IRequestHandler<CreateTaskCommand, Guid>
    {
        private readonly TaskDbContext _dbContext;
        private readonly IMediator _mediator;

        public CreateTaskCommandHandler(TaskDbContext dbContext, IMediator mediator)
        {
            _dbContext = dbContext;
            _mediator = mediator;
        }

        public async Task<Guid> Handle(CreateTaskCommand request, CancellationToken cancellationToken)
        {
            var task = new Task
            {
                Id = Guid.NewGuid(),
                Title = request.Title,
                Description = request.Description,
                NotificationWay = request.NotificationWay,
                UserId = request.UserId,
                ReporterUserId = request.ReporterUserId,
                Priority = request.Priority,
                StatusId = request.StatusId,
                EndDate = request.EndDate,
                InsertDate = DateTime.Now,
                InsertUserId = request.ReporterUserId
            };

            _dbContext.Tasks.Add(task);

            // Departman ilişkilerini ekle
            if (request.DepartmentIds != null && request.DepartmentIds.Any())
            {
                foreach (var departmentId in request.DepartmentIds)
                {
                    _dbContext.TaskDepartments.Add(new TaskDepartment
                    {
                        Id = Guid.NewGuid(),
                        TaskId = task.Id,
                        DepartmentId = departmentId
                    });
                }
            }

            // Dosya ilişkilerini ekle
            if (request.TaskFiles != null && request.TaskFiles.Any())
            {
                foreach (var file in request.TaskFiles)
                {
                    _dbContext.TaskFiles.Add(new TaskFile
                    {
                        Id = Guid.NewGuid(),
                        TaskId = task.Id,
                        FileName = file.FileName,
                        FilePath = file.FilePath,
                        FileSize = file.FileSize,
                        ContentType = file.ContentType,
                        UploadDate = DateTime.Now,
                        UploadUserId = request.ReporterUserId
                    });
                }
            }

            // İzleme listesi ilişkilerini ekle
            if (request.Watchlist != null && request.Watchlist.Any())
            {
                foreach (var watch in request.Watchlist)
                {
                    _dbContext.Watchlists.Add(new Watchlist
                    {
                        Id = Guid.NewGuid(),
                        TaskId = task.Id,
                        UserId = watch.UserId,
                    });
                }
            }

            await _dbContext.SaveChangesAsync(cancellationToken);

            // Task oluşturuldu olayını yayınla
            await _mediator.Publish(new TaskCreatedEvent { TaskId = task.Id }, cancellationToken);

            return task.Id;
        }
    }
}
```

#### CreateTaskEndpoint.cs
```csharp
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using MediatR;
using System.Threading.Tasks;
using MasterCRM.Shared.Endpoints;

namespace Tasks.Application.Tasks.Commands.CreateTask
{
    [Route("api/v1/tasks/tasks")]
    public class CreateTaskEndpoint : EndpointBase
    {
        private readonly IMediator _mediator;

        public CreateTaskEndpoint(IMediator mediator)
        {
            _mediator = mediator;
        }

        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult> HandleAsync([FromBody] CreateTaskCommand command)
        {
            var taskId = await _mediator.Send(command);
            return CreatedAtAction(nameof(GetTaskById), new { id = taskId }, null);
        }
    }
}
```

#### GetTaskByIdQuery.cs
```csharp
using MediatR;
using System;

namespace Tasks.Application.Tasks.Queries.GetTaskById
{
    public class GetTaskByIdQuery : IRequest<TaskDetailsDto>
    {
        public Guid Id { get; set; }
    }
}
```

#### GetTaskByIdQueryHandler.cs
```csharp
using Tasks.Application.Common.Dtos;
using Tasks.Infrastructure.Data;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Tasks.Application.Tasks.Queries.GetTaskById
{
    public class GetTaskByIdQueryHandler : IRequestHandler<GetTaskByIdQuery, TaskDetailsDto>
    {
        private readonly TaskDbContext _dbContext;

        public GetTaskByIdQueryHandler(TaskDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        public async Task<TaskDetailsDto> Handle(GetTaskByIdQuery request, CancellationToken cancellationToken)
        {
            var task = await _dbContext.Tasks
                .Include(t => t.Status)
                .Include(t => t.TaskDepartments)
                .Include(t => t.Watchlist)
                .FirstOrDefaultAsync(t => t.Id == request.Id, cancellationToken);

            if (task == null)
            {
                return null;
            }

            return new TaskDetailsDto
            {
                Id = task.Id,
                Title = task.Title,
                Description = task.Description,
                NotificationWay = task.NotificationWay,
                UserId = task.UserId,
                ReporterUserId = task.ReporterUserId,
                Priority = task.Priority,
                Status = new StatusDto
                {
                    Id = task.Status.Id,
                    Name = task.Status.Name,
                    ColorCode = task.Status.ColorCode
                },
                EndDate = task.EndDate,
                InsertDate = task.InsertDate,
                UpdateDate = task.UpdateDate,
                DepartmentIds = task.TaskDepartments.Select(td => td.DepartmentId).ToList(),
                Files = task.TaskFiles.Select(tf => new TaskFileDto
                {
                    Id = tf.Id,
                    FileName = tf.FileName,
                    FilePath = tf.FilePath,
                    FileSize = tf.FileSize,
                    ContentType = tf.ContentType,
                    UploadDate = tf.UploadDate,
                    UploadUserId = tf.UploadUserId
                }).ToList(),
                Watchlist = task.Watchlist.Select(w => new WatchlistDto
                {
                    UserId = w.UserId,
                }).ToList()
            };
        }
    }
}
```

### Service Registration

#### ModuleInitializer.cs
```csharp
using Tasks.Infrastructure.Data;
using Tasks.Infrastructure.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System.Reflection;

namespace Tasks
{
    public static class ModuleInitializer.cs
    {
        public static IServiceCollection AddTaskModule(this IServiceCollection services, IConfiguration configuration)
        {
            // DbContext ekle
            services.AddDbContext<TaskDbContext>(options =>
                options.UseSqlServer(
                    configuration.GetConnectionString("DefaultConnection"),
                    sqlOptions => sqlOptions.MigrationsHistoryTable("__EFMigrationsHistory", "Tasks")));

            // MediatR Handlers'ları kaydet
            services.AddMediatR(cfg => {
                cfg.RegisterServicesFromAssembly(Assembly.GetExecutingAssembly());
            });

            // AutoMapper Profilleri
            services.AddAutoMapper(Assembly.GetExecutingAssembly());

            // Servisler
            services.AddScoped<IFileStorageService, FileStorageService>();

            return services;
        }
    }
}
```

## Modüller Arası Entegrasyonlar

### Kullanıcı Modülü Entegrasyonu
- Görev atama ve izleme listesi için kullanıcı bilgilerine erişim
- Kullanıcı değişikliklerinde görev bilgilerinin güncellenmesi

### Departman Modülü Entegrasyonu
- Görevlerin departmanlara atanabilmesi
- Departman değişikliklerinde görev bilgilerinin güncellenmesi

### Bildirim Sistemi Entegrasyonu
- Görev durumu değişikliklerinde bildirim gönderimi
- İzleme listesindeki kullanıcılara bildirimler

### Dosya Yönetimi Entegrasyonu
- Görevlere dosya yükleme
- Dosya depolama servisi ile entegrasyon

## Uygulama Stratejisi

### Geliştirme Aşamaları
1. Temel Veri Modelinin Oluşturulması (1 hafta)
   - Veritabanı tablolarının tasarımı
   - Entity modellerinin implementasyonu
   - DbContext yapılandırması

2. CQRS Yapısının Kurulması (1 hafta)
   - Command ve Query sınıflarının oluşturulması
   - Handler'ların implementasyonu
   - Validation kurallarının tanımlanması

3. Core Özelliklerin Geliştirilmesi (2 hafta)
   - Görev oluşturma ve atama
   - Görevleri listeleme ve filtreleme
   - Görev detaylarını görüntüleme

4. İleri Özelliklerin Geliştirilmesi (2 hafta)
   - Dosya yükleme işlemleri
   - Yorumlar ve izleme listesi
   - Bildirim mekanizması

5. Entegrasyonlar (1 hafta)
   - Diğer modüllerle entegrasyon
   - Sistem genelinde uyumluluk testleri

6. Test ve Optimizasyon (1 hafta)
   - Unit ve entegrasyon testleri
   - Performans optimizasyonu
   - Hata düzeltmeleri

### Test Stratejisi
- Unit Testler
  - Command ve Query Handler'ları için birim testler
  - Validation kuralları için testler

- Integration Testler
  - Veritabanı işlemleri için entegrasyon testleri
  - Modüller arası entegrasyon testleri

## Riskler ve Azaltma Stratejileri

1. Performans Riskleri:
   - Risk: Çok sayıda görevin filtrelenmesi ve listelenmesi performans sorunlarına yol açabilir.
   - Azaltma: İndeksleme, sayfalama ve filtreleme optimizasyonları

2. Ölçeklenebilirlik Riskleri:
   - Risk: Sistem büyüdükçe görev sayısının artması veritabanı performansını etkileyebilir.
   - Azaltma: Partitioning, arşivleme stratejileri

3. Entegrasyon Riskleri:
   - Risk: Diğer modüllerle entegrasyonlarda veri tutarsızlıkları oluşabilir.
   - Azaltma: Event-driven mimarinin kullanılması, transaction yönetimi

## Başarı Metrikleri
- Görev oluşturma/atama işlemlerinin ortalama süresi < 500ms
- Görev listeleme sorguları < 200ms
- Task modülü API başarı oranı > 99%
- Concurrent kullanıcılar tarafından oluşturulan görev sayısı > 50/dakika