# RFC 003 - Transition Rules System

## Status
Proposed

## Özet
Bu RFC, MasterCRM sistemi içerisinde Requests modülü altında bulunan Workflow yönetim sistemine gelişmiş kural motoru (Rule Engine) eklenmesini tanımlar. Sistem, transition'lar sıras<PERSON><PERSON> yet<PERSON>, al<PERSON>, al<PERSON>, bildiri<PERSON> gönder<PERSON> gibi işlemleri otomatik olarak gerçekleştiren esnek ve genişletilebilir bir yapı sağlar.

## Motivasyon
- Transition'lar sırasında kullanıcı bazlı yetkilendirme kontrolü ihtiyacı
- Belirli alanlara göre geçiş kısıtlamaları uygulama
- Geçiş işlemleri sırasında otomatik alan güncellemeleri
- Email, SMS ve in-app bildirimlerinin otomatik gönderilmesi
- Kural çalıştırmalarının detaylı loglanması
- Modüler ve genişletilebilir kural sistemi oluşturma

## Teknik Detaylar

### Yeni Entity Tasarımları

#### TransitionRule Entity
```csharp
public class TransitionRule : BaseEntity
{
    public Guid Id { get; set; }
    public Guid TransitionId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public RuleType RuleType { get; set; }
    public int Order { get; set; } // Execution order
    public bool IsActive { get; set; } = true;
    public string Configuration { get; set; } = "{}"; // JSON configuration

    // Navigation Properties
    public Transition Transition { get; set; } = null!;
    public ICollection<RuleExecution> RuleExecutions { get; set; } = new List<RuleExecution>();
}

public enum RuleType
{
    Authorization = 1,      // Kullanıcı/rol bazlı kısıtlama
    FieldValidation = 2,    // Alan bazlı doğrulama
    FieldModification = 3,  // Alan değiştirme
    Notification = 4,       // Bildirim gönderme
    Approval = 5,          // Onay süreci
    Schedule = 6,          // Zamanlama kuralları
    Custom = 99            // Özel kurallar
}
```

#### RuleExecution Entity (Comprehensive Logging)
```csharp
public class RuleExecution : BaseEntity
{
    public Guid Id { get; set; }
    public Guid TransitionRuleId { get; set; }
    public Guid TicketId { get; set; }
    public Guid ExecutedByUserId { get; set; }
    public DateTime ExecutedAt { get; set; }
    public RuleExecutionStatus Status { get; set; }
    public string InputData { get; set; } = string.Empty; // JSON input context
    public string OutputData { get; set; } = string.Empty; // JSON result
    public string ErrorMessage { get; set; } = string.Empty;
    public int ExecutionTimeMs { get; set; }

    // Navigation Properties
    public TransitionRule TransitionRule { get; set; } = null!;
}

public enum RuleExecutionStatus
{
    Success = 1,
    Failed = 2,
    Skipped = 3,
    Pending = 4
}
```

### Rule Engine Pattern

#### IRuleEngine Interface
```csharp
public interface IRuleEngine
{
    Task<RuleExecutionResult> ExecuteRulesAsync(
        Guid transitionId,
        TransitionContext context,
        CancellationToken cancellationToken = default);

    Task<RuleValidationResult> ValidateRulesAsync(
        Guid transitionId,
        TransitionContext context,
        CancellationToken cancellationToken = default);
}

public class RuleEngine(
    IServiceProvider serviceProvider,
    RequestsDbContext context,
    ILogger<RuleEngine> logger) : IRuleEngine
{
    public async Task<RuleExecutionResult> ExecuteRulesAsync(
        Guid transitionId,
        TransitionContext context,
        CancellationToken cancellationToken = default)
    {
        var rules = await GetActiveRulesAsync(transitionId, cancellationToken);
        var result = new RuleExecutionResult();

        // Execution Order - kuralları sırayla çalıştır
        foreach (var rule in rules.OrderBy(r => r.Order))
        {
            var stopwatch = Stopwatch.StartNew();
            var executionId = Guid.NewGuid();

            try
            {
                var handler = GetRuleHandler(rule.RuleType);
                var ruleResult = await handler.ExecuteAsync(rule, context, cancellationToken);

                stopwatch.Stop();

                // Comprehensive Logging
                await LogRuleExecutionAsync(rule, context, executionId, ruleResult, stopwatch.ElapsedMilliseconds);

                if (!ruleResult.IsSuccess)
                {
                    result.Errors.AddRange(ruleResult.Errors);
                    if (ruleResult.IsBlockingFailure)
                        break; // Kritik hata durumunda dur
                }

                result.ModifiedFields.AddRange(ruleResult.ModifiedFields);
                result.PendingNotifications.AddRange(ruleResult.PendingNotifications);
                result.Warnings.AddRange(ruleResult.Warnings);
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                logger.LogError(ex, "Rule execution failed for rule {RuleId}", rule.Id);

                await LogRuleExecutionErrorAsync(rule, context, executionId, ex, stopwatch.ElapsedMilliseconds);
                result.Errors.Add($"Kural çalıştırma hatası: {rule.Name}");
            }
        }

        result.IsSuccess = !result.Errors.Any();
        return result;
    }
}
```

#### Base Rule Handler
```csharp
public abstract class BaseRuleHandler
{
    public abstract RuleType RuleType { get; }

    public abstract Task<RuleResult> ExecuteAsync(
        TransitionRule rule,
        TransitionContext context,
        CancellationToken cancellationToken);

    protected T GetConfiguration<T>(TransitionRule rule) where T : class
    {
        return JsonSerializer.Deserialize<T>(rule.Configuration)
               ?? throw new InvalidOperationException($"Invalid configuration for rule {rule.Id}");
    }
}
```

### Rich Context Implementation

#### TransitionContext
```csharp
public class TransitionContext
{
    public Guid TicketId { get; set; }
    public Guid UserId { get; set; }
    public Guid OrganizationId { get; set; }
    public Dictionary<string, object> TicketData { get; set; } = new();
    public Dictionary<string, object> UserData { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
    public Node FromNode { get; set; } = null!;
    public Node ToNode { get; set; } = null!;
    public DateTime TransitionTime { get; set; } = DateTime.UtcNow;

    // Helper methods
    public T GetTicketField<T>(string fieldName) =>
        TicketData.TryGetValue(fieldName, out var value) ? (T)value : default(T);

    public T GetUserField<T>(string fieldName) =>
        UserData.TryGetValue(fieldName, out var value) ? (T)value : default(T);

    public void SetTicketField(string fieldName, object value) =>
        TicketData[fieldName] = value;
}
```

### JSON Configuration Examples

#### Authorization Rule Configuration
```csharp
public class AuthorizationConfiguration
{
    public List<string>? AllowedRoles { get; set; }
    public List<string>? AllowedUsers { get; set; }
    public List<string>? RequiredPermissions { get; set; }
    public DepartmentRestriction? DepartmentRestriction { get; set; }
}

public class DepartmentRestriction
{
    public string Field { get; set; } = string.Empty;
    public bool AllowSameDepartment { get; set; }
    public List<string>? AllowedDepartments { get; set; }
}

// JSON Örneği:
{
  "allowedRoles": ["Admin", "Manager"],
  "allowedUsers": ["user-guid-1", "user-guid-2"],
  "requiredPermissions": ["CanApproveTicket"],
  "departmentRestriction": {
    "field": "DepartmentId",
    "allowSameDepartment": true,
    "allowedDepartments": ["dept-1", "dept-2"]
  }
}
```

#### Field Modification Rule Configuration
```csharp
public class FieldModificationConfiguration
{
    public List<FieldModification> Modifications { get; set; } = new();
}

public class FieldModification
{
    public string FieldName { get; set; } = string.Empty;
    public string Value { get; set; } = string.Empty;
    public ModificationType Type { get; set; }
}

public enum ModificationType
{
    SetValue = 1,
    SetCurrentDateTime = 2,
    SetCurrentUser = 3,
    SetCalculatedValue = 4
}

// JSON Örneği:
{
  "modifications": [
    {
      "fieldName": "Status",
      "value": "InProgress",
      "type": "SetValue"
    },
    {
      "fieldName": "StartDate",
      "value": "{{NOW}}",
      "type": "SetCurrentDateTime"
    },
    {
      "fieldName": "AssigneeId",
      "value": "{{CURRENT_USER_ID}}",
      "type": "SetCurrentUser"
    }
  ]
}
```

#### Notification Rule Configuration
```csharp
public class NotificationConfiguration
{
    public List<NotificationDefinition> Notifications { get; set; } = new();
}

public class NotificationDefinition
{
    public NotificationType Type { get; set; }
    public RecipientDefinition Recipients { get; set; } = new();
    public string Template { get; set; } = string.Empty;
    public string Subject { get; set; } = string.Empty;
    public string? Condition { get; set; }
    public int DelaySeconds { get; set; } = 0;
}

// JSON Örneği:
{
  "notifications": [
    {
      "type": "Email",
      "recipients": {
        "type": "Field",
        "fieldName": "AssigneeId"
      },
      "template": "TicketAssigned",
      "subject": "Yeni Görev Atandı: {{TicketTitle}}",
      "delay": 0
    },
    {
      "type": "SMS",
      "recipients": {
        "type": "Role",
        "roles": ["Manager"]
      },
      "template": "HighPriorityTicket",
      "condition": "Priority == 'High'",
      "delay": 300
    }
  ]
}
```

### Specific Rule Handler Implementations

#### AuthorizationRuleHandler
```csharp
public class AuthorizationRuleHandler(
    IUserService userService,
    ILogger<AuthorizationRuleHandler> logger) : BaseRuleHandler
{
    public override RuleType RuleType => RuleType.Authorization;

    public override async Task<RuleResult> ExecuteAsync(
        TransitionRule rule,
        TransitionContext context,
        CancellationToken cancellationToken)
    {
        var config = GetConfiguration<AuthorizationConfiguration>(rule);

        // Role bazlı kontrol
        if (config.AllowedRoles?.Any() == true)
        {
            var userRoles = await userService.GetUserRolesAsync(context.UserId);
            if (!userRoles.Any(r => config.AllowedRoles.Contains(r)))
            {
                return RuleResult.Failure("Bu geçiş için yetkiniz bulunmamaktadır", isBlocking: true);
            }
        }

        // Kullanıcı bazlı kontrol
        if (config.AllowedUsers?.Any() == true)
        {
            if (!config.AllowedUsers.Contains(context.UserId.ToString()))
            {
                return RuleResult.Failure("Bu geçişi yapma yetkiniz bulunmamaktadır", isBlocking: true);
            }
        }

        // Department kısıtlaması
        if (config.DepartmentRestriction != null)
        {
            var isAuthorized = await ValidateDepartmentRestrictionAsync(config.DepartmentRestriction, context);
            if (!isAuthorized)
            {
                return RuleResult.Failure("Departman kısıtlaması nedeniyle geçiş yapılamaz", isBlocking: true);
            }
        }

        return RuleResult.Success();
    }
}
```

### Updated Command Implementation

#### ExecuteTransitionCommand
```csharp
public record ExecuteTransitionCommand(
    Guid TicketId,
    Guid TransitionId,
    Dictionary<string, object>? AdditionalData = null
) : IRequest<Result<TransitionExecutionResult>>;

public class ExecuteTransitionCommandHandler(
    RequestsDbContext context,
    IRuleEngine ruleEngine,
    IMediator mediator,
    ITicketService ticketService,
    IUserService userService) : IRequestHandler<ExecuteTransitionCommand, Result<TransitionExecutionResult>>
{
    public async Task<Result<TransitionExecutionResult>> Handle(
        ExecuteTransitionCommand request,
        CancellationToken cancellationToken)
    {
        // 1. Transition bilgilerini al
        var transition = await context.Transitions
            .Include(t => t.FromNode)
            .Include(t => t.ToNode)
            .Include(t => t.Rules.Where(r => r.IsActive))
            .FirstOrDefaultAsync(t => t.Id == request.TransitionId, cancellationToken);

        if (transition == null)
            return Result.Failure<TransitionExecutionResult>("Transition bulunamadı");

        // 2. Rich Context oluştur
        var transitionContext = await CreateTransitionContextAsync(
            request.TicketId,
            transition,
            request.AdditionalData,
            cancellationToken);

        // 3. Rule Engine ile kuralları çalıştır
        var ruleResult = await ruleEngine.ExecuteRulesAsync(
            request.TransitionId,
            transitionContext,
            cancellationToken);

        if (!ruleResult.IsSuccess)
        {
            return Result.Failure<TransitionExecutionResult>(
                string.Join(", ", ruleResult.Errors));
        }

        // 4. Ticket'i güncelle
        await ticketService.UpdateTicketAsync(
            request.TicketId,
            transition.ToNode,
            ruleResult.ModifiedFields,
            cancellationToken);

        // 5. Bildirimleri gönder
        foreach (var notification in ruleResult.PendingNotifications)
        {
            await mediator.Send(new SendNotificationCommand(notification), cancellationToken);
        }

        return Result.Success(new TransitionExecutionResult
        {
            TransitionId = request.TransitionId,
            NewNodeId = transition.ToNodeId,
            ModifiedFields = ruleResult.ModifiedFields,
            NotificationsSent = ruleResult.PendingNotifications.Count,
            ExecutedRules = ruleResult.ExecutedRuleCount
        });
    }

    private async Task<TransitionContext> CreateTransitionContextAsync(
        Guid ticketId,
        Transition transition,
        Dictionary<string, object>? additionalData,
        CancellationToken cancellationToken)
    {
        var ticket = await ticketService.GetTicketDataAsync(ticketId, cancellationToken);
        var user = await userService.GetUserDataAsync(GetCurrentUserId(), cancellationToken);

        return new TransitionContext
        {
            TicketId = ticketId,
            UserId = GetCurrentUserId(),
            OrganizationId = GetCurrentOrganizationId(),
            TicketData = ticket.ToDictionary(),
            UserData = user.ToDictionary(),
            Metadata = additionalData ?? new(),
            FromNode = transition.FromNode,
            ToNode = transition.ToNode,
            TransitionTime = DateTime.UtcNow
        };
    }
}
```

### Klasör Yapısı

```
src/Modules/Requests/
├── Application/
│   ├── Rules/
│   │   ├── Engine/
│   │   │   ├── IRuleEngine.cs
│   │   │   ├── RuleEngine.cs
│   │   │   └── TransitionContext.cs
│   │   ├── Handlers/
│   │   │   ├── BaseRuleHandler.cs
│   │   │   ├── AuthorizationRuleHandler.cs
│   │   │   ├── FieldValidationRuleHandler.cs
│   │   │   ├── FieldModificationRuleHandler.cs
│   │   │   └── NotificationRuleHandler.cs
│   │   ├── Configurations/
│   │   │   ├── AuthorizationConfiguration.cs
│   │   │   ├── FieldValidationConfiguration.cs
│   │   │   ├── FieldModificationConfiguration.cs
│   │   │   └── NotificationConfiguration.cs
│   │   ├── CreateRule/
│   │   ├── UpdateRule/
│   │   ├── DeleteRule/
│   │   ├── GetRule/
│   │   └── ListRules/
│   ├── Transitions/
│   │   ├── ExecuteTransition/
│   │   └── ValidateTransition/
│   └── Dtos/
│       ├── TransitionRuleDto.cs
│       └── RuleExecutionDto.cs
├── Domain/
│   ├── TransitionRule.cs
│   ├── RuleExecution.cs
│   └── Enums/
│       ├── RuleType.cs
│       └── RuleExecutionStatus.cs
└── Infrastructure/
    ├── Data/Configurations/
    │   ├── TransitionRuleConfiguration.cs
    │   └── RuleExecutionConfiguration.cs
    └── Rules/
        └── RuleHandlerRegistry.cs
```

### API Endpoints

```
# Rule Management
GET    /api/v1/requests/transitions/{transitionId}/rules
POST   /api/v1/requests/transitions/{transitionId}/rules
GET    /api/v1/requests/rules/{ruleId}
PUT    /api/v1/requests/rules/{ruleId}
DELETE /api/v1/requests/rules/{ruleId}

# Transition Execution
POST   /api/v1/requests/tickets/{ticketId}/transitions/{transitionId}/execute
POST   /api/v1/requests/tickets/{ticketId}/transitions/{transitionId}/validate
GET    /api/v1/requests/tickets/{ticketId}/available-transitions

# Rule Execution Logs
GET    /api/v1/requests/rules/{ruleId}/executions
GET    /api/v1/requests/tickets/{ticketId}/rule-executions
```

## Uygulama Stratejisi

### Faz 1: Temel Altyapı (1.5 hafta)
- TransitionRule ve RuleExecution entity'lerinin oluşturulması
- Rule Engine interface ve base implementation
- Temel CRUD operasyonları
- Database migration'lar

### Faz 2: Rule Handlers (1.5 hafta)
- Authorization Rule Handler implementasyonu
- Field Validation Rule Handler implementasyonu
- Field Modification Rule Handler implementasyonu
- JSON configuration validation

### Faz 3: Notification ve Logging (1 hafta)
- Notification Rule Handler implementasyonu
- Comprehensive logging sistemi
- Rule execution tracking
- ExecuteTransition command güncellemesi

### Faz 4: Test ve Optimizasyon (0.5 hafta)
- Unit test'lerin yazılması
- Integration test'lerin hazırlanması
- Performans optimizasyonları

## Riskler ve Önlemler

### Performans Riskleri
- **Risk**: Çok sayıda kuralın sırayla çalıştırılması gecikmeye neden olabilir
- **Önlem**: Async execution, rule caching, timeout mekanizması

### Güvenlik Riskleri
- **Risk**: JSON configuration'a zararlı veri enjeksiyonu
- **Önlem**: Configuration validation, input sanitization, schema validation

### Veri Tutarlılığı Riskleri
- **Risk**: Rule execution sırasında hata durumunda ticket inconsistent state'de kalabilir
- **Önlem**: Transaction management, rollback mekanizması

## Başarı Metrikleri
- Rule execution süresi < 200ms (kural başına)
- Rule configuration validation süresi < 50ms
- System availability > 99.9% (rule engine hatalarına rağmen)
- Rule execution success rate > 95%
- Comprehensive logging coverage = 100%

## Bağımlılıklar
- RFC 002 - Workflow Node Management (temel altyapı)
- MediatR pipeline
- JSON Schema validation library
- Notification service
- User service ve permission system