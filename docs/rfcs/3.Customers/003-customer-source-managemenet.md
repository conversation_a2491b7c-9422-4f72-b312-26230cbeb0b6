# RFC 003: Customer Source Management

## Özet

Bu RFC, MasterCRM sisteminde müşteri kaynaklarının (CustomerSource) yönetimi için gereken backend bileşenlerini ve CRUD işlemlerini tanımlar. CustomerSource, müşterilerin sisteme hangi kaynaktan (örn: web sitesi, sosyal medya, referans) geldiğini izlemek için kullanılan temel bir veri yapısıdır.

## Motivasyon

Müşteri edinim kaynaklarının takibi, pazarlama stratejilerinin etkinliğini değerlendirmek ve müşteri segmentasyonu için kritik öneme sahiptir. Bu modül:

- Müşteri edinim kaynaklarının merkezi yönetimini sağlar
- Müşteri verilerinin kaynak bazlı analiz edilmesine olanak tanır
- Pazarlama stratejilerinin etkinliğinin ölçülmesine yardımcı olur
- Müşteri yolculuğunun daha iyi anlaşılmasını sağlar

## Teknik Tasarım

### Entity Modeli

```csharp
namespace MasterCRM.Modules.Customers.Domain.Entities
{
    public class CustomerSource : BaseEntity
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
    }
}
```

### Database Schema

```sql
CREATE TABLE Customers.CustomerSource (
    Id UNIQUEIDENTIFIER PRIMARY KEY,
    InsertDate DATETIME2 NOT NULL,
    UpdateDate DATETIME2,
    InsertUserId UNIQUEIDENTIFIER,
    UpdateUserId UNIQUEIDENTIFIER,
    History NVARCHAR(MAX),
    Name NVARCHAR(100) NOT NULL
);
```

### CQRS Implementation

Vertical Slice Architecture ve CQRS pattern'e uygun olarak aşağıdaki feature'lar oluşturulacaktır:

#### Feature: CreateCustomerSource

**Command:**

```csharp
namespace Customers.Application.CustomerSources.CreateCustomerSource
{
    public class CreateCustomerSourceCommand : IRequest<Result<Guid>>
    {
        public string Name { get; set; }
    }
}
```

**Command Handler:**

```csharp
namespace Customers.Application.CustomerSources.CreateCustomerSource
{
    public class CreateCustomerSourceCommandHandler : IRequestHandler<CreateCustomerSourceCommand, Result<Guid>>
    {
        private readonly ICustomersDbContext _context;
        private readonly ICurrentUserService _currentUserService;

        public CreateCustomerSourceCommandHandler(ICustomersDbContext context, ICurrentUserService currentUserService)
        {
            _context = context;
            _currentUserService = currentUserService;
        }

        public async Task<Result<Guid>> Handle(CreateCustomerSourceCommand request, CancellationToken cancellationToken)
        {
            var customerSource = new CustomerSource
            {
                Id = Guid.NewGuid(),
                Name = request.Name,
                InsertDate = DateTime.Now,
                InsertUserId = _currentUserService.UserId
            };

            _context.CustomerSource.Add(customerSource);
            await _context.SaveChangesAsync(cancellationToken);

            return Result.Success(customerSource.Id);
        }
    }
}
```

**Validator:**

```csharp
namespace Customers.Application.CustomerSources.CreateCustomerSource
{
    public class CreateCustomerSourceCommandValidator : AbstractValidator<CreateCustomerSourceCommand>
    {
        private readonly ICustomersDbContext _context;

        public CreateCustomerSourceCommandValidator(ICustomersDbContext context)
        {
            _context = context;

            RuleFor(x => x.Name)
                .NotEmpty().WithMessage("Müşteri kaynağı adı boş olamaz.")
                .MaximumLength(100).WithMessage("Müşteri kaynağı adı 100 karakterden uzun olamaz.")
                .MustAsync(BeUniqueName).WithMessage("Bu isimde bir müşteri kaynağı zaten mevcut.");
        }

        private async Task<bool> BeUniqueName(string name, CancellationToken cancellationToken)
        {
            return !await _context.CustomerSource
                .AnyAsync(x => x.Name == name, cancellationToken);
        }
    }
}
```

**Endpoint:**

```csharp
namespace Customers.Application.CustomerSources.CreateCustomerSource
{
    internal sealed class CreateCustomerSourceEndpoint : IEndpoint
    {
        public void MapEndpoint(IEndpointRouteBuilder app)
        {
            app.MapPost("/api/v1/customers/sources", async (
                CreateCustomerSourceCommand command,
                IMediator mediator,
                CancellationToken cancellationToken) =>
            {
                var result = await mediator.Send(command, cancellationToken);
                return result.Match(Results.Ok, CustomResults.Problem);
            })
            .WithTags("Customer Sources")
            .WithGroupName("apiv1")
            .RequireAuthorization("Customers.Management");
        }
    }
}
```

#### Feature: UpdateCustomerSource

**Command:**

```csharp
namespace Customers.Application.CustomerSources.UpdateCustomerSource
{
    public class UpdateCustomerSourceCommand : IRequest<Result>
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
    }
}
```

**Command Handler:**

```csharp
namespace Customers.Application.CustomerSources.UpdateCustomerSource
{
    public class UpdateCustomerSourceCommandHandler : IRequestHandler<UpdateCustomerSourceCommand, Result>
    {
        private readonly ICustomersDbContext _context;
        private readonly ICurrentUserService _currentUserService;

        public UpdateCustomerSourceCommandHandler(ICustomersDbContext context, ICurrentUserService currentUserService)
        {
            _context = context;
            _currentUserService = currentUserService;
        }

        public async Task<Result> Handle(UpdateCustomerSourceCommand request, CancellationToken cancellationToken)
        {
            var customerSource = await _context.CustomerSource
                .FindAsync(new object[] { request.Id }, cancellationToken);

            if (customerSource == null)
                return Result.Failure("Müşteri kaynağı bulunamadı.");

            // Historisini tutar
            customerSource.History = UpdateHistory(customerSource);

            customerSource.Name = request.Name;
            customerSource.UpdateDate = DateTime.Now;
            customerSource.UpdateUserId = _currentUserService.UserId;

            await _context.SaveChangesAsync(cancellationToken);

            return Result.Success();
        }

        private string UpdateHistory(CustomerSource customerSource)
        {
            var history = string.IsNullOrEmpty(customerSource.History)
                ? new List<HistoryRecord>()
                : JsonSerializer.Deserialize<List<HistoryRecord>>(customerSource.History);

            history.Add(new HistoryRecord
            {
                Date = DateTime.Now,
                UserId = _currentUserService.UserId,
                PreviousValue = JsonSerializer.Serialize(customerSource)
            });

            return JsonSerializer.Serialize(history);
        }
    }
}
```

**Validator:**

```csharp
namespace Customers.Application.CustomerSources.UpdateCustomerSource
{
    public class UpdateCustomerSourceCommandValidator : AbstractValidator<UpdateCustomerSourceCommand>
    {
        private readonly ICustomersDbContext _context;

        public UpdateCustomerSourceCommandValidator(ICustomersDbContext context)
        {
            _context = context;

            RuleFor(x => x.Id)
                .NotEmpty().WithMessage("Müşteri kaynağı ID'si boş olamaz.");

            RuleFor(x => x.Name)
                .NotEmpty().WithMessage("Müşteri kaynağı adı boş olamaz.")
                .MaximumLength(100).WithMessage("Müşteri kaynağı adı 100 karakterden uzun olamaz.")
                .MustAsync(BeUniqueName).WithMessage("Bu isimde bir müşteri kaynağı zaten mevcut.");
        }

        private async Task<bool> BeUniqueName(UpdateCustomerSourceCommand command, string name, CancellationToken cancellationToken)
        {
            return !await _context.CustomerSource
                .AnyAsync(x => x.Name == name && x.Id != command.Id, cancellationToken);
        }
    }
}
```

**Endpoint:**

```csharp
namespace Customers.Application.CustomerSources.UpdateCustomerSource
{
    internal sealed class UpdateCustomerSourceEndpoint : IEndpoint
    {
        public void MapEndpoint(IEndpointRouteBuilder app)
        {
            app.MapPut("/api/v1/customers/sources/{id}", async (
                Guid id,
                UpdateCustomerSourceCommand command,
                IMediator mediator,
                CancellationToken cancellationToken) =>
            {
                // ID'yi command'dan gelen ile birleştir
                command.Id = id;

                var result = await mediator.Send(command, cancellationToken);
                return result.Match(Results.NoContent, CustomResults.Problem);
            })
            .WithTags("Customer Sources")
            .WithGroupName("apiv1")
            .RequireAuthorization("Customers.Management");
        }
    }
}
```

#### Feature: DeleteCustomerSource

**Command:**

```csharp
namespace Customers.Application.CustomerSources.DeleteCustomerSource
{
    public class DeleteCustomerSourceCommand : IRequest<Result>
    {
        public Guid Id { get; set; }
    }
}
```

**Command Handler:**

```csharp
namespace Customers.Application.CustomerSources.DeleteCustomerSource
{
    public class DeleteCustomerSourceCommandHandler : IRequestHandler<DeleteCustomerSourceCommand, Result>
    {
        private readonly ICustomersDbContext _context;

        public DeleteCustomerSourceCommandHandler(ICustomersDbContext context)
        {
            _context = context;
        }

        public async Task<Result> Handle(DeleteCustomerSourceCommand request, CancellationToken cancellationToken)
        {
            // İlişkili müşteri var mı kontrol et
            var hasRelatedCustomers = await _context.Customers
                .AnyAsync(x => x.SourceId == request.Id, cancellationToken);

            if (hasRelatedCustomers)
                return Result.Failure("Bu müşteri kaynağı kullanımda olduğu için silinemez.");

            var customerSource = await _context.CustomerSource
                .FindAsync(new object[] { request.Id }, cancellationToken);

            if (customerSource == null)
                return Result.Failure("Müşteri kaynağı bulunamadı.");

            _context.CustomerSource.Remove(customerSource);
            await _context.SaveChangesAsync(cancellationToken);

            return Result.Success();
        }
    }
}
```

**Validator:**

```csharp
namespace Customers.Application.CustomerSources.DeleteCustomerSource
{
    public class DeleteCustomerSourceCommandValidator : AbstractValidator<DeleteCustomerSourceCommand>
    {
        public DeleteCustomerSourceCommandValidator()
        {
            RuleFor(x => x.Id)
                .NotEmpty().WithMessage("Müşteri kaynağı ID'si boş olamaz.");
        }
    }
}
```

**Endpoint:**

```csharp
namespace Customers.Application.CustomerSources.DeleteCustomerSource
{
    internal sealed class DeleteCustomerSourceEndpoint : IEndpoint
    {
        public void MapEndpoint(IEndpointRouteBuilder app)
        {
            app.MapDelete("/api/v1/customers/sources/{id}", async (
                Guid id,
                IMediator mediator,
                CancellationToken cancellationToken) =>
            {
                var command = new DeleteCustomerSourceCommand(id);
                var result = await mediator.Send(command, cancellationToken);
                return result.Match(Results.NoContent, CustomResults.Problem);
            })
            .WithTags("Customer Sources")
            .WithGroupName("apiv1")
            .RequireAuthorization("Customers.Management");
        }
    }
}
```

#### Feature: GetCustomerSource

**Query:**

```csharp
namespace Customers.Application.CustomerSources.GetCustomerSource
{
    public record GetCustomerSourceQuery(Guid Id) : IRequest<Result<CustomerSourceDto>>;
}
```

**Query Handler:**

```csharp
namespace Customers.Application.CustomerSources.GetCustomerSource
{
    public class GetCustomerSourceQueryHandler : IRequestHandler<GetCustomerSourceQuery, Result<CustomerSourceDto>>
    {
        private readonly ICustomersDbContext _context;
        private readonly IMapper _mapper;

        public GetCustomerSourceQueryHandler(ICustomersDbContext context, IMapper mapper)
        {
            _context = context;
            _mapper = mapper;
        }

        public async Task<Result<CustomerSourceDto>> Handle(GetCustomerSourceQuery request, CancellationToken cancellationToken)
        {
            var customerSource = await _context.CustomerSource
                .FindAsync(new object[] { request.Id }, cancellationToken);

            if (customerSource == null)
                return Result.Failure<CustomerSourceDto>("Müşteri kaynağı bulunamadı.");

            return Result.Success(_mapper.Map<CustomerSourceDto>(customerSource));
        }
    }
}
```

**Dto:**

```csharp
namespace Customers.Application.CustomerSources.GetCustomerSource
{
    public class CustomerSourceDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
        public DateTime InsertDate { get; set; }
        public DateTime? UpdateDate { get; set; }
    }
}
```

**Endpoint:**

```csharp
namespace Customers.Application.CustomerSources.GetCustomerSource
{
    internal sealed class GetCustomerSourceEndpoint : IEndpoint
    {
        public void MapEndpoint(IEndpointRouteBuilder app)
        {
            app.MapGet("/api/v1/customers/sources/{id}", async (
                Guid id,
                IMediator mediator,
                CancellationToken cancellationToken) =>
            {
                var query = new GetCustomerSourceQuery(id);
                var result = await mediator.Send(query, cancellationToken);
                return result.Match(Results.Ok, error => Results.NotFound(error));
            })
            .WithTags("Customer Sources")
            .WithGroupName("apiv1")
            .RequireAuthorization("Customers.Management");
        }
    }
}
```

#### Feature: GetCustomerSources

**Query:**

```csharp
namespace Customers.Application.CustomerSources.GetCustomerSources
{
    public class GetCustomerSourcesQuery : IRequest<Result<List<CustomerSourceDto>>>
    {
        public string SearchTerm { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 10;
    }
}
```

**Query Handler:**

```csharp
namespace Customers.Application.CustomerSources.GetCustomerSources
{
    public class GetCustomerSourcesQueryHandler : IRequestHandler<GetCustomerSourcesQuery, Result<List<CustomerSourceDto>>>
    {
        private readonly ICustomersDbContext _context;
        private readonly IMapper _mapper;

        public GetCustomerSourcesQueryHandler(ICustomersDbContext context, IMapper mapper)
        {
            _context = context;
            _mapper = mapper;
        }

        public async Task<Result<List<CustomerSourceDto>>> Handle(GetCustomerSourcesQuery request, CancellationToken cancellationToken)
        {
            var query = _context.CustomerSource.AsQueryable();

            if (!string.IsNullOrWhiteSpace(request.SearchTerm))
                query = query.Where(x => x.Name.Contains(request.SearchTerm));

            query = query.OrderBy(x => x.Name);

            var customerSources = await query
                .Skip((request.PageNumber - 1) * request.PageSize)
                .Take(request.PageSize)
                .ToListAsync(cancellationToken);

            return Result.Success(_mapper.Map<List<CustomerSourceDto>>(customerSources));
        }
    }
}
```

**Dto:**

```csharp
namespace Customers.Application.CustomerSources.GetCustomerSources
{
    public class CustomerSourceDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
    }
}
```

**Endpoint:**

```csharp
namespace Customers.Application.CustomerSources.GetCustomerSources
{
    internal sealed class GetCustomerSourcesEndpoint : IEndpoint
    {
        public void MapEndpoint(IEndpointRouteBuilder app)
        {
            app.MapGet("/api/v1/customers/sources", async (
                [AsParameters] GetCustomerSourcesQuery query,
                IMediator mediator,
                CancellationToken cancellationToken) =>
            {
                var result = await mediator.Send(query, cancellationToken);
                return result.Match(Results.Ok, CustomResults.Problem);
            })
            .WithTags("Customer Sources")
            .WithGroupName("apiv1")
            .RequireAuthorization("Customers.Management");
        }
    }
}
```

### DbContext ve Repository Implementation

```csharp
namespace MasterCRM.Modules.Customers.Infrastructure.Data
{
    public interface ICustomersDbContext
    {
        DbSet<CustomerSource> CustomerSources { get; set; }
        DbSet<Customer> Customers { get; set; }

        Task<int> SaveChangesAsync(CancellationToken cancellationToken);
    }

    public class CustomerDbContext : DbContext, ICustomersDbContext
    {
        public CustomerDbContext(DbContextOptions<CustomerDbContext> options) : base(options)
        {
        }

        public DbSet<CustomerSource> CustomerSources { get; set; }
        public DbSet<Customer> Customers { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<CustomerSource>(entity =>
            {
                entity.ToTable("CustomerSource", "Customers");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).HasMaxLength(100).IsRequired();
            });

            base.OnModelCreating(modelBuilder);
        }
    }
}
```

## Test Stratejisi

### Unit Tests

```csharp
namespace MasterCRM.Modules.Customers.Tests.UnitTests.CustomerSources
{
    public class CreateCustomerSourceCommandHandlerTests
    {
        [Fact]
        public async Task Handle_ValidRequest_ShouldCreateCustomerSource()
        {
            // Arrange
            var dbContextMock = new Mock<ICustomersDbContext>();
            var currentUserServiceMock = new Mock<ICurrentUserService>();
            var dbSetMock = new Mock<DbSet<CustomerSource>>();

            dbContextMock.Setup(x => x.CustomerSources).Returns(dbSetMock.Object);
            currentUserServiceMock.Setup(x => x.UserId).Returns(Guid.NewGuid());

            var handler = new CreateCustomerSourceCommandHandler(dbContextMock.Object, currentUserServiceMock.Object);
            var command = new CreateCustomerSourceCommand { Name = "Web Site" };

            // Act
            var result = await handler.Handle(command, CancellationToken.None);

            // Assert
            result.IsSuccess.Should().BeTrue();
            result.Value.Should().NotBeEmpty();
            dbSetMock.Verify(x => x.Add(It.IsAny<CustomerSource>()), Times.Once);
            dbContextMock.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Once);
        }
    }

    // Diğer test metodları benzer şekilde yazılır...
}
```

### Integration Tests

```csharp
namespace MasterCRM.Modules.Customers.Tests.IntegrationTests.CustomerSources
{
    public class CustomerSourcesControllerTests : IClassFixture<WebApplicationFactory<Program>>
    {
        private readonly WebApplicationFactory<Program> _factory;

        public CustomerSourcesControllerTests(WebApplicationFactory<Program> factory)
        {
            _factory = factory;
        }

        [Fact]
        public async Task CreateCustomerSource_ValidRequest_ReturnsCreatedId()
        {
            // Arrange
            var client = _factory.CreateClient();
            var command = new CreateCustomerSourceCommand { Name = "Instagram" };

            // Act
            var response = await client.PostAsJsonAsync("/api/v1/customers/sources", command);

            // Assert
            response.EnsureSuccessStatusCode();
            var id = await response.Content.ReadFromJsonAsync<Guid>();
            id.Should().NotBeEmpty();
        }

        // Diğer test metodları benzer şekilde yazılır...
    }
}
```

## Endpoint Registrasyonu

CustomerSource modülünün Minimal API endpoint'lerini sisteme kaydetmek için aşağıdaki extension metodu kullanılacaktır:

```csharp
namespace MasterCRM.Modules.Customers.Infrastructure
{
    public static class CustomerSourceEndpointExtensions
    {
        public static WebApplication MapCustomerSourceEndpoints(this WebApplication app)
        {
            var endpoints = app.Services.GetServices<IEndpoint>()
                .Where(e => e.GetType().Namespace?
                    .StartsWith("MasterCRM.Modules.Customers.Application.CustomerSources") == true);

            foreach (var endpoint in endpoints)
            {
                endpoint.MapEndpoint(app);
            }

            return app;
        }
    }
}
```

Program.cs içerisinde endpoint'lerin kaydedilmesi:

```csharp
app.MapCustomerSourceEndpoints();
```

## Uygulama Planı

Bu modül, MasterCRM sisteminin "Faz 3: Müşteri Yönetimi" aşamasında, aşağıdaki sırayla geliştirilebilir:

1. Entity ve DbContext implementasyonu
2. CQRS sınıflarının oluşturulması
3. Minimal API Endpoint'lerinin implementasyonu
4. Endpoint kayıt sisteminin kurulması
5. Unit ve Integration testlerinin yazılması
6. Dokümantasyon

Tahmini geliştirme süresi: 2-3 gün

## Bağımlılıklar

- Users modülünün tamamlanmış olması (yetkilendirme için)
- Core altyapının hazır olması (Result pattern, Validation, vb.)

## Riskler ve Çözümler

- Veri tutarlılığı: CASCADE olmayan DELETE işlemleri için CustomerSource'un kullanımda olup olmadığının kontrolü
- Performans: Paging ve filtreleme implementasyonu ile büyük veri setlerinin yönetimi

## Sonuç

Bu RFC, MasterCRM sisteminde müşteri kaynaklarının yönetimi için gerekli backend CRUD işlemlerini tanımlamaktadır. Modüler monolith mimarisi ve CQRS pattern'i kullanılarak, müşteri kaynaklarının etkin bir şekilde yönetilmesini sağlayacak API'ler oluşturulacaktır.