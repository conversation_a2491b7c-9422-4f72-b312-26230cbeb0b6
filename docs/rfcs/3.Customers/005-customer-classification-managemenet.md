# RFC 003: Customer Classification Management

## Status
Tammalandı

## Özet

Bu RFC, MasterCRM sisteminde müşteri sınıflandırma (Classification) yönetimi için gereken backend bileşenlerini ve CRUD işlemlerini tanımlar. Classification, müşterilerin sınıflandırılması için kullanılır.

## Motivasyon

Müşteri sınıflandırılmasının takibi, pazarlama stratejilerinin etkinliğini değerlendirmek ve müşteri segmentasyonu için kritik öneme sahiptir. Bu modül:

- Müşteri sınıflandırılmasının merkezi yönetimini sağlar
- Müşterilerin sınıf bazlı analiz edilmesine olanak tanır
- Pazarlama stratejilerinin etkinliğinin ölçülmesine yardımcı olur
- Müşteri yolculuğunun daha iyi anlaşılmasını sağlar

## Teknik Tasarım

### Entity Modeli

```csharp
namespace MasterCRM.Modules.Customers.Domain.Entities
{
    public class Classification : BaseEntity
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
    }
}
```

### Database Schema

```sql
CREATE TABLE Customers.Classification (
    Id UNIQUEIDENTIFIER PRIMARY KEY,
    Name NVARCHAR(100) NOT NULL
);
```

### CQRS Implementation

Vertical Slice Architecture ve CQRS pattern'e uygun olarak aşağıdaki feature'lar oluşturulacaktır:

#### Feature: CreateClassification

**Command:**

```csharp
namespace Customers.Application.Classifications.CreateClassification
{
    public class CreateClassificationCommand : IRequest<Result<Guid>>
    {
        public string Name { get; set; }
    }
}
```

**Command Handler:**

```csharp
namespace Customers.Application.Classifications.CreateClassification
{
    public class CreateClassificationCommandHandler : IRequestHandler<CreateClassificationCommand, Result<Guid>>
    {
        private readonly ICustomersDbContext _context;

        public CreateClassificationCommandHandler(ICustomersDbContext context)
        {
            _context = context;
        }

        public async Task<Result<Guid>> Handle(CreateClassificationCommand request, CancellationToken cancellationToken)
        {
            var Classification = new Classification
            {
                Id = Guid.NewGuid(),
                Name = request.Name
            };

            _context.Classification.Add(Classification);
            await _context.SaveChangesAsync(cancellationToken);

            return Result.Success(Classification.Id);
        }
    }
}
```

**Validator:**

```csharp
namespace Customers.Application.Classifications.CreateClassification
{
    public class CreateClassificationCommandValidator : AbstractValidator<CreateClassificationCommand>
    {
        private readonly ICustomersDbContext _context;

        public CreateClassificationCommandValidator(ICustomersDbContext context)
        {
            _context = context;

            RuleFor(x => x.Name)
                .NotEmpty().WithMessage("Müşteri kaynağı adı boş olamaz.")
                .MaximumLength(100).WithMessage("Müşteri kaynağı adı 100 karakterden uzun olamaz.")
                .MustAsync(BeUniqueName).WithMessage("Bu isimde bir müşteri kaynağı zaten mevcut.");
        }

        private async Task<bool> BeUniqueName(string name, CancellationToken cancellationToken)
        {
            return !await _context.Classification
                .AnyAsync(x => x.Name == name, cancellationToken);
        }
    }
}
```

**Endpoint:**

```csharp
namespace Customers.Application.Classifications.CreateClassification
{
    internal sealed class CreateClassificationEndpoint : IEndpoint
    {
        public void MapEndpoint(IEndpointRouteBuilder app)
        {
            app.MapPost("/api/v1/customers/classifications", async (
                CreateClassificationCommand command,
                IMediator mediator,
                CancellationToken cancellationToken) =>
            {
                var result = await mediator.Send(command, cancellationToken);
                return result.Match(Results.Ok, CustomResults.Problem);
            })
            .WithTags("Customer Classifications")
            .WithGroupName("apiv1")
            .RequireAuthorization("Customers.Management");
        }
    }
}
```

#### Feature: UpdateClassification

**Command:**

```csharp
namespace Customers.Application.Classifications.UpdateClassification
{
    public class UpdateClassificationCommand : IRequest<Result>
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
    }
}
```

**Command Handler:**

```csharp
namespace Customers.Application.Classifications.UpdateClassification
{
    public class UpdateClassificationCommandHandler : IRequestHandler<UpdateClassificationCommand, Result>
    {
        private readonly ICustomersDbContext _context;
        private readonly IWorkContext _workcontext;

        public UpdateClassificationCommandHandler(ICustomersDbContext context, IWorkContext workcontext)
        {
            _context = context;
            _workcontext = workcontext;
        }

        public async Task<Result> Handle(UpdateClassificationCommand request, CancellationToken cancellationToken)
        {
            var Classification = await _context.Classification
                .FindAsync(new object[] { request.Id }, cancellationToken);

            if (Classification == null)
                return Result.Failure("Müşteri kaynağı bulunamadı.");

            // Historisini tutar
            Classification.History = UpdateHistory(Classification);

            Classification.Name = request.Name;
            Classification.UpdateDate = DateTime.Now;
            Classification.UpdateUserId = _workcontext.UserId;

            await _context.SaveChangesAsync(cancellationToken);

            return Result.Success();
        }

        private string UpdateHistory(Classification Classification)
        {
            var history = string.IsNullOrEmpty(Classification.History)
                ? new List<HistoryRecord>()
                : JsonSerializer.Deserialize<List<HistoryRecord>>(Classification.History);

            history.Add(new HistoryRecord
            {
                Date = DateTime.Now,
                UserId = _workcontext.UserId,
                PreviousValue = JsonSerializer.Serialize(Classification)
            });

            return JsonSerializer.Serialize(history);
        }
    }
}
```

**Validator:**

```csharp
namespace Customers.Application.Classifications.UpdateClassification
{
    public class UpdateClassificationCommandValidator : AbstractValidator<UpdateClassificationCommand>
    {
        private readonly ICustomersDbContext _context;

        public UpdateClassificationCommandValidator(ICustomersDbContext context)
        {
            _context = context;

            RuleFor(x => x.Id)
                .NotEmpty().WithMessage("Müşteri kaynağı ID'si boş olamaz.");

            RuleFor(x => x.Name)
                .NotEmpty().WithMessage("Müşteri kaynağı adı boş olamaz.")
                .MaximumLength(100).WithMessage("Müşteri kaynağı adı 100 karakterden uzun olamaz.")
                .MustAsync(BeUniqueName).WithMessage("Bu isimde bir müşteri kaynağı zaten mevcut.");
        }

        private async Task<bool> BeUniqueName(UpdateClassificationCommand command, string name, CancellationToken cancellationToken)
        {
            return !await _context.Classification
                .AnyAsync(x => x.Name == name && x.Id != command.Id, cancellationToken);
        }
    }
}
```

**Endpoint:**

```csharp
namespace Customers.Application.Classifications.UpdateClassification
{
    internal sealed class UpdateClassificationEndpoint : IEndpoint
    {
        public void MapEndpoint(IEndpointRouteBuilder app)
        {
            app.MapPut("/api/v1/customers/classifications/{id}", async (
                Guid id,
                UpdateClassificationCommand command,
                IMediator mediator,
                CancellationToken cancellationToken) =>
            {
                // ID'yi command'dan gelen ile birleştir
                command.Id = id;

                var result = await mediator.Send(command, cancellationToken);
                return result.Match(Results.NoContent, CustomResults.Problem);
            })
            .WithTags("Customer Classifications")
            .WithGroupName("apiv1")
            .RequireAuthorization("Customers.Management");
        }
    }
}
```

#### Feature: DeleteClassification

**Command:**

```csharp
namespace Customers.Application.Classifications.DeleteClassification
{
    public class DeleteClassificationCommand : IRequest<Result>
    {
        public Guid Id { get; set; }
    }
}
```

**Command Handler:**

```csharp
namespace Customers.Application.Classifications.DeleteClassification
{
    public class DeleteClassificationCommandHandler : IRequestHandler<DeleteClassificationCommand, Result>
    {
        private readonly ICustomersDbContext _context;

        public DeleteClassificationCommandHandler(ICustomersDbContext context)
        {
            _context = context;
        }

        public async Task<Result> Handle(DeleteClassificationCommand request, CancellationToken cancellationToken)
        {
            // İlişkili müşteri var mı kontrol et
            var hasRelatedCustomers = await _context.Customers
                .AnyAsync(x => x.ClassificationId == request.Id, cancellationToken);

            if (hasRelatedCustomers)
                return Result.Failure("Bu müşteri kaynağı kullanımda olduğu için silinemez.");

            var Classification = await _context.Classification
                .FindAsync(new object[] { request.Id }, cancellationToken);

            if (Classification == null)
                return Result.Failure("Müşteri kaynağı bulunamadı.");

            _context.Classification.Remove(Classification);
            await _context.SaveChangesAsync(cancellationToken);

            return Result.Success();
        }
    }
}
```

**Validator:**

```csharp
namespace Customers.Application.Classifications.DeleteClassification
{
    public class DeleteClassificationCommandValidator : AbstractValidator<DeleteClassificationCommand>
    {
        public DeleteClassificationCommandValidator()
        {
            RuleFor(x => x.Id)
                .NotEmpty().WithMessage("Müşteri kaynağı ID'si boş olamaz.");
        }
    }
}
```

**Endpoint:**

```csharp
namespace Customers.Application.Classifications.DeleteClassification
{
    internal sealed class DeleteClassificationEndpoint : IEndpoint
    {
        public void MapEndpoint(IEndpointRouteBuilder app)
        {
            app.MapDelete("/api/v1/customers/classifications/{id}", async (
                Guid id,
                IMediator mediator,
                CancellationToken cancellationToken) =>
            {
                var command = new DeleteClassificationCommand(id);
                var result = await mediator.Send(command, cancellationToken);
                return result.Match(Results.NoContent, CustomResults.Problem);
            })
            .WithTags("Customer Classifications")
            .WithGroupName("apiv1")
            .RequireAuthorization("Customers.Management");
        }
    }
}
```

#### Feature: GetClassification

**Query:**

```csharp
namespace Customers.Application.Classifications.GetClassification
{
    public record GetClassificationQuery(Guid Id) : IRequest<Result<ClassificationDto>>;
}
```

**Query Handler:**

```csharp
namespace Customers.Application.Classifications.GetClassification
{
    public class GetClassificationQueryHandler : IRequestHandler<GetClassificationQuery, Result<ClassificationDto>>
    {
        private readonly ICustomersDbContext _context;
        private readonly IMapper _mapper;

        public GetClassificationQueryHandler(ICustomersDbContext context, IMapper mapper)
        {
            _context = context;
            _mapper = mapper;
        }

        public async Task<Result<ClassificationDto>> Handle(GetClassificationQuery request, CancellationToken cancellationToken)
        {
            var Classification = await _context.Classification
                .FindAsync(new object[] { request.Id }, cancellationToken);

            if (Classification == null)
                return Result.Failure<ClassificationDto>("Müşteri kaynağı bulunamadı.");

            return Result.Success(_mapper.Map<ClassificationDto>(Classification));
        }
    }
}
```

**Dto:**

```csharp
namespace Customers.Application.Classifications.GetClassification
{
    public class ClassificationDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
    }
}
```

**Endpoint:**

```csharp
namespace Customers.Application.Classifications.GetClassification
{
    internal sealed class GetClassificationEndpoint : IEndpoint
    {
        public void MapEndpoint(IEndpointRouteBuilder app)
        {
            app.MapGet("/api/v1/customers/classifications/{id}", async (
                Guid id,
                IMediator mediator,
                CancellationToken cancellationToken) =>
            {
                var query = new GetClassificationQuery(id);
                var result = await mediator.Send(query, cancellationToken);
                return result.Match(Results.Ok, error => Results.NotFound(error));
            })
            .WithTags("Customer Classifications")
            .WithGroupName("apiv1")
            .RequireAuthorization("Customers.Management");
        }
    }
}
```

#### Feature: GetClassifications

**Query:**

```csharp
namespace Customers.Application.Classifications.GetClassifications
{
    public class GetClassificationsQuery : IRequest<Result<List<ClassificationDto>>>
    {
        public string SearchTerm { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 10;
    }
}
```

**Query Handler:**

```csharp
namespace Customers.Application.Classifications.GetClassifications
{
    public class GetClassificationsQueryHandler : IRequestHandler<GetClassificationsQuery, Result<List<ClassificationDto>>>
    {
        private readonly ICustomersDbContext _context;
        private readonly IMapper _mapper;

        public GetClassificationsQueryHandler(ICustomersDbContext context, IMapper mapper)
        {
            _context = context;
            _mapper = mapper;
        }

        public async Task<Result<List<ClassificationDto>>> Handle(GetClassificationsQuery request, CancellationToken cancellationToken)
        {
            var query = _context.Classification.AsQueryable();

            if (!string.IsNullOrWhiteSpace(request.SearchTerm))
                query = query.Where(x => x.Name.Contains(request.SearchTerm));

            query = query.OrderBy(x => x.Name);

            var Classifications = await query
                .Skip((request.PageNumber - 1) * request.PageSize)
                .Take(request.PageSize)
                .ToListAsync(cancellationToken);

            return Result.Success(_mapper.Map<List<ClassificationDto>>(Classifications));
        }
    }
}
```

**Dto:**

```csharp
namespace Customers.Application.Classifications.GetClassifications
{
    public class ClassificationDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
    }
}
```

**Endpoint:**

```csharp
namespace Customers.Application.Classifications.GetClassifications
{
    internal sealed class GetClassificationsEndpoint : IEndpoint
    {
        public void MapEndpoint(IEndpointRouteBuilder app)
        {
            app.MapGet("/api/v1/customers/classifications", async (
                [AsParameters] GetClassificationsQuery query,
                IMediator mediator,
                CancellationToken cancellationToken) =>
            {
                var result = await mediator.Send(query, cancellationToken);
                return result.Match(Results.Ok, CustomResults.Problem);
            })
            .WithTags("Customer Classifications")
            .WithGroupName("apiv1")
            .RequireAuthorization("Customers.Management");
        }
    }
}
```

### DbContext ve Repository Implementation

```csharp
namespace MasterCRM.Modules.Customers.Infrastructure.Data
{
    public interface ICustomersDbContext
    {
        DbSet<Classification> Classifications { get; set; }
        DbSet<Customer> Customers { get; set; }

        Task<int> SaveChangesAsync(CancellationToken cancellationToken);
    }

    public class CustomerDbContext : DbContext, ICustomersDbContext
    {
        public CustomerDbContext(DbContextOptions<CustomerDbContext> options) : base(options)
        {
        }

        public DbSet<Classification> Classifications { get; set; }
        public DbSet<Customer> Customers { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Classification>(entity =>
            {
                entity.ToTable("Classification", "Customers");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).HasMaxLength(100).IsRequired();
            });

            base.OnModelCreating(modelBuilder);
        }
    }
}
```

## Test Stratejisi

### Unit Tests

```csharp
namespace MasterCRM.Modules.Customers.Tests.UnitTests.Classifications
{
    public class CreateClassificationCommandHandlerTests
    {
        [Fact]
        public async Task Handle_ValidRequest_ShouldCreateClassification()
        {
            // Arrange
            var dbContextMock = new Mock<ICustomersDbContext>();
            var workcontextMock = new Mock<IWorkContext>();
            var dbSetMock = new Mock<DbSet<Classification>>();

            dbContextMock.Setup(x => x.Classifications).Returns(dbSetMock.Object);
            workcontextMock.Setup(x => x.UserId).Returns(Guid.NewGuid());

            var handler = new CreateClassificationCommandHandler(dbContextMock.Object, workcontextMock.Object);
            var command = new CreateClassificationCommand { Name = "Web Site" };

            // Act
            var result = await handler.Handle(command, CancellationToken.None);

            // Assert
            result.IsSuccess.Should().BeTrue();
            result.Value.Should().NotBeEmpty();
            dbSetMock.Verify(x => x.Add(It.IsAny<Classification>()), Times.Once);
            dbContextMock.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Once);
        }
    }

    // Diğer test metodları benzer şekilde yazılır...
}
```

### Integration Tests

```csharp
namespace MasterCRM.Modules.Customers.Tests.IntegrationTests.Classifications
{
    public class ClassificationsControllerTests : IClassFixture<WebApplicationFactory<Program>>
    {
        private readonly WebApplicationFactory<Program> _factory;

        public ClassificationsControllerTests(WebApplicationFactory<Program> factory)
        {
            _factory = factory;
        }

        [Fact]
        public async Task CreateClassification_ValidRequest_ReturnsCreatedId()
        {
            // Arrange
            var client = _factory.CreateClient();
            var command = new CreateClassificationCommand { Name = "Instagram" };

            // Act
            var response = await client.PostAsJsonAsync("/api/v1/customers/classifications", command);

            // Assert
            response.EnsureSuccessStatusCode();
            var id = await response.Content.ReadFromJsonAsync<Guid>();
            id.Should().NotBeEmpty();
        }

        // Diğer test metodları benzer şekilde yazılır...
    }
}
```

## Endpoint Registrasyonu

Classification modülünün Minimal API endpoint'lerini sisteme kaydetmek için aşağıdaki extension metodu kullanılacaktır:

```csharp
namespace MasterCRM.Modules.Customers.Infrastructure
{
    public static class ClassificationEndpointExtensions
    {
        public static WebApplication MapClassificationEndpoints(this WebApplication app)
        {
            var endpoints = app.Services.GetServices<IEndpoint>()
                .Where(e => e.GetType().Namespace?
                    .StartsWith("MasterCRM.Modules.Customers.Application.Classifications") == true);

            foreach (var endpoint in endpoints)
            {
                endpoint.MapEndpoint(app);
            }

            return app;
        }
    }
}
```

Program.cs içerisinde endpoint'lerin kaydedilmesi:

```csharp
app.MapClassificationEndpoints();
```

## Uygulama Planı

Bu modül, MasterCRM sisteminin "Faz 3: Müşteri Yönetimi" aşamasında, aşağıdaki sırayla geliştirilebilir:

1. Entity ve DbContext implementasyonu
2. CQRS sınıflarının oluşturulması
3. Minimal API Endpoint'lerinin implementasyonu
4. Endpoint kayıt sisteminin kurulması
5. Unit ve Integration testlerinin yazılması
6. Dokümantasyon

Tahmini geliştirme süresi: 2-3 gün

## Bağımlılıklar

- Users modülünün tamamlanmış olması (yetkilendirme için)
- Core altyapının hazır olması (Result pattern, Validation, vb.)

## Riskler ve Çözümler

- Veri tutarlılığı: CASCADE olmayan DELETE işlemleri için Classification'un kullanımda olup olmadığının kontrolü
- Performans: Paging ve filtreleme implementasyonu ile büyük veri setlerinin yönetimi

## Sonuç

Bu RFC, MasterCRM sisteminde müşteri sınıflandırılmasının yönetimi için gerekli backend CRUD işlemlerini tanımlamaktadır. Modüler monolith mimarisi ve CQRS pattern'i kullanılarak, müşteri sınıflarının etkin bir şekilde yönetilmesini sağlayacak API'ler oluşturulacaktır.