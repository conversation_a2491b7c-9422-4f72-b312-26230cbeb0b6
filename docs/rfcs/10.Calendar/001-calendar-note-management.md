# RFC-001: Takvim Notu Yönetim Sistemi

## Durum
Tamamlandı

## Özet
Kullanıcıların takvim notları oluşturmasını, düzenlemesini ve yönetmesini sağlayan kapsamlı bir takvim sistemi. <PERSON><PERSON><PERSON> depart<PERSON>, te<PERSON><PERSON><PERSON><PERSON>, hatırlatmalar ve katılımcı yönetimi içerir.

## Tasarım Detayları

### Domain Model

#### CalendarNote Entity
```csharp
public class CalendarNote : AuditableEntity
{
    public Guid Id { get; set; }
    public string Title { get; set; }
    public string Description { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public CalendarNoteType Type { get; set; }
    public CalendarVisibility Visibility { get; set; }
    public Guid? CreatedByUserId { get; set; }
    public Guid? AssignedUserId { get; set; }
    public Guid? RelatedCustomerId { get; set; }
    public bool IsRecurring { get; set; }
    public bool IsDone { get; set; }
    public bool IsImportant { get; set; }
    public bool IsAllDay { get; set; }
    public bool IsCancelled { get; set; }
    
    // Soft Delete
    public bool IsDeleted { get; set; }
    public DateTime? DeletedDate { get; set; }
    public Guid? DeletedByUserId { get; set; }
    
    // Navigation Properties
    public ICollection<CalendarReminder> Reminders { get; set; }
    public ICollection<CalendarNoteAttendee> Attendees { get; set; }
    public ICollection<CalendarNoteTag> Tags { get; set; }
    public ICollection<CalendarNoteDepartment> Departments { get; set; }
    public RecurrenceRule? RecurrenceRule { get; set; }
}
```

#### Supporting Entities
```csharp
public class CalendarNoteDepartment : AuditableEntity
{
    public Guid Id { get; set; }
    public Guid CalendarNoteId { get; set; }
    public Guid DepartmentId { get; set; }
    public CalendarNote CalendarNote { get; set; }
}

public class CalendarReminder : AuditableEntity
{
    public Guid Id { get; set; }
    public Guid CalendarNoteId { get; set; }
    public int MinutesBefore { get; set; }
    public ReminderChannel Channel { get; set; }
    public bool IsSent { get; set; }
    public DateTime? SentAt { get; set; }
}

public class CalendarNoteAttendee : AuditableEntity
{
    public Guid Id { get; set; }
    public Guid CalendarNoteId { get; set; }
    public Guid UserId { get; set; }
    public bool IsOrganizer { get; set; }
    public AttendanceStatus Status { get; set; }
}
```

### Enum Definitions
```csharp
public enum CalendarNoteType
{
    Note,
    Task,
    Meeting,
    CallAuto
}

public enum CalendarVisibility
{
    Personal,
    Departmental,
    Public
}

public enum ReminderChannel
{
    Email,
    Push,
    SMS
}

public enum AttendanceStatus
{
    Pending,
    Accepted,
    Declined
}

public enum RecurrenceType
{
    Daily,
    Weekly,
    Monthly,
    Yearly
}
```

## API Endpoints

### CRUD Operations
- `POST /api/v1/calendar/notes` - Yeni takvim notu oluşturma
- `GET /api/v1/calendar/notes` - Takvim notlarını listeleme (sayfalama ve filtreleme)
- `PUT /api/v1/calendar/notes/{id}` - Takvim notu güncelleme
- `DELETE /api/v1/calendar/notes/{id}` - Takvim notu silme (soft delete)

### Utility Endpoints
- `GET /api/v1/calendar/enums` - Frontend için enum değerleri

## Özellikler

### 1. Çoklu Departman Desteği
- Bir takvim notu birden çok departmana atanabilir
- Kullanıcılar sadece kendi departmanlarındaki notları görebilir
- Admin kullanıcılar tüm notları görebilir

### 2. Performans Optimizasyonu
- Tarih filtresi belirtilmezse aktif ayın kayıtları getirilir
- SharedUserService ile kullanıcı departmanları dinamik olarak alınır

### 3. Authorization
- Personal: Sadece oluşturan kullanıcı
- Departmental: Aynı departmandaki kullanıcılar
- Public: Tüm kullanıcılar

### 4. Localization
- Tüm hata mesajları ve validasyonlar çok dilli
- Türkçe ve İngilizce desteği

## Teknik Detaylar

### Vertical Slice Architecture
Her operasyon için ayrı klasör yapısı:
```
CalendarNotes/
├── Create/
├── Update/
├── Delete/
├── List/
└── Common/
```

### Validation
- FluentValidation kullanımı
- Localized error messages
- Business rule validations

### Performance Considerations
- Entity Framework Include optimizasyonları
- Bulk operations for related entities
- Index recommendations for StartDate field
