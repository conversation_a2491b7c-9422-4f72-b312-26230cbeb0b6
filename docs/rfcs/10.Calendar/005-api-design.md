# RFC-005: Calendar API Tasarım Rehberi

## Durum
Tamamlandı

## Özet
Calendar modülü API endpoint'lerinin tasarım prensipleri, request/response modelleri ve frontend entegrasyonu.

## API Design Principles

### 1. RESTful Design
- Resource-based URLs
- HTTP verbs for operations
- Consistent response formats
- Proper status codes

### 2. Vertical Slice Architecture
- Self-contained feature folders
- Minimal coupling between features
- Clear separation of concerns

## Endpoints

### Core CRUD Operations

#### Create Calendar Note
```http
POST /api/v1/calendar/notes
Authorization: Bearer {token}
Content-Type: application/json

{
  "title": "Team Meeting",
  "description": "Weekly team sync",
  "startDate": "2025-01-15T10:00:00Z",
  "endDate": "2025-01-15T11:00:00Z",
  "type": 2,
  "visibility": 1,
  "departmentIds": ["dept-1", "dept-2"],
  "isAllDay": false,
  "isImportant": true,
  "attendeeUserIds": ["user-1", "user-2"],
  "tagNames": ["meeting", "weekly"],
  "reminders": [
    {
      "minutesBefore": 15,
      "channel": 0
    }
  ],
  "recurrence": {
    "type": 1,
    "interval": 1,
    "occurrenceCount": 10
  }
}
```

**Response:**
```http
HTTP/1.1 201 Created
Location: /api/v1/calendar/notes/{id}

{
  "success": true,
  "noteId": "note-123",
  "message": "Calendar note created successfully."
}
```

#### List Calendar Notes
```http
GET /api/v1/calendar/notes?pageNumber=1&pageSize=20&startDate=2025-01-01&endDate=2025-01-31
Authorization: Bearer {token}
```

**Response:**
```json
{
  "items": [
    {
      "id": "note-123",
      "title": "Team Meeting",
      "description": "Weekly team sync",
      "startDate": "2025-01-15T10:00:00Z",
      "endDate": "2025-01-15T11:00:00Z",
      "type": 2,
      "visibility": 1,
      "createdByUserId": "user-1",
      "createdByUserName": "John Doe",
      "assignedUserId": "user-2",
      "assignedUserName": "Jane Smith",
      "departments": [
        {
          "departmentId": "dept-1",
          "departmentName": "Engineering"
        }
      ],
      "isRecurring": true,
      "isDone": false,
      "isImportant": true,
      "isAllDay": false,
      "isCancelled": false,
      "insertDate": "2025-01-10T08:00:00Z",
      "tags": [
        {
          "tagId": "tag-1",
          "tagName": "meeting"
        }
      ],
      "reminders": [
        {
          "id": "reminder-1",
          "minutesBefore": 15,
          "channel": 0,
          "isSent": false,
          "sentAt": null
        }
      ],
      "attendees": [
        {
          "id": "attendee-1",
          "userId": "user-1",
          "userName": "John Doe",
          "isOrganizer": true,
          "status": 0
        }
      ]
    }
  ],
  "pageNumber": 1,
  "pageSize": 20,
  "totalCount": 150,
  "totalPages": 8,
  "hasPreviousPage": false,
  "hasNextPage": true
}
```

#### Update Calendar Note
```http
PUT /api/v1/calendar/notes/{id}
Authorization: Bearer {token}
Content-Type: application/json

{
  "id": "note-123",
  "title": "Updated Team Meeting",
  "description": "Updated description",
  // ... other fields
}
```

#### Delete Calendar Note
```http
DELETE /api/v1/calendar/notes/{id}
Authorization: Bearer {token}
```

**Response:**
```http
HTTP/1.1 200 OK

{
  "success": true,
  "message": "Calendar note deleted successfully."
}
```

### Utility Endpoints

#### Get Calendar Enums
```http
GET /api/v1/calendar/enums
```

**Response:**
```json
{
  "recurrenceTypes": [
    { "value": 0, "name": "Daily" },
    { "value": 1, "name": "Weekly" },
    { "value": 2, "name": "Monthly" },
    { "value": 3, "name": "Yearly" }
  ],
  "reminderChannels": [
    { "value": 0, "name": "Email" },
    { "value": 1, "name": "Push" },
    { "value": 2, "name": "SMS" }
  ],
  "calendarNoteTypes": [
    { "value": 0, "name": "Note" },
    { "value": 1, "name": "Task" },
    { "value": 2, "name": "Meeting" },
    { "value": 3, "name": "CallAuto" }
  ],
  "calendarVisibilities": [
    { "value": 0, "name": "Personal" },
    { "value": 1, "name": "Departmental" },
    { "value": 2, "name": "Public" }
  ],
  "attendanceStatuses": [
    { "value": 0, "name": "Pending" },
    { "value": 1, "name": "Accepted" },
    { "value": 2, "name": "Declined" }
  ]
}
```

## Request/Response Models

### Core Dtos

#### RecurrenceRuleDto
```csharp
public class RecurrenceRuleDto
{
    public RecurrenceType Type { get; set; }     // 0=Daily, 1=Weekly, 2=Monthly, 3=Yearly
    public int Interval { get; set; }            // Interval (e.g., every 2 days = 2)
    public int? OccurrenceCount { get; set; }    // How many times to repeat (optional)
    public DateTime? EndDate { get; set; }       // End date (optional)
}
```

#### ReminderDto
```csharp
public class ReminderDto
{
    public int MinutesBefore { get; set; }       // Minutes before event
    public ReminderChannel Channel { get; set; } // 0=Email, 1=Push, 2=SMS
}
```

#### CalendarNoteDepartmentDto
```csharp
public class CalendarNoteDepartmentDto
{
    public Guid DepartmentId { get; set; }
    public string DepartmentName { get; set; }
}
```

### Frontend Integration

#### TypeScript Interfaces
```typescript
interface CreateCalendarNoteRequest {
  title: string;
  description?: string;
  startDate: string;
  endDate?: string;
  type: CalendarNoteType;
  visibility: CalendarVisibility;
  departmentIds?: string[];
  isAllDay: boolean;
  isImportant: boolean;
  attendeeUserIds?: string[];
  tagNames?: string[];
  reminders?: ReminderDto[];
  recurrence?: RecurrenceRuleDto;
}

interface RecurrenceRuleDto {
  type: RecurrenceType;
  interval: number;
  occurrenceCount?: number;
  endDate?: string;
}

enum RecurrenceType {
  Daily = 0,
  Weekly = 1,
  Monthly = 2,
  Yearly = 3
}

enum CalendarNoteType {
  Note = 0,
  Task = 1,
  Meeting = 2,
  CallAuto = 3
}

enum CalendarVisibility {
  Personal = 0,
  Departmental = 1,
  Public = 2
}
```

#### Usage Examples
```typescript
// Create daily recurring meeting
const dailyMeeting: CreateCalendarNoteRequest = {
  title: "Daily Standup",
  startDate: "2025-01-15T09:00:00Z",
  endDate: "2025-01-15T09:30:00Z",
  type: CalendarNoteType.Meeting,
  visibility: CalendarVisibility.Departmental,
  departmentIds: ["engineering-dept"],
  isAllDay: false,
  isImportant: false,
  recurrence: {
    type: RecurrenceType.Daily,
    interval: 1,
    occurrenceCount: 30
  },
  reminders: [
    {
      minutesBefore: 10,
      channel: 1 // Push notification
    }
  ]
};

// Create monthly report task
const monthlyReport: CreateCalendarNoteRequest = {
  title: "Monthly Report",
  description: "Prepare and submit monthly performance report",
  startDate: "2025-01-31T17:00:00Z",
  type: CalendarNoteType.Task,
  visibility: CalendarVisibility.Personal,
  isAllDay: false,
  isImportant: true,
  recurrence: {
    type: RecurrenceType.Monthly,
    interval: 1,
    endDate: "2025-12-31T23:59:59Z"
  }
};
```

## Error Handling

### Validation Errors
```http
HTTP/1.1 400 Bad Request

{
  "success": false,
  "errors": [
    {
      "code": "ValidationError",
      "message": "Title cannot be empty.",
      "type": "Validation"
    },
    {
      "code": "ValidationError",
      "message": "Department selection is required for departmental visibility.",
      "type": "Validation"
    }
  ]
}
```

### Authorization Errors
```http
HTTP/1.1 403 Forbidden

{
  "success": false,
  "errors": [
    {
      "code": "Calendar.Note.Unauthorized",
      "message": "You do not have permission to perform this action on this calendar note.",
      "type": "Authorization"
    }
  ]
}
```

### Not Found Errors
```http
HTTP/1.1 404 Not Found

{
  "success": false,
  "errors": [
    {
      "code": "Calendar.Note.NotFound",
      "message": "The specified calendar note was not found.",
      "type": "NotFound"
    }
  ]
}
```

## Swagger Documentation

### OpenAPI Annotations
```csharp
[HttpPost]
[ProducesResponseType(typeof(CreateCalendarNoteResponse), StatusCodes.Status201Created)]
[ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
[ProducesResponseType(StatusCodes.Status401Unauthorized)]
[ProducesResponseType(StatusCodes.Status403Forbidden)]
public async Task<IActionResult> CreateCalendarNote(
    [FromBody] CreateCalendarNoteCommand command)
{
    // Implementation
}
```

### Example Requests/Responses
- Swagger UI automatically generates examples
- Custom examples can be added via attributes
- Interactive testing available

## Security Considerations

### Authorization
- JWT Bearer token required
- Role-based access control
- Department-based filtering
- Resource ownership validation

### Input Validation
- Model validation with FluentValidation
- SQL injection prevention
- XSS protection
- File upload restrictions

### Rate Limiting
- Per-user rate limits
- API key throttling
- DDoS protection

## Versioning Strategy

### URL Versioning
- `/api/v1/calendar/notes`
- `/api/v2/calendar/notes` (future)

### Backward Compatibility
- Additive changes only
- Deprecation notices
- Migration guides
- Support multiple versions
