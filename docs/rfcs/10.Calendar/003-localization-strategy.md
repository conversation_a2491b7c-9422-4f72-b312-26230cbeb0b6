# RFC-003: Calendar Modülü Lokalizasyon Stratejisi

## Durum
Tamamlandı

## Özet
Calendar modülündeki tüm kullanıcı aray<PERSON><PERSON><PERSON>, hata mesajları ve validasyon mesajlarının çok dilli desteği.

## Motivasyon
- Uluslararası kullanıcı desteği
- Tutarlı hata mesajları
- Maintainable localization structure
- Developer experience iyileştirmesi

## Tasarım Detayları

### Localization Architecture

#### ILocalizer Service
```csharp
public interface ILocalizer
{
    string Get(string key, object? parameters = null);
}

public class Localizer : ILocalizer
{
    public string Get(string key, object? parameters = null)
    {
        var translations = _langService.GetTranslations(_languageCode);
        
        if (!translations.TryGetValue(key, out var value))
        {
            File.AppendAllText("missing_translations.log", 
                $"{_languageCode}:{key}{Environment.NewLine}");
            return $"[{key}]";
        }
        
        // Parameter replacement
        if (parameters != null)
        {
            foreach (var prop in parameters.GetType().GetProperties())
            {
                var placeholder = $"{{{{{prop.Name}}}}}";
                var val = prop.GetValue(parameters)?.ToString();
                value = value.Replace(placeholder, val);
            }
        }
        
        return value;
    }
}
```

### Resource Files Structure

#### English (en.json)
```json
{
  "Calendar.StartTimeWasInThePast": "Start time cannot be in the past.",
  "Calendar.EndTimeWasBeforeStartTime": "End time cannot be before start time.",
  "Calendar.InvalidNoteType": "Invalid note type.",
  "Calendar.InvalidVisibility": "Invalid visibility type.",
  "Calendar.RequiredCustomer": "Customer selection is required.",
  "Calendar.Validation.MissingCustomerForAutoCall": "Customer selection is required for automatic call type.",
  "Calendar.Validation.DepartmentRequired": "Department selection is required.",
  "Calendar.Validation.StartDateCannotBePast": "Start date cannot be in the past.",
  "Calendar.Validation.EndDateBeforeStart": "End date cannot be before start date.",
  "Calendar.Validation.EndDateMustBeAfterStartDate": "End date must be after start date.",
  "Calendar.Note.NotFound": "The specified calendar note was not found.",
  "Calendar.Note.Unauthorized": "You do not have permission to perform this action on this calendar note.",
  "Calendar.Note.DeleteSuccess": "Calendar note deleted successfully.",
  "Calendar.Note.UpdateSuccess": "Calendar note updated successfully.",
  "Calendar.Note.CreateSuccess": "Calendar note created successfully.",
  "Calendar.Note.TitleRequired": "Title cannot be empty.",
  "Calendar.Note.TitleMaxLength": "Title cannot be longer than 500 characters.",
  "Calendar.Note.DescriptionMaxLength": "Description cannot be longer than 2000 characters.",
  "Calendar.Note.StartDateRequired": "Start date cannot be empty.",
  "Calendar.Note.PageNumberInvalid": "Page number must be greater than 0.",
  "Calendar.Note.PageSizeInvalid": "Page size must be greater than 0.",
  "Calendar.Note.PageSizeMaxLimit": "Page size cannot exceed 100.",
  "Calendar.Note.SortDirectionInvalid": "Sort direction must be 'asc' or 'desc'.",
  "Calendar.UnknownDepartment": "Unknown Department",
  "Calendar.UnknownUser": "Unknown User"
}
```

#### Turkish (tr.json)
```json
{
  "Calendar.StartTimeWasInThePast": "Başlangıç tarihi geçmiş olamaz.",
  "Calendar.EndTimeWasBeforeStartTime": "Bitiş tarihi başlangıç tarihinden önce olamaz.",
  "Calendar.InvalidNoteType": "Geçersiz not tipi.",
  "Calendar.InvalidVisibility": "Geçersiz görünürlük tipi.",
  "Calendar.RequiredCustomer": "Müşteri seçilmesi zorunludur.",
  "Calendar.Validation.MissingCustomerForAutoCall": "Otomatik arama tipi için müşteri seçimi zorunludur.",
  "Calendar.Validation.DepartmentRequired": "Departman seçilmelidir.",
  "Calendar.Validation.StartDateCannotBePast": "Başlangıç tarihi geçmiş olamaz.",
  "Calendar.Validation.EndDateBeforeStart": "Bitiş tarihi, başlangıç tarihinden önce olamaz.",
  "Calendar.Validation.EndDateMustBeAfterStartDate": "Bitiş tarihi başlangıç tarihinden sonra olmalıdır.",
  "Calendar.Note.NotFound": "Belirtilen takvim notu bulunamadı.",
  "Calendar.Note.Unauthorized": "Bu takvim notu üzerinde işlem yapma yetkiniz bulunmamaktadır.",
  "Calendar.Note.DeleteSuccess": "Takvim notu başarıyla silindi.",
  "Calendar.Note.UpdateSuccess": "Takvim notu başarıyla güncellendi.",
  "Calendar.Note.CreateSuccess": "Takvim notu başarıyla oluşturuldu.",
  "Calendar.Note.TitleRequired": "Başlık boş olamaz.",
  "Calendar.Note.TitleMaxLength": "Başlık 500 karakterden uzun olamaz.",
  "Calendar.Note.DescriptionMaxLength": "Açıklama 2000 karakterden uzun olamaz.",
  "Calendar.Note.StartDateRequired": "Başlangıç tarihi boş olamaz.",
  "Calendar.Note.PageNumberInvalid": "Sayfa numarası 0'dan büyük olmalıdır.",
  "Calendar.Note.PageSizeInvalid": "Sayfa boyutu 0'dan büyük olmalıdır.",
  "Calendar.Note.PageSizeMaxLimit": "Sayfa boyutu 100'ü geçemez.",
  "Calendar.Note.SortDirectionInvalid": "Sıralama yönü 'asc' veya 'desc' olmalıdır.",
  "Calendar.UnknownDepartment": "Bilinmeyen Departman",
  "Calendar.UnknownUser": "Bilinmeyen Kullanıcı"
}
```

### Usage in Validators

#### FluentValidation Integration
```csharp
public class CreateCalendarNoteCommandValidator : AbstractValidator<CreateCalendarNoteCommand>
{
    private readonly ILocalizer _localizer;
    
    public CreateCalendarNoteCommandValidator(ILocalizer localizer)
    {
        _localizer = localizer;

        RuleFor(x => x.Title)
            .NotEmpty().WithMessage(_localizer.Get("Calendar.Note.TitleRequired"))
            .MaximumLength(500).WithMessage(_localizer.Get("Calendar.Note.TitleMaxLength"));

        RuleFor(x => x.StartDate)
            .GreaterThanOrEqualTo(DateTime.UtcNow)
            .WithMessage(_localizer.Get("Calendar.StartTimeWasInThePast"));

        When(x => x.Visibility == CalendarVisibility.Departmental, () =>
        {
            RuleFor(x => x.DepartmentIds)
                .NotEmpty()
                .WithMessage(_localizer.Get("Calendar.Validation.DepartmentRequired"));
        });
    }
}
```

### Usage in Handlers

#### Error Messages
```csharp
public async Task<Result<UpdateCalendarNoteResponse>> Handle(
    UpdateCalendarNoteCommand request, 
    CancellationToken cancellationToken)
{
    var note = await _dbContext.CalendarNotes.FindAsync(request.Id);
    
    if (note == null)
    {
        validationErrors.Add(_localizer.Get("Calendar.Note.NotFound"));
        return Result<UpdateCalendarNoteResponse>.Validation(validationErrors);
    }

    if (note.CreatedByUserId != _workContext.UserId && !_workContext.IsAdmin)
    {
        validationErrors.Add(_localizer.Get("Calendar.Note.Unauthorized"));
        return Result<UpdateCalendarNoteResponse>.Validation(validationErrors);
    }

    // Success message
    response.Message = _localizer.Get("Calendar.Note.UpdateSuccess");
    return Result.Success(response);
}
```

#### Dynamic Content
```csharp
// ListCalendarNotesQueryHandler'da hardcoded string'lerin yerine
UserName = users.TryGetValue(a.UserId, out var attendeeName) 
    ? attendeeName 
    : _localizer.Get("Calendar.UnknownUser");

DepartmentName = departments.TryGetValue(d.DepartmentId, out var deptName) 
    ? deptName 
    : _localizer.Get("Calendar.UnknownDepartment");
```

## Key Naming Convention

### Hierarchical Structure
```
Calendar.{Category}.{Specific}
Calendar.Validation.{ValidationRule}
Calendar.Note.{Operation/Property}
```

### Examples
- `Calendar.Validation.DepartmentRequired`
- `Calendar.Note.TitleRequired`
- `Calendar.Note.UpdateSuccess`
- `Calendar.UnknownUser`

## Missing Translation Handling

### Development Mode
```csharp
if (!translations.TryGetValue(key, out var value))
{
    File.AppendAllText("missing_translations.log", 
        $"{_languageCode}:{key}{Environment.NewLine}");
    return $"[{key}]";
}
```

### Production Mode
- Fallback to English
- Log missing keys
- Return key in brackets for debugging

## Benefits

1. **Consistency**: Tüm mesajlar merkezi olarak yönetilir
2. **Maintainability**: Yeni dil desteği kolay eklenir
3. **Developer Experience**: Missing key detection
4. **User Experience**: Native language support
5. **Quality**: Standardized error messages

## Future Enhancements

1. **Pluralization Support**: Sayısal değerlere göre çoğul/tekil
2. **Context-Aware Translation**: Bağlama göre çeviri
3. **Real-time Language Switching**: Kullanıcı dil değişikliği
4. **Translation Management UI**: Admin panel entegrasyonu
