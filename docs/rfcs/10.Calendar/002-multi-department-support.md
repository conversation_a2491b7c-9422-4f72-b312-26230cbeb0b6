# RFC-002: <PERSON><PERSON><PERSON> Departman Desteği

## Durum
Tamamlandı

## Özet
Calendar modülünde bir takvim notunun birden çok departmana ait olabilmesi ve kullanıcıların departman bazlı yetkilendirme sistemi.

## Motivasyon
- Bir takvim notu birden çok departmanı ilgilendirebilir
- Kullanıcılar sadece yetkili oldukları departmanların notlarını görmeli
- Departman değişiklikleri dinamik olarak yansıtılmalı

## Tasarım Detayları

### Database Schema Changes

#### Eski Yapı (Single Department)
```csharp
public class CalendarNote
{
    public Guid? DepartmentId { get; set; } // Tek departman
}
```

#### Yeni <PERSON>ı (Multi Department)
```csharp
public class CalendarNote
{
    // DepartmentId property kaldırıldı
    public ICollection<CalendarNoteDepartment> Departments { get; set; }
}

public class CalendarNoteDepartment : AuditableEntity
{
    public Guid Id { get; set; }
    public Guid CalendarNoteId { get; set; }
    public Guid DepartmentId { get; set; }
    public CalendarNote CalendarNote { get; set; }
}
```

### API Changes

#### Request Models
```csharp
// Eski
public class CreateCalendarNoteCommand
{
    public Guid? DepartmentId { get; set; }
}

// Yeni
public class CreateCalendarNoteCommand
{
    public List<Guid>? DepartmentIds { get; set; }
}
```

#### Response Models
```csharp
// Eski
public class CalendarNoteDto
{
    public Guid? DepartmentId { get; set; }
    public string? DepartmentName { get; set; }
}

// Yeni
public class CalendarNoteDto
{
    public List<CalendarNoteDepartmentDto> Departments { get; set; }
}

public class CalendarNoteDepartmentDto
{
    public Guid DepartmentId { get; set; }
    public string DepartmentName { get; set; }
}
```

### Authorization Logic

#### SharedUserService Integration
```csharp
public async Task<Result<PagedResult<CalendarNoteDto>>> Handle(
    ListCalendarNotesQuery request, 
    CancellationToken cancellationToken)
{
    var query = _dbContext.CalendarNotes
        .Include(n => n.Departments)
        .Where(n => !n.IsDeleted);

    if (!_workContext.HasRole("Admin"))
    {
        var currentUserResult = await _userService.GetUserAsync(_workContext.UserId);
        if (currentUserResult.IsSuccess && currentUserResult.Value.DepartmentIds != null)
        {
            var userDepartmentIds = currentUserResult.Value.DepartmentIds;
            
            query = query.Where(n => 
                n.CreatedByUserId == _workContext.UserId ||
                n.AssignedUserId == _workContext.UserId ||
                n.Visibility == CalendarVisibility.Departmental && 
                    n.Departments.Any(d => userDepartmentIds.Contains(d.DepartmentId)) ||
                n.Visibility == CalendarVisibility.Public ||
                n.Attendees.Any(a => a.UserId == _workContext.UserId));
        }
    }
}
```

### CRUD Operations

#### Create Operation
```csharp
// Departmanları ekle
if (request.DepartmentIds != null && request.DepartmentIds.Any())
{
    var noteDepartments = request.DepartmentIds.Select(departmentId => 
        new CalendarNoteDepartment
        {
            Id = Guid.NewGuid(),
            CalendarNoteId = note.Id,
            DepartmentId = departmentId
        }).ToList();

    await _dbContext.CalendarNoteDepartments.AddRangeAsync(noteDepartments);
}
```

#### Update Operation
```csharp
private async Task UpdateDepartments(CalendarNote note, List<Guid> departmentIds)
{
    // Mevcut departmanları sil
    var existingDepartments = note.Departments.ToList();
    foreach (var dept in existingDepartments)
    {
        _dbContext.CalendarNoteDepartments.Remove(dept);
    }

    // Yeni departmanları ekle
    var newDepartments = departmentIds.Select(departmentId => 
        new CalendarNoteDepartment
        {
            Id = Guid.NewGuid(),
            CalendarNoteId = note.Id,
            DepartmentId = departmentId
        }).ToList();

    await _dbContext.CalendarNoteDepartments.AddRangeAsync(newDepartments);
    note.Departments = newDepartments;
}
```

### Validation Rules

```csharp
// Departmansal görünürlük için departman seçimi zorunlu
RuleFor(x => x.DepartmentIds)
    .NotEmpty()
    .When(x => x.Visibility == CalendarVisibility.Departmental)
    .WithMessage("Calendar.Validation.DepartmentRequired");
```

## Migration Strategy

### Backward Compatibility
- API endpoint'leri aynı kaldı
- Request/Response modelleri güncellendi
- Eski DepartmentId alanı kaldırıldı

### Data Migration
```sql
-- Mevcut DepartmentId verilerini yeni tabloya taşı
INSERT INTO CalendarNoteDepartments (Id, CalendarNoteId, DepartmentId, InsertDate)
SELECT NEWID(), Id, DepartmentId, GETUTCDATE()
FROM CalendarNotes 
WHERE DepartmentId IS NOT NULL;

-- Eski DepartmentId kolonunu kaldır
ALTER TABLE CalendarNotes DROP COLUMN DepartmentId;
```

## Benefits

1. **Flexibility**: Bir not birden çok departmana atanabilir
2. **Security**: Kullanıcılar sadece yetkili oldukları departmanların notlarını görür
3. **Dynamic**: Departman değişiklikleri otomatik yansır
4. **Performance**: SharedUserService ile optimize edilmiş sorgular
5. **Scalability**: Departman sayısı artışında performans sorunu yok

## Considerations

1. **Memory Usage**: Çoklu departman ilişkileri daha fazla bellek kullanır
2. **Query Complexity**: JOIN işlemleri artar
3. **Index Strategy**: DepartmentId alanında index gerekli
4. **Cache Strategy**: Kullanıcı departman bilgileri cache'lenebilir
