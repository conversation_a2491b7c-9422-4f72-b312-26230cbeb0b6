@baseUrl = https://localhost:7001
@token = {{$dotenv TOKEN}}

### Variables
@ticketId = 550e8400-e29b-41d4-a716-446655440000

### Get Ticket List
GET {{baseUrl}}/api/v1/requests/tickets
Authorization: Bearer {{token}}
Content-Type: application/json

### Get Ticket by ID
GET {{baseUrl}}/api/v1/requests/tickets/{{ticketId}}
Authorization: Bearer {{token}}
Content-Type: application/json

### Create Ticket
POST {{baseUrl}}/api/v1/requests/tickets
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "ticketType": 1,
  "subjectId": "550e8400-e29b-41d4-a716-446655440001",
  "customerId": "550e8400-e29b-41d4-a716-446655440002",
  "title": "Test Ticket",
  "description": "Test ticket description",
  "priority": 1,
  "departmentIds": ["550e8400-e29b-41d4-a716-446655440003"],
  "tags": ["test", "api"]
}

### Update Ticket (PUT)
PUT {{baseUrl}}/api/v1/requests/tickets/{{ticketId}}
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "id": "{{ticketId}}",
  "ticketType": 1,
  "subjectId": "550e8400-e29b-41d4-a716-446655440001",
  "title": "Updated Test Ticket",
  "description": "Updated test ticket description",
  "priority": 2,
  "departmentIds": ["550e8400-e29b-41d4-a716-446655440003"],
  "tags": ["updated", "test"],
  "ticketFiles": []
}

### Patch Ticket (PATCH) - Update only specific fields
PATCH {{baseUrl}}/api/v1/requests/tickets/{{ticketId}}
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "title": "Patched Ticket Title",
  "priority": 3,
  "tags": ["patched", "urgent"]
}

### Patch Ticket - Update only status
PATCH {{baseUrl}}/api/v1/requests/tickets/{{ticketId}}
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "statusId": "550e8400-e29b-41d4-a716-446655440004"
}

### Patch Ticket - Update watchlist
PATCH {{baseUrl}}/api/v1/requests/tickets/{{ticketId}}
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "watchlist": ["550e8400-e29b-41d4-a716-446655440005", "550e8400-e29b-41d4-a716-446655440006"]
}

### Patch Ticket - Update end date
PATCH {{baseUrl}}/api/v1/requests/tickets/{{ticketId}}
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "endDate": "2024-12-31T23:59:59Z"
}

### Patch Ticket - Update attribute data
PATCH {{baseUrl}}/api/v1/requests/tickets/{{ticketId}}
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "attributeData": {
    "customField1": "value1",
    "customField2": "value2"
  }
}

### Delete Ticket
DELETE {{baseUrl}}/api/v1/requests/tickets/{{ticketId}}
Authorization: Bearer {{token}}
Content-Type: application/json

### Get Ticket History
GET {{baseUrl}}/api/v1/requests/tickets/{{ticketId}}/history
Authorization: Bearer {{token}}
Content-Type: application/json
