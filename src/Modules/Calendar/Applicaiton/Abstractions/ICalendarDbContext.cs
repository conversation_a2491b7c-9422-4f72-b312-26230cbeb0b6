using Microsoft.EntityFrameworkCore;
using Shared.Infrastructure.Data;
using Calendar.Domain;

namespace Calendar.Application.Abstractions;

public interface ICalendarDbContext : IBaseDbContext
{
    DbSet<CalendarNote> CalendarNotes { get; }
    DbSet<CalendarNoteFile> CalendarNoteFiles { get; }
    DbSet<CalendarReminder> CalendarReminders { get; }
    DbSet<CalendarNoteAttendee> CalendarNoteAttendees { get; }
    DbSet<RecurrenceRule> RecurrenceRules { get; }
    DbSet<UserCalendarSettings> UserCalendarSettings { get; }
    DbSet<CalendarTag> CalendarTags { get; }
    DbSet<CalendarNoteTag> CalendarNoteTags { get; }
    DbSet<CalendarNoteDepartment> CalendarNoteDepartments { get; }
}
