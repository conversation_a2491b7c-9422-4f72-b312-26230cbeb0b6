using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Infrastructure.Localization;
using Calendar.Application.CalendarNotes.Common;
using Shared.Contracts;
using Microsoft.AspNetCore.Http;
using Shared.Application;
using Shared.Application.EventBus;
using Calendar.Domain;
using Calendar.Application.Abstractions;
using Microsoft.EntityFrameworkCore;

namespace Calendar.Application.CalendarNotes.Create;
public class CreateCalendarNoteCommandHandler :  IRequestHandler<CreateCalendarNoteCommand, Result<CreateCalendarNoteResponse>>
{
    private readonly ILogger<CreateCalendarNoteCommandHandler> _logger;
    private readonly IReminderScheduler _reminderScheduler;
    private readonly ISharedCustomerService _customerService;
    private readonly ILocalizer _localizer;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly IWorkContext _workContext;
    private readonly IEventBus _eventBus;
    private readonly ICalendarDbContext _dbContext;

    public CreateCalendarNoteCommandHandler(
        ILogger<CreateCalendarNoteCommandHandler> logger,
        IReminderScheduler reminderScheduler,
        ISharedCustomerService customerService,
        ILocalizer localizer,
        IHttpContextAccessor httpContextAccessor,
        IWorkContext workContext,
        IEventBus eventBus,
        ICalendarDbContext dbContext)
    {
        _logger = logger;
        _reminderScheduler = reminderScheduler;
        _customerService = customerService;
        _localizer = localizer;
        _httpContextAccessor = httpContextAccessor;
        _workContext = workContext;
        _eventBus = eventBus;
        _dbContext = dbContext;
    }

    public async Task<Result<CreateCalendarNoteResponse>> Handle(CreateCalendarNoteCommand request, CancellationToken cancellationToken)
    {
        CreateCalendarNoteResponse response = new();

        var validationErrors = new List<string>();
        var currentUserId = _workContext.UserId;

        if (request.Type == CalendarNoteType.CallAuto && request.RelatedCustomerId == null)
        {
            validationErrors.Add(_localizer.Get("Calendar.Validation.MissingCustomerForAutoCall"));
        }

        if (request.Visibility == CalendarVisibility.Departmental && (request.DepartmentIds == null || !request.DepartmentIds.Any()))
        {
            validationErrors.Add(_localizer.Get("Calendar.Validation.DepartmentRequired"));
        }

        if (request.StartDate < DateTime.UtcNow)
        {
            validationErrors.Add(_localizer.Get("Calendar.Validation.StartDateCannotBePast"));
        }

        if (request.EndDate.HasValue && request.EndDate < request.StartDate)
        {
            validationErrors.Add(_localizer.Get("Calendar.Validation.EndDateBeforeStart"));
        }

        if (validationErrors.Any())
        {
            var errors = validationErrors.Select(msg => new Error("ValidationError", msg, ErrorType.Validation)).ToArray();
            return Result<CreateCalendarNoteResponse>.Validation(new ValidationError(errors), response);
        }

        // Not Oluşturma
        var note = new CalendarNote
        {
            Id = Guid.NewGuid(),
            Title = request.Title,
            Description = request.Description,
            StartDate = request.StartDate,
            EndDate = request.EndDate,
            Type = request.Type,
            Visibility = request.Visibility,
            RelatedCustomerId = request.RelatedCustomerId,
            AssignedUserId = request.AssignedUserId,

            CreatedByUserId = currentUserId,
            InsertDate = DateTime.UtcNow,
            IsRecurring = request.IsRecurring,
            IsImportant = request.IsImportant,
            IsAllDay = request.IsAllDay, 
            Reminders = request.Reminders.Select(r => new CalendarReminder
            {
                Id = Guid.NewGuid(),
                MinutesBefore = r.MinutesBefore,
                Channel = r.Channel,
                IsSent = false
            }).ToList()
        };

        if (request.TagNames?.Any() == true)
        {
            var tags = new List<CalendarNoteTag>();
            var newTags = new List<CalendarTag>();
            
            foreach (var tagName in request.TagNames)
            {
                var existingTag = await _dbContext.CalendarTags
                    .FirstOrDefaultAsync(t => t.Name.ToLower() == tagName.ToLower(), cancellationToken);

                if (existingTag == null)
                {
                    existingTag = new CalendarTag
                    {
                        Id = Guid.NewGuid(),
                        Name = tagName
                    };
                    newTags.Add(existingTag);
                }

                tags.Add(new CalendarNoteTag
                {
                    CalendarNoteId = note.Id,
                    TagId = existingTag.Id,
                    Tag = existingTag
                });
            }
            note.Tags = tags;

            if (newTags.Any())
            {
                await _dbContext.CalendarTags.AddRangeAsync(newTags, cancellationToken);
            }

        }

        if (request.IsRecurring && request.Recurrence is not null)
        {
            note.RecurrenceRule = new RecurrenceRule
            {
                Id = Guid.NewGuid(),
                Type = request.Recurrence.Type,
                Interval = request.Recurrence.Interval,
                OccurrenceCount = request.Recurrence.OccurrenceCount,
                EndDate = request.Recurrence.EndDate
            };
        }

        if (request.AttendeeUserIds?.Any() == true)
        {
            note.Attendees = request.AttendeeUserIds
                .Select(uid => new CalendarNoteAttendee
                {
                    Id = Guid.NewGuid(),
                    UserId = uid,
                    IsOrganizer = uid == currentUserId,
                    Status = AttendanceStatus.Pending
                }).ToList();
        }

        

        
        // Departmanları ekle
        if (request.DepartmentIds != null && request.DepartmentIds.Any())
        {
            var noteDepartments = request.DepartmentIds.Select(departmentId => new CalendarNoteDepartment
            {
                Id = Guid.NewGuid(),
                CalendarNoteId = note.Id,
                DepartmentId = departmentId
            }).ToList();

            await _dbContext.CalendarNoteDepartments.AddRangeAsync(noteDepartments, cancellationToken);
        }
 

        // Hatırlatmaları Planla
        foreach (var reminder in note.Reminders)
        {
            var triggerTime = note.StartDate.AddMinutes(-reminder.MinutesBefore);
            if (triggerTime > DateTime.UtcNow)
            {
                await _reminderScheduler.ScheduleReminderAsync(note.Id, reminder.Id, triggerTime, reminder.Channel);
            }
        }

        switch (note.Type)
        {
            case CalendarNoteType.CallAuto:
                // Otomatik çağrı notu oluşturuldu
                break;
            case CalendarNoteType.Meeting:
                // Toplantı notu oluşturuldu
                break;
            case CalendarNoteType.Task:
                // Görev notu oluşturuldu
                break;
        }

        // Event Bus veya Domain Event Hook
        // await _eventBus.PublishAsync(...);
        // notification veya domain event publish edilebilir


        await _dbContext.CalendarNotes.AddAsync(note, cancellationToken);
        await _dbContext.SaveChangesAsync(cancellationToken);

        var ip = _httpContextAccessor.HttpContext?.Connection?.RemoteIpAddress?.ToString();
        var userAgent = _httpContextAccessor.HttpContext?.Request?.Headers["User-Agent"].ToString();

        _logger.LogInformation(
            "Calendar note created by UserId: {UserId}. Title: {Title}, Customer: {CustomerId}, Departments: {DepartmentIds}, IsRecurring: {IsRecurring}, IP: {IP}, UA: {UserAgent}, Date: {Date}",
            currentUserId,
            note.Title,
            note.RelatedCustomerId,
            request.DepartmentIds != null ? string.Join(",", request.DepartmentIds) : "None",
            note.IsRecurring,
            ip,
            userAgent,
            DateTime.Now
        );

            response.TotalRecords = 1;
            response.SuccessfulRecords = 1;
            response.Errors = validationErrors;
            response.Success = !validationErrors.Any();

            return Result.Success(response);
    }
}

