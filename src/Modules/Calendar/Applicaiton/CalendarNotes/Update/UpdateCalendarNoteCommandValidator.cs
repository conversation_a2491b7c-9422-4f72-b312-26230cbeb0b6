using FluentValidation;
using Calendar.Domain;
using Shared.Infrastructure.Localization;

namespace Calendar.Application.CalendarNotes.Update;

public class UpdateCalendarNoteCommandValidator : AbstractValidator<UpdateCalendarNoteCommand>
{
    private readonly ILocalizer _localizer;

    public UpdateCalendarNoteCommandValidator(ILocalizer localizer)
    {
        _localizer = localizer;

        RuleFor(x => x.Id)
            .NotEmpty().WithMessage(_localizer.Get("Calendar.Note.IdRequired"));

        RuleFor(x => x.Title)
            .NotEmpty().WithMessage(_localizer.Get("Calendar.Note.TitleRequired"))
            .MaximumLength(200).WithMessage(_localizer.Get("Calendar.Note.TitleMaxLength"));

        RuleFor(x => x.Description)
            .MaximumLength(2000).WithMessage(_localizer.Get("Calendar.Note.DescriptionMaxLength"));

        RuleFor(x => x.StartDate)
            .NotEmpty().WithMessage(_localizer.Get("Calendar.Note.StartDateRequired"));

        RuleFor(x => x.EndDate)
            .GreaterThan(x => x.StartDate)
            .When(x => x.EndDate.HasValue)
            .WithMessage(_localizer.Get("Calendar.Validation.EndDateMustBeAfterStartDate"));

        RuleFor(x => x.Type)
            .IsInEnum().WithMessage(_localizer.Get("Calendar.Note.InvalidNoteType"));

        RuleFor(x => x.Visibility)
            .IsInEnum().WithMessage(_localizer.Get("Calendar.Note.InvalidVisibility"));

        RuleFor(x => x.DepartmentIds)
            .NotEmpty()
            .When(x => x.Visibility == CalendarVisibility.Departmental)
            .WithMessage(_localizer.Get("Calendar.Note.DepartmentRequiredForDepartmental"));

        RuleForEach(x => x.Reminders)
            .SetValidator(new UpdateReminderDtoValidator(_localizer));

        RuleFor(x => x.Recurrence)
            .SetValidator(new UpdateRecurrenceRuleDtoValidator(_localizer))
            .When(x => x.Recurrence != null);

        RuleFor(x => x.RelatedCompanyId)
            .MustAsync(async (id, cancellationToken) => 
            {
                if (!id.HasValue) return true;
                // Add company existence check if needed
                return true;
            })
            .WithMessage(_localizer.Get("Calendar.Note.CompanyNotFound"))
            .When(x => x.RelatedCompanyId.HasValue);
    }
}

public class UpdateReminderDtoValidator : AbstractValidator<UpdateReminderDto>
{
    private readonly ILocalizer _localizer;

    public UpdateReminderDtoValidator(ILocalizer localizer)
    {
        _localizer = localizer;

        RuleFor(x => x.MinutesBefore)
            .GreaterThanOrEqualTo(0).WithMessage(_localizer.Get("Calendar.Note.ReminderMinutesInvalid"))
            .LessThanOrEqualTo(43200).WithMessage(_localizer.Get("Calendar.Note.ReminderMinutesMaxLimit"));

        RuleFor(x => x.Channel)
            .IsInEnum().WithMessage(_localizer.Get("Calendar.Note.InvalidReminderChannel"));
    }
}

public class UpdateRecurrenceRuleDtoValidator : AbstractValidator<UpdateRecurrenceRuleDto>
{
    private readonly ILocalizer _localizer;

    public UpdateRecurrenceRuleDtoValidator(ILocalizer localizer)
    {
        _localizer = localizer;

        RuleFor(x => x.Type)
            .IsInEnum().WithMessage(_localizer.Get("Calendar.Note.InvalidRecurrenceType"));

        RuleFor(x => x.Interval)
            .GreaterThan(0).WithMessage(_localizer.Get("Calendar.Note.RecurrenceIntervalInvalid"))
            .LessThanOrEqualTo(365).WithMessage(_localizer.Get("Calendar.Note.RecurrenceIntervalMaxLimit"));

        RuleFor(x => x.OccurrenceCount)
            .GreaterThan(0)
            .When(x => x.OccurrenceCount.HasValue)
            .WithMessage(_localizer.Get("Calendar.Note.RecurrenceCountInvalid"));

        RuleFor(x => x.EndDate)
            .GreaterThan(DateTime.UtcNow)
            .When(x => x.EndDate.HasValue)
            .WithMessage(_localizer.Get("Calendar.Note.RecurrenceEndDateInvalid"));
    }
}
