using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Calendar.Application.CalendarNotes.Update;

internal sealed class UpdateCalendarNoteEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPut("/api/v1/calendar/notes/{id}", async (
            Guid id,
            UpdateCalendarNoteCommand command,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            if (id != command.Id)
            {
                command = command with { Id = id };
            }
            
            var result = await mediator.Send(command, cancellationToken);
            return result.Match(
                success => Results.NoContent(),
                CustomResults.Problem);
        })
        .WithTags("Calendar.Notes")
        .WithGroupName("apiv1")
        .Produces(StatusCodes.Status204NoContent)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .RequireAuthorization("Calendar.Management");
    }
}
