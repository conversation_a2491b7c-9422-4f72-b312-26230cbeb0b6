using Calendar.Domain;
using MediatR;
using Shared.Application;

namespace Calendar.Application.CalendarNotes.Update;

public record UpdateCalendarNoteCommand : IRequest<Result<UpdateCalendarNoteResponse>>
{
    public Guid Id { get; init; }
    public string Title { get; init; } = string.Empty;
    public string Description { get; init; } = string.Empty;
    public DateTime StartDate { get; init; }
    public DateTime? EndDate { get; init; }
    public CalendarNoteType Type { get; init; }
    public CalendarVisibility Visibility { get; init; }
    public Guid? RelatedCustomerId { get; init; }
    public Guid? RelatedCompanyId { get; init; }
    public Guid? AssignedUserId { get; init; }
    public List<Guid>? DepartmentIds { get; init; }
    public bool IsRecurring { get; init; }
    public bool IsImportant { get; init; }
    public bool IsAllDay { get; init; }
    public bool IsDone { get; init; }
    public bool IsCancelled { get; init; }
    public UpdateRecurrenceRuleDto? Recurrence { get; init; }
    public List<UpdateReminderDto> Reminders { get; init; } = new();
    public List<Guid>? AttendeeUserIds { get; init; }
    public List<string>? TagNames { get; init; }
}

public record UpdateReminderDto
{
    public Guid? Id { get; init; } // Null ise yeni reminder
    public int MinutesBefore { get; init; }
    public ReminderChannel Channel { get; init; }
    public bool IsDeleted { get; init; } // Mevcut reminder'ı silmek için
}

public record UpdateRecurrenceRuleDto
{
    public RecurrenceType Type { get; init; }
    public int Interval { get; init; }
    public int? OccurrenceCount { get; init; }
    public DateTime? EndDate { get; init; }
}
