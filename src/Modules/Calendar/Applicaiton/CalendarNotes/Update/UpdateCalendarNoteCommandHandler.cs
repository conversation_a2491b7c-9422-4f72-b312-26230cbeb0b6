using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Calendar.Application.Abstractions;
using Calendar.Domain;
using Shared.Application;
using Shared.Contracts;
using Shared.Infrastructure.Localization;

namespace Calendar.Application.CalendarNotes.Update;

public class UpdateCalendarNoteCommandHandler : IRequestHandler<UpdateCalendarNoteCommand, Result<UpdateCalendarNoteResponse>>
{
    private readonly ICalendarDbContext _dbContext;
    private readonly ILogger<UpdateCalendarNoteCommandHandler> _logger;
    private readonly IWorkContext _workContext;
    private readonly ILocalizer _localizer;

    public UpdateCalendarNoteCommandHandler(
        ICalendarDbContext dbContext,
        ILogger<UpdateCalendarNoteCommandHandler> logger,
        IWorkContext workContext,
        ILocalizer localizer)
    {
        _dbContext = dbContext;
        _logger = logger;
        _workContext = workContext;
        _localizer = localizer;
    }

    public async Task<Result<UpdateCalendarNoteResponse>> Handle(UpdateCalendarNoteCommand request, CancellationToken cancellationToken)
    {
        var response = new UpdateCalendarNoteResponse();
        var validationErrors = new List<string>();

        // Mevcut notu bul
        var note = await _dbContext.CalendarNotes
            .Include(n => n.Reminders)
            .Include(n => n.Attendees)
            .Include(n => n.Tags).ThenInclude(t => t.Tag)
            .Include(n => n.Departments)
            .FirstOrDefaultAsync(n => n.Id == request.Id && !n.IsDeleted, cancellationToken);

        if (note == null)
        {
            validationErrors.Add(_localizer.Get("Calendar.Note.NotFound"));
            response.Success = false;
            response.Errors = validationErrors;
            return Result<UpdateCalendarNoteResponse>.Validation(new ValidationError(
                validationErrors.Select(err => new Error("ValidationError", err, ErrorType.Validation)).ToArray()),
                response);
        }

        // Yetki kontrolü
        if (note.CreatedByUserId != _workContext.UserId && !_workContext.HasRole("Admin"))
        {
            validationErrors.Add(_localizer.Get("Calendar.Note.Unauthorized"));
            response.Success = false;
            response.Errors = validationErrors;
            return Result<UpdateCalendarNoteResponse>.Validation(new ValidationError(
                validationErrors.Select(err => new Error("ValidationError", err, ErrorType.Validation)).ToArray()),
                response);
        }

        // Validation kontrolü
        if (request.Visibility == CalendarVisibility.Departmental && (request.DepartmentIds == null || !request.DepartmentIds.Any()))
        {
            validationErrors.Add(_localizer.Get("Calendar.Validation.DepartmentRequired"));
        }

        if (request.StartDate < DateTime.UtcNow && note.StartDate >= DateTime.UtcNow)
        {
            validationErrors.Add(_localizer.Get("Calendar.Validation.StartDateCannotBePast"));
        }

        if (request.EndDate.HasValue && request.EndDate < request.StartDate)
        {
            validationErrors.Add(_localizer.Get("Calendar.Validation.EndDateMustBeAfterStartDate"));
        }

        if (validationErrors.Any())
        {
            response.Success = false;
            response.Errors = validationErrors;
            return Result<UpdateCalendarNoteResponse>.Validation(new ValidationError(
                validationErrors.Select(err => new Error("ValidationError", err, ErrorType.Validation)).ToArray()),
                response);
        }

        // Not bilgilerini güncelle
        note.Title = request.Title;
        note.Description = request.Description;
        note.StartDate = request.StartDate;
        note.EndDate = request.EndDate;
        note.Type = request.Type;
        note.Visibility = request.Visibility;
        note.RelatedCustomerId = request.RelatedCustomerId;
        note.AssignedUserId = request.AssignedUserId;
        note.IsRecurring = request.IsRecurring;
        note.IsImportant = request.IsImportant;
        note.IsAllDay = request.IsAllDay;
        note.IsDone = request.IsDone;
        note.IsCancelled = request.IsCancelled;
        note.UpdateDate = DateTime.UtcNow;
        note.UpdateUserId = _workContext.UserId;
        note.RelatedCompanyId = request.RelatedCompanyId;

        // Reminder'ları güncelle
        await UpdateReminders(note, request.Reminders, cancellationToken);

        // Attendee'leri güncelle
        if (request.AttendeeUserIds != null)
        {
            await UpdateAttendees(note, request.AttendeeUserIds, cancellationToken);
        }

        // Tag'leri güncelle
        if (request.TagNames != null)
        {
            await UpdateTags(note, request.TagNames, cancellationToken);
        }

        // Departmanları güncelle
        if (request.DepartmentIds != null)
        {
            await UpdateDepartments(note, request.DepartmentIds, cancellationToken);
        }

        await _dbContext.SaveChangesAsync(cancellationToken);

        _logger.LogInformation(
            "Calendar note updated by UserId: {UserId}. Note: {NoteId}, Title: {Title}",
            _workContext.UserId,
            note.Id,
            note.Title);

        response.Success = true;
        response.NoteId = note.Id;
        response.Message = _localizer.Get("Calendar.Note.UpdateSuccess");

        return Result.Success(response);
    }

    private async Task UpdateReminders(CalendarNote note, List<UpdateReminderDto> reminderDtos, CancellationToken cancellationToken)
    {
        // Silinecek reminder'ları işaretle
        var remindersToDelete = reminderDtos.Where(r => r.Id.HasValue && r.IsDeleted).Select(r => r.Id!.Value).ToList();
        var existingRemindersToDelete = note.Reminders.Where(r => remindersToDelete.Contains(r.Id)).ToList();
        
        foreach (var reminder in existingRemindersToDelete)
        {
            _dbContext.CalendarReminders.Remove(reminder);
        }

        // Mevcut reminder'ları güncelle
        var remindersToUpdate = reminderDtos.Where(r => r.Id.HasValue && !r.IsDeleted).ToList();
        foreach (var reminderDto in remindersToUpdate)
        {
            var existingReminder = note.Reminders.FirstOrDefault(r => r.Id == reminderDto.Id!.Value);
            if (existingReminder != null)
            {
                existingReminder.MinutesBefore = reminderDto.MinutesBefore;
                existingReminder.Channel = reminderDto.Channel;
            }
        }

        // Yeni reminder'ları ekle
        var newReminders = reminderDtos.Where(r => !r.Id.HasValue).ToList();
        foreach (var reminderDto in newReminders)
        {
            var newReminder = new CalendarReminder
            {
                Id = Guid.NewGuid(),
                CalendarNoteId = note.Id,
                MinutesBefore = reminderDto.MinutesBefore,
                Channel = reminderDto.Channel,
                IsSent = false
            };
            await _dbContext.CalendarReminders.AddAsync(newReminder, cancellationToken);
        }
    }

    private async Task UpdateAttendees(CalendarNote note, List<Guid> attendeeUserIds, CancellationToken cancellationToken)
    {
        // Mevcut attendee'leri sil
        var existingAttendees = note.Attendees.ToList();
        foreach (var attendee in existingAttendees)
        {
            _dbContext.CalendarNoteAttendees.Remove(attendee);
        }

        // Yeni attendee'leri ekle
        foreach (var userId in attendeeUserIds)
        {
            var attendee = new CalendarNoteAttendee
            {
                Id = Guid.NewGuid(),
                CalendarNoteId = note.Id,
                UserId = userId,
                IsOrganizer = userId == note.CreatedByUserId,
                Status = AttendanceStatus.Pending
            };
            await _dbContext.CalendarNoteAttendees.AddAsync(attendee, cancellationToken);
        }
    }

    private async Task UpdateTags(CalendarNote note, List<string> tagNames, CancellationToken cancellationToken)
    {
        // Mevcut tag'leri sil
        var existingTags = note.Tags.ToList();
        foreach (var tag in existingTags)
        {
            _dbContext.CalendarNoteTags.Remove(tag);
        }

        // Yeni tag'leri ekle
        var tags = new List<CalendarNoteTag>();
        var newTags = new List<CalendarTag>();

        foreach (var tagName in tagNames)
        {
            var existingTag = await _dbContext.CalendarTags
                .FirstOrDefaultAsync(t => t.Name.ToLower() == tagName.ToLower(), cancellationToken);

            if (existingTag == null)
            {
                existingTag = new CalendarTag
                {
                    Id = Guid.NewGuid(),
                    Name = tagName
                };
                newTags.Add(existingTag);
            }

            tags.Add(new CalendarNoteTag
            {
                CalendarNoteId = note.Id,
                TagId = existingTag.Id,
                Tag = existingTag
            });
        }

        if (newTags.Any())
        {
            await _dbContext.CalendarTags.AddRangeAsync(newTags, cancellationToken);
        }

        note.Tags = tags;
    }

    private async Task UpdateDepartments(CalendarNote note, List<Guid> departmentIds, CancellationToken cancellationToken)
    {
        // Mevcut departmanları sil
        var existingDepartments = note.Departments.ToList();
        foreach (var dept in existingDepartments)
        {
            _dbContext.CalendarNoteDepartments.Remove(dept);
        }

        // Yeni departmanları ekle
        var newDepartments = departmentIds.Select(departmentId => new CalendarNoteDepartment
        {
            Id = Guid.NewGuid(),
            CalendarNoteId = note.Id,
            DepartmentId = departmentId
        }).ToList();

        await _dbContext.CalendarNoteDepartments.AddRangeAsync(newDepartments, cancellationToken);
        note.Departments = newDepartments;
    }
}
