using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Calendar.Application.Abstractions;
using Calendar.Domain;
using Shared.Application;
using Shared.Contracts;
using Shared.Infrastructure.Localization;

namespace Calendar.Application.CalendarNotes.Delete;

public class DeleteCalendarNoteCommandHandler : IRequestHandler<DeleteCalendarNoteCommand, Result>
{
    private readonly ICalendarDbContext _dbContext;
    private readonly ILogger<DeleteCalendarNoteCommandHandler> _logger;
    private readonly IWorkContext _workContext;
    private readonly ILocalizer _localizer;

    public DeleteCalendarNoteCommandHandler(
        ICalendarDbContext dbContext,
        ILogger<DeleteCalendarNoteCommandHandler> logger,
        IWorkContext workContext,
        ILocalizer localizer)
    {
        _dbContext = dbContext;
        _logger = logger;
        _workContext = workContext;
        _localizer = localizer;
    }

    public async Task<Result> Handle(DeleteCalendarNoteCommand request, CancellationToken cancellationToken)
    {
        var note = await _dbContext.CalendarNotes
            .FirstOrDefaultAsync(n => n.Id == request.Id, cancellationToken);

        if (note == null)
        {
            return Result.Failure("Calendar.Note.NotFound", _localizer.Get("Calendar.Note.NotFound"));
        }

        if (note.CreatedByUserId != _workContext.UserId && !_workContext.HasRole("Admin") )
        {
            return Result.Failure("Calendar.Note.Unauthorized", _localizer.Get("Calendar.Note.Unauthorized"));
        }
 
        note.IsDeleted = true;
        note.DeletedDate = DateTime.UtcNow;
        note.DeletedByUserId = _workContext.UserId;

        await _dbContext.SaveChangesAsync(cancellationToken);

        _logger.LogInformation(
            "Calendar note deleted by UserId: {UserId}. Note: {NoteId}, Title: {Title}",
            _workContext.UserId,
            note.Id,
            note.Title);

        return Result.Success();
    }
}
