using FluentValidation;
using Shared.Infrastructure.Localization;

namespace Calendar.Application.CalendarNotes.Delete;

public class DeleteCalendarNoteCommandValidator : AbstractValidator<DeleteCalendarNoteCommand>
{
    private readonly ILocalizer _localizer;

    public DeleteCalendarNoteCommandValidator(ILocalizer localizer)
    {
        _localizer = localizer;

        RuleFor(x => x.Id)
            .NotEmpty().WithMessage(_localizer.Get("Calendar.Note.IdRequired"));
    }
}
