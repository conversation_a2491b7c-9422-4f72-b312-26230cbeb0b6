using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Calendar.Domain;
using Shared.Endpoints;

namespace Calendar.Application.CalendarNotes.Common;

internal sealed class GetCalendarEnumsEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapGet("/api/v1/calendar/enums", () =>
        {
            var result = new CalendarEnumsResponse
            {
                RecurrenceTypes = Enum.GetValues<RecurrenceType>()
                    .Select(x => new EnumValueDto { Value = (int)x, Name = x.ToString() })
                    .ToArray(),

                ReminderChannels = Enum.GetValues<ReminderChannel>()
                    .Select(x => new EnumValueDto { Value = (int)x, Name = x.ToString() })
                    .ToArray(),

                CalendarNoteTypes = Enum.GetValues<CalendarNoteType>()
                    .Select(x => new EnumValueDto { Value = (int)x, Name = x.ToString() })
                    .ToArray(),

                CalendarVisibilities = Enum.GetValues<CalendarVisibility>()
                    .Select(x => new EnumValueDto { Value = (int)x, Name = x.ToString() })
                    .ToArray(),

                AttendanceStatuses = Enum.GetValues<AttendanceStatus>()
                    .Select(x => new EnumValueDto { Value = (int)x, Name = x.ToString() })
                    .ToArray()
            };

            return Results.Ok(result);
        })
        .WithTags("Calendar.Notes")
        .WithGroupName("apiv1")
        .Produces<CalendarEnumsResponse>(StatusCodes.Status200OK)
        .AllowAnonymous();
    }
}

public class CalendarEnumsResponse
{
    public EnumValueDto[] RecurrenceTypes { get; set; }
    public EnumValueDto[] ReminderChannels { get; set; }
    public EnumValueDto[] CalendarNoteTypes { get; set; }
    public EnumValueDto[] CalendarVisibilities { get; set; }
    public EnumValueDto[] AttendanceStatuses { get; set; }
}

public class EnumValueDto
{
    public int Value { get; set; }
    public string Name { get; set; }
}
