using FluentValidation;
using Shared.Infrastructure.Localization;

namespace Calendar.Application.CalendarNotes.List;

public class ListCalendarNotesQueryValidator : AbstractValidator<ListCalendarNotesQuery>
{
    private readonly ILocalizer _localizer;

    public ListCalendarNotesQueryValidator(ILocalizer localizer)
    {
        _localizer = localizer;

        RuleFor(x => x.PageNumber)
            .GreaterThan(0).WithMessage(_localizer.Get("Calendar.Note.PageNumberInvalid"));

        RuleFor(x => x.PageSize)
            .GreaterThan(0).WithMessage(_localizer.Get("Calendar.Note.PageSizeInvalid"))
            .LessThanOrEqualTo(100).WithMessage(_localizer.Get("Calendar.Note.PageSizeMaxLimit"));

        RuleFor(x => x.StartDate)
            .LessThanOrEqualTo(x => x.EndDate)
            .When(x => x.StartDate.HasValue && x.EndDate.HasValue)
            .WithMessage(_localizer.Get("Calendar.Note.DateRangeInvalid"));

        RuleFor(x => x.SortDirection)
            .Must(x => string.IsNullOrEmpty(x) || x.ToLower() == "asc" || x.ToLower() == "desc")
            .WithMessage(_localizer.Get("Calendar.Note.SortDirectionInvalid"));
    }
}
