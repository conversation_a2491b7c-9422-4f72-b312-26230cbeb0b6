using Calendar.Domain;

namespace Calendar.Application.CalendarNotes.List;

public class CalendarNoteDto
{
    public Guid Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public DateTime StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public CalendarNoteType Type { get; set; }
    public CalendarVisibility Visibility { get; set; }
    public Guid? CreatedByUserId { get; set; }
    public string? CreatedByUserName { get; set; }
    public Guid? AssignedUserId { get; set; }
    public string? AssignedUserName { get; set; }
    public List<CalendarNoteDepartmentDto> Departments { get; set; } = new();
    public Guid? RelatedCustomerId { get; set; }
    public string? RelatedCustomerName { get; set; }
    public bool IsRecurring { get; set; }
    public bool IsDone { get; set; }
    public bool IsImportant { get; set; }
    public bool IsAllDay { get; set; }
    public bool IsCancelled { get; set; }
    public DateTime InsertDate { get; set; }
    public List<CalendarNoteTagDto> Tags { get; set; } = new();
    public List<CalendarReminderDto> Reminders { get; set; } = new();
    public List<CalendarNoteAttendeeDto> Attendees { get; set; } = new();
}

public class CalendarNoteTagDto
{
    public Guid TagId { get; set; }
    public string TagName { get; set; } = string.Empty;
}

public class CalendarReminderDto
{
    public Guid Id { get; set; }
    public int MinutesBefore { get; set; }
    public ReminderChannel Channel { get; set; }
    public bool IsSent { get; set; }
    public DateTime? SentAt { get; set; }
}

public class CalendarNoteAttendeeDto
{
    public Guid Id { get; set; }
    public Guid UserId { get; set; }
    public string UserName { get; set; } = string.Empty;
    public bool IsOrganizer { get; set; }
    public AttendanceStatus Status { get; set; }
}

public class CalendarNoteDepartmentDto
{
    public Guid DepartmentId { get; set; }
    public string DepartmentName { get; set; } = string.Empty;
}
