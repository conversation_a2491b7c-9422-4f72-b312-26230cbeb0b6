using Calendar.Application.Abstractions;
using Calendar.Domain;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;
using Shared.Contracts;
using Shared.Infrastructure.Localization;

namespace Calendar.Application.CalendarNotes.List;

public class ListCalendarNotesQueryHandler : IRequestHandler<ListCalendarNotesQuery, Result<PagedResult<CalendarNoteDto>>>
{
    private readonly ICalendarDbContext _dbContext;
    private readonly IWorkContext _workContext;
    private readonly ISharedUserService _userService;
    private readonly ISharedDepartmentService _departmentService;
    private readonly ISharedCustomerService _customerService;
    private readonly ILocalizer _localizer;

    public ListCalendarNotesQueryHandler(
        ICalendarDbContext dbContext,
        IWorkContext workContext,
        ISharedUserService userService,
        ISharedDepartmentService departmentService,
        ISharedCustomerService customerService,
        ILocalizer localizer)
    {
        _dbContext = dbContext;
        _workContext = workContext;
        _userService = userService;
        _departmentService = departmentService;
        _customerService = customerService;
        _localizer = localizer;
    }

    public async Task<Result<PagedResult<CalendarNoteDto>>> Handle(ListCalendarNotesQuery request, CancellationToken cancellationToken)
    {
        var query = _dbContext.CalendarNotes
            .Include(n => n.Tags).ThenInclude(t => t.Tag)
            .Include(n => n.Reminders)
            .Include(n => n.Attendees)
            .Include(n => n.Departments)
            .Where(n => !n.IsDeleted)
            .AsQueryable();

        // Authorization kontrolü
        if (!_workContext.HasRole("Admin"))
        {
            // Kullanıcının departmanlarını al
            var currentUserResult = await _userService.GetUserAsync(_workContext.UserId);
            if (!currentUserResult.IsSuccess || currentUserResult.Value.DepartmentIds == null)
            {
                // Kullanıcı bulunamazsa veya departmanı yoksa sadece kendi notlarını görebilir
                query = query.Where(n =>
                    n.CreatedByUserId == _workContext.UserId ||
                    n.AssignedUserId == _workContext.UserId ||
                    n.Attendees.Any(a => a.UserId == _workContext.UserId));
            }
            else
            {
                var userDepartmentIds = currentUserResult.Value.DepartmentIds;

                // Admin değilse sadece kendi notlarını, departmansal notları ve public notları görebilir
                query = query.Where(n =>
                    n.CreatedByUserId == _workContext.UserId || // Kendi oluşturduğu notlar
                    n.AssignedUserId == _workContext.UserId || // Kendisine atanan notlar
                    n.Visibility == CalendarVisibility.Departmental && n.Departments.Any(d => userDepartmentIds.Contains(d.DepartmentId)) || // Departman notları
                    n.Visibility == CalendarVisibility.Public || // Public notlar
                    n.Attendees.Any(a => a.UserId == _workContext.UserId)); // Katılımcı olduğu notlar
            }
        }

        // Filtreleme - Performans için varsayılan olarak aktif ayın kayıtlarını getir
        if (request.StartDate.HasValue || request.EndDate.HasValue)
        {
            // Kullanıcı tarih aralığı belirtmişse, o aralığı kullan
            if (request.StartDate.HasValue)
            {
                query = query.Where(n => n.StartDate >= request.StartDate.Value);
            }

            if (request.EndDate.HasValue)
            {
                query = query.Where(n => n.StartDate <= request.EndDate.Value);
            }
        }
        else
        {
            // Tarih aralığı belirtilmemişse, aktif ayın kayıtlarını getir (performans optimizasyonu)
            var currentDate = DateTime.UtcNow;
            var monthStart = new DateTime(currentDate.Year, currentDate.Month, 1);
            var monthEnd = monthStart.AddMonths(1).AddSeconds(-1); // Ayın son saniyesi

            query = query.Where(n => n.StartDate >= monthStart && n.StartDate <= monthEnd);
        }

        var parsedUserIds = request.ParsedUserIds;
        if (parsedUserIds.Any())
        {
            query = query.Where(n =>
                n.CreatedByUserId.HasValue && parsedUserIds.Contains(n.CreatedByUserId.Value) ||
                n.AssignedUserId.HasValue && parsedUserIds.Contains(n.AssignedUserId.Value) ||
                n.Attendees.Any(a => parsedUserIds.Contains(a.UserId)));
        }

        var parsedDepartmentIds = request.ParsedDepartmentIds;
        if (parsedDepartmentIds.Any())
        {
            query = query.Where(n => n.Departments.Any(d => parsedDepartmentIds.Contains(d.DepartmentId)));
        }

        var parsedTagNames = request.ParsedTagNames;
        if (parsedTagNames.Any())
        {
            query = query.Where(n => n.Tags.Any(t =>
                parsedTagNames.Any(tagName =>
                    t.Tag.Name.ToLower().Contains(tagName.ToLower()))));
        }

        if (!string.IsNullOrWhiteSpace(request.Title))
        {
            query = query.Where(n => n.Title.Contains(request.Title));
        }

        var parsedTypes = request.ParsedTypes;
        if (parsedTypes.Any())
        {
            query = query.Where(n => parsedTypes.Contains(n.Type));
        }

        if (request.Visibility.HasValue)
        {
            query = query.Where(n => n.Visibility == request.Visibility.Value);
        }

        if (request.IsImportant.HasValue)
        {
            query = query.Where(n => n.IsImportant == request.IsImportant.Value);
        }

        if (request.IsDone.HasValue)
        {
            query = query.Where(n => n.IsDone == request.IsDone.Value);
        }

        if (request.IsRecurring.HasValue)
        {
            query = query.Where(n => n.IsRecurring == request.IsRecurring.Value);
        }

        if (request.IsCancelled.HasValue)
        {
            query = query.Where(n => n.IsCancelled == request.IsCancelled.Value);
        }

        // Filter by attendees
        var parsedAttendeeUserIds = request.ParsedAttendeeUserIds;
        if (parsedAttendeeUserIds.Any())
        {
            query = query.Where(n => n.Attendees.Any(a => parsedAttendeeUserIds.Contains(a.UserId)));
        }

        // Filter by CreatedByUserId
        if (request.CreatedByUserId.HasValue)
        {
            query = query.Where(n => n.CreatedByUserId == request.CreatedByUserId.Value);
        }

        // Filter by RelatedCustomerId
        if (request.RelatedCustomerId.HasValue)
        {
            query = query.Where(n => n.RelatedCustomerId == request.RelatedCustomerId.Value);
        }

        // Filter by RelatedCompanyId
        if (request.RelatedCompanyId.HasValue)
        {
            query = query.Where(n => n.RelatedCompanyId == request.RelatedCompanyId.Value);
        }

        query = ApplySorting(query, request.SortBy, request.SortDirection);

        var totalCount = await query.CountAsync(cancellationToken);
        var items = await query
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .ToListAsync(cancellationToken);

        var dtos = await MapToDto(items, cancellationToken);

        var result = new PagedResult<CalendarNoteDto>(dtos)
        {
            PageNumber = request.PageNumber,
            PageSize = request.PageSize,
            Count = totalCount,
            FilteredCount = totalCount
        };

        return Result.Success(result);
    }

    private static IQueryable<CalendarNote> ApplySorting(IQueryable<CalendarNote> query, string? sortBy, string? sortDirection)
    {
        var isDescending = sortDirection?.ToLower() == "desc";

        return sortBy?.ToLower() switch
        {
            "title" => isDescending ? query.OrderByDescending(n => n.Title) : query.OrderBy(n => n.Title),
            "type" => isDescending ? query.OrderByDescending(n => n.Type) : query.OrderBy(n => n.Type),
            "insertdate" => isDescending ? query.OrderByDescending(n => n.InsertDate) : query.OrderBy(n => n.InsertDate),
            "enddate" => isDescending ? query.OrderByDescending(n => n.EndDate) : query.OrderBy(n => n.EndDate),
            _ => isDescending ? query.OrderByDescending(n => n.StartDate) : query.OrderBy(n => n.StartDate)
        };
    }

    private async Task<List<CalendarNoteDto>> MapToDto(List<CalendarNote> notes, CancellationToken cancellationToken)
    {
        // Kullanıcı bilgilerini toplu olarak al
        var userIds = notes.SelectMany(n => new[] { n.CreatedByUserId, n.AssignedUserId }.Where(id => id.HasValue).Select(id => id!.Value))
            .Concat(notes.SelectMany(n => n.Attendees.Select(a => a.UserId)))
            .Distinct().ToList();

        var users = userIds.Any() ? (await _userService.GetUsersByIdsAsync(userIds)).ToDictionary(u => u.Id, u => $"{u.Name} {u.Surname}") : [];

        // Departman bilgilerini toplu olarak al (sadece ID'ler için)
        var departmentIds = notes.SelectMany(n => n.Departments.Select(d => d.DepartmentId)).Distinct().ToList();
        var departments = departmentIds.Any() ? (await _departmentService.GetDepartmentsByIdsAsync(departmentIds)).ToDictionary(d => d.Id, d => d.Name) : [];

        // Müşteri bilgilerini toplu olarak al
        var customerIds = notes.Where(n => n.RelatedCustomerId.HasValue).Select(n => n.RelatedCustomerId!.Value).Distinct().ToList();
        var customers = customerIds.Any() ? (await _customerService.GetCustomerByIdsAsync(customerIds)).Value.ToDictionary(c => c.Id, c => $"{c.Name} {c.Surname}") : [];

        return notes.Select(note => new CalendarNoteDto
        {
            Id = note.Id,
            Title = note.Title,
            Description = note.Description,
            StartDate = note.StartDate,
            EndDate = note.EndDate,
            Type = note.Type,
            Visibility = note.Visibility,
            CreatedByUserId = note.CreatedByUserId,
            CreatedByUserName = note.CreatedByUserId.HasValue && users.TryGetValue(note.CreatedByUserId.Value, out var createdBy) ? createdBy : null,
            AssignedUserId = note.AssignedUserId,
            AssignedUserName = note.AssignedUserId.HasValue && users.TryGetValue(note.AssignedUserId.Value, out var assigned) ? assigned : null,
            Departments = note.Departments.Select(d => new CalendarNoteDepartmentDto
            {
                DepartmentId = d.DepartmentId,
                DepartmentName = departments.TryGetValue(d.DepartmentId, out var deptName) ? deptName : _localizer.Get("Calendar.UnknownDepartment")
            }).ToList(),
            RelatedCustomerId = note.RelatedCustomerId,
            RelatedCustomerName = note.RelatedCustomerId.HasValue && customers.TryGetValue(note.RelatedCustomerId.Value, out var customer) ? customer : null,
            IsRecurring = note.IsRecurring,
            IsDone = note.IsDone,
            IsImportant = note.IsImportant,
            IsAllDay = note.IsAllDay,
            IsCancelled = note.IsCancelled,
            InsertDate = note.InsertDate,
            Tags = note.Tags.Select(t => new CalendarNoteTagDto
            {
                TagId = t.TagId,
                TagName = t.Tag.Name
            }).ToList(),
            Reminders = note.Reminders.Select(r => new CalendarReminderDto
            {
                Id = r.Id,
                MinutesBefore = r.MinutesBefore,
                Channel = r.Channel,
                IsSent = r.IsSent,
                SentAt = r.SentAt
            }).ToList(),
            Attendees = note.Attendees.Select(a => new CalendarNoteAttendeeDto
            {
                Id = a.Id,
                UserId = a.UserId,
                UserName = users.TryGetValue(a.UserId, out var attendeeName) ? attendeeName : _localizer.Get("Calendar.UnknownUser"),
                IsOrganizer = a.IsOrganizer,
                Status = a.Status
            }).ToList()
        }).ToList();
    }
}
