using MediatR;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using Calendar.Application.Abstractions;
using Shared.Application;
using Shared.Contracts;
using Shared.Infrastructure.Localization;

namespace Calendar.Application.CalendarTags.Delete;

public class DeleteCalendarTagCommandHandler : IRequestHandler<DeleteCalendarTagCommand, Result>
{
    private readonly ICalendarDbContext _dbContext;
    private readonly ILogger<DeleteCalendarTagCommandHandler> _logger;
    private readonly IWorkContext _workContext;
    private readonly ILocalizer _localizer;

    public DeleteCalendarTagCommandHandler(
        ICalendarDbContext dbContext,
        ILogger<DeleteCalendarTagCommandHandler> logger,
        IWorkContext workContext,
        ILocalizer localizer)
    {
        _dbContext = dbContext;
        _logger = logger;
        _workContext = workContext;
        _localizer = localizer;
    }

    public async Task<Result> Handle(DeleteCalendarTagCommand request, CancellationToken cancellationToken)
    {
        var tag = await _dbContext.CalendarTags
            .FirstOrDefaultAsync(t => t.Id == request.Id, cancellationToken);

        if (tag == null)
        {
            return Result.Failure(Error.NotFound("Calendar.TagNotFound", _localizer.Get("Calendar.TagNotFound")));
        }

        var isInUse = await _dbContext.CalendarNoteTags
            .AnyAsync(nt => nt.TagId == request.Id, cancellationToken);

        if (isInUse)
        {
            return Result.Failure(_localizer.Get("Calendar.TagInUse"));
        }

        _dbContext.CalendarTags.Remove(tag);
        await _dbContext.SaveChangesAsync(cancellationToken);

        _logger.LogInformation(
            "Calendar tag deleted by UserId: {UserId}. Tag: {TagId}, Name: {TagName}",
            _workContext.UserId,
            tag.Id,
            tag.Name);

        return Result.Success();
    }
}
