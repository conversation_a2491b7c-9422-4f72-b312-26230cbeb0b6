using FluentValidation;
using Calendar.Application.Abstractions;
using Microsoft.EntityFrameworkCore;
using Shared.Infrastructure.Localization;

namespace Calendar.Application.CalendarTags.Delete;

public class DeleteCalendarTagCommandValidator : AbstractValidator<DeleteCalendarTagCommand>
{
    private readonly ICalendarDbContext _dbContext;
    private readonly ILocalizer _localizer;

    public DeleteCalendarTagCommandValidator(ICalendarDbContext dbContext, ILocalizer localizer)
    {
        _dbContext = dbContext;
        _localizer = localizer;

        RuleFor(x => x.Id)
            .NotEmpty().WithMessage(_localizer.Get("Calendar.Tag.IdRequired"))
            .MustAsync(BeExistingTag).WithMessage(_localizer.Get("Calendar.Tag.NotFound"))
            .MustAsync(NotBeInUse).WithMessage(_localizer.Get("Calendar.Tag.InUse"));
    }

    private async Task<bool> BeExistingTag(Guid id, CancellationToken cancellationToken)
    {
        return await _dbContext.CalendarTags
            .AnyAsync(t => t.Id == id, cancellationToken);
    }

    private async Task<bool> NotBeInUse(Guid id, CancellationToken cancellationToken)
    {
        return !await _dbContext.CalendarNoteTags
            .AnyAsync(nt => nt.TagId == id, cancellationToken);
    }
}
