using FluentValidation;
using Calendar.Application.Abstractions;
using Microsoft.EntityFrameworkCore;
using Shared.Infrastructure.Localization;

namespace Calendar.Application.CalendarTags.Create;

public class CreateCalendarTagCommandValidator : AbstractValidator<CreateCalendarTagCommand>
{
    private readonly ICalendarDbContext _dbContext;
    private readonly ILocalizer _localizer;

    public CreateCalendarTagCommandValidator(ICalendarDbContext dbContext, ILocalizer localizer)
    {
        _dbContext = dbContext;
        _localizer = localizer;

        RuleFor(x => x.Name)
            .NotEmpty().WithMessage(_localizer.Get("Calendar.Tag.NameRequired"))
            .MaximumLength(50).WithMessage(_localizer.Get("Calendar.Tag.NameMaxLength"))
            .MustAsync(BeUniqueName).WithMessage(_localizer.Get("Calendar.Tag.NameUnique"));
    }

    private async Task<bool> BeUniqueName(string name, CancellationToken cancellationToken)
    {
        return !await _dbContext.CalendarTags
            .AnyAsync(t => t.Name.ToLower() == name.ToLower(), cancellationToken);
    }
}
