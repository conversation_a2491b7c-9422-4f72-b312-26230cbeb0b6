using MediatR;
using Microsoft.Extensions.Logging;
using Calendar.Application.Abstractions;
using Calendar.Domain;
using Shared.Application;
using Shared.Contracts;
using Microsoft.EntityFrameworkCore;
using Shared.Infrastructure.Localization;

namespace Calendar.Application.CalendarTags.Create;

public class CreateCalendarTagCommandHandler : IRequestHandler<CreateCalendarTagCommand, Result<CreateCalendarTagResponse>>
{
    private readonly ICalendarDbContext _dbContext;
    private readonly ILogger<CreateCalendarTagCommandHandler> _logger;
    private readonly IWorkContext _workContext;
    private readonly ILocalizer _localizer;

    public CreateCalendarTagCommandHandler(
        ICalendarDbContext dbContext,
        ILogger<CreateCalendarTagCommandHandler> logger,
        IWorkContext workContext,
        ILocalizer localizer)
    {
        _dbContext = dbContext;
        _logger = logger;
        _workContext = workContext;
        _localizer = localizer;
    }

    public async Task<Result<CreateCalendarTagResponse>> Handle(CreateCalendarTagCommand request, CancellationToken cancellationToken)
    {
        var response = new CreateCalendarTagResponse();
        var validationErrors = new List<string>();

        var existingTag = await _dbContext.CalendarTags
            .FirstOrDefaultAsync(t => t.Name.ToLower() == request.Name.ToLower(), cancellationToken);
        
        if (existingTag != null)
        {
            validationErrors.Add(_localizer.Get("Calendar.TagAlreadyExists", new { request.Name }));
        }

        if (validationErrors.Any())
        {
            response.Success = false;
            response.Errors = validationErrors;
            return Result<CreateCalendarTagResponse>.Validation(new ValidationError(
                validationErrors.Select(err => new Error("ValidationError", err, ErrorType.Validation)).ToArray()),
                response);
        }

        var tag = new CalendarTag
        {
            Id = Guid.NewGuid(),
            Name = request.Name
        };

        await _dbContext.CalendarTags.AddAsync(tag, cancellationToken);
        await _dbContext.SaveChangesAsync(cancellationToken);

        _logger.LogInformation(
            "Calendar tag created by UserId: {UserId}. Tag: {TagName}, Id: {TagId}",
            _workContext.UserId,
            tag.Name,
            tag.Id);

        response.Success = true;
        response.TagId = tag.Id;
        
        return Result.Success(response);
    }
}

