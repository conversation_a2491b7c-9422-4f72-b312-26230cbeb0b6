using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Calendar.Application.CalendarTags.Create;

internal sealed class CreateCalendarTagEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPost("/api/v1/calendar/tags", async (
            CreateCalendarTagCommand command,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var result = await mediator.Send(command, cancellationToken);
            return result.Match(
                tagId => Results.Created($"/api/v1/calendar/tags/{tagId}", new { tagId }),
                CustomResults.Problem);
        })
        .WithTags("Calendar.Tags")
        .WithGroupName("apiv1")
        .Produces<Guid>(StatusCodes.Status201Created)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .RequireAuthorization("Calendar.Management");
    }
}

