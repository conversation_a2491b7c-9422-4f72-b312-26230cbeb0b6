using MediatR;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using Calendar.Application.Abstractions;
using Shared.Application;
using Shared.Contracts;
using Shared.Infrastructure.Localization;

namespace Calendar.Application.CalendarTags.Update;

public class UpdateCalendarTagCommandHandler : IRequestHandler<UpdateCalendarTagCommand, Result>
{
    private readonly ICalendarDbContext _dbContext;
    private readonly ILogger<UpdateCalendarTagCommandHandler> _logger;
    private readonly IWorkContext _workContext;
    private readonly ILocalizer _localizer;

    public UpdateCalendarTagCommandHandler(
        ICalendarDbContext dbContext,
        ILogger<UpdateCalendarTagCommandHandler> logger,
        IWorkContext workContext,
        ILocalizer localizer)
    {
        _dbContext = dbContext;
        _logger = logger;
        _workContext = workContext;
        _localizer = localizer;
    }

    public async Task<Result> Handle(UpdateCalendarTagCommand request, CancellationToken cancellationToken)
    {
        var tag = await _dbContext.CalendarTags
            .FirstOrDefaultAsync(t => t.Id == request.Id, cancellationToken);

        if (tag == null)
        {
            return Result.Failure(Error.NotFound("Calendar.TagNotFound", _localizer.Get("Calendar.TagNotFound")));
        }

        var oldName = tag.Name;
        tag.Name = request.Name;

        await _dbContext.SaveChangesAsync(cancellationToken);

        _logger.LogInformation(
            "Calendar tag updated by UserId: {UserId}. Tag: {TagId}, Old Name: {OldName}, New Name: {NewName}",
            _workContext.UserId,
            tag.Id,
            oldName,
            tag.Name);

        return Result.Success();
    }
}
