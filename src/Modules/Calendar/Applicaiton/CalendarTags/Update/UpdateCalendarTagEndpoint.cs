using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Application;
using Shared.Endpoints;

namespace Calendar.Application.CalendarTags.Update;

internal sealed class UpdateCalendarTagEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPut("/api/v1/calendar/tags/{id}", async (
            Guid id,
            UpdateCalendarTagCommand command,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            if (id != command.Id)
            {
                command = command with { Id = id };
            }
            var result = await mediator.Send(command, cancellationToken);
            return result.Match(Results.NoContent, CustomResults.Problem);
        })
        .WithTags("Calendar.Tags")
        .WithGroupName("apiv1")
        .Produces<Result>(StatusCodes.Status204NoContent)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .RequireAuthorization("Calendar.Management");
    }
}
