using FluentValidation;
using Calendar.Application.Abstractions;
using Microsoft.EntityFrameworkCore;
using Shared.Infrastructure.Localization;

namespace Calendar.Application.CalendarTags.Update;

public class UpdateCalendarTagCommandValidator : AbstractValidator<UpdateCalendarTagCommand>
{
    private readonly ICalendarDbContext _dbContext;
    private readonly ILocalizer _localizer;

    public UpdateCalendarTagCommandValidator(ICalendarDbContext dbContext, ILocalizer localizer)
    {
        _dbContext = dbContext;
        _localizer = localizer;

        RuleFor(x => x.Id)
            .NotEmpty().WithMessage(_localizer.Get("Calendar.Tag.IdRequired"))
            .MustAsync(BeExistingTag).WithMessage(_localizer.Get("Calendar.Tag.NotFound"));

        RuleFor(x => x.Name)
            .NotEmpty().WithMessage(_localizer.Get("Calendar.Tag.NameRequired"))
            .MaximumLength(50).WithMessage(_localizer.Get("Calendar.Tag.NameMaxLength"))
            .MustAsync(BeUniqueName).WithMessage(_localizer.Get("Calendar.Tag.NameUnique"));
    }

    private async Task<bool> BeExistingTag(Guid id, CancellationToken cancellationToken)
    {
        return await _dbContext.CalendarTags
            .AnyAsync(t => t.Id == id, cancellationToken);
    }

    private async Task<bool> BeUniqueName(UpdateCalendarTagCommand command, string name, CancellationToken cancellationToken)
    {
        return !await _dbContext.CalendarTags
            .AnyAsync(t => t.Name.ToLower() == name.ToLower() && t.Id != command.Id, cancellationToken);
    }
}
