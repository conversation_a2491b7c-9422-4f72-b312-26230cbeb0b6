using MediatR;
using Microsoft.EntityFrameworkCore;
using Calendar.Application.Abstractions;
using Shared.Application;

namespace Calendar.Application.CalendarTags.List;

public class ListCalendarTagsQueryHandler : IRequestHandler<ListCalendarTagsQuery, Result<PagedResult<CalendarTagDto>>>
{
    private readonly ICalendarDbContext _dbContext;

    public ListCalendarTagsQueryHandler(ICalendarDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<Result<PagedResult<CalendarTagDto>>> <PERSON>le(ListCalendarTagsQuery request, CancellationToken cancellationToken)
    {
        var query = _dbContext.CalendarTags.AsQueryable();

        if (!string.IsNullOrWhiteSpace(request.SearchTerm))
        {
            var searchTerm = request.SearchTerm.ToLower();
            query = query.Where(t => t.Name.ToLower().Contains(searchTerm));
        }

        var totalCount = await query.CountAsync(cancellationToken);

        var tags = await query
            .OrderBy(t => t.Name)
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .Select(t => new CalendarTagDto
            {
                Id = t.Id,
                Name = t.Name,
                UsageCount = t.NoteTags.Count
            })
            .ToListAsync(cancellationToken);

        var result = PagedResult<CalendarTagDto>.Success(tags);
        result.PageNumber = request.PageNumber;
        result.PageSize = request.PageSize;
        result.Count = totalCount;
        result.FilteredCount = totalCount;

        return Result.Success(result);
    }
}
