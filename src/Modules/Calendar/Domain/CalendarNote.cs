using Shared.Domain;

namespace Calendar.Domain;

public class CalendarNote : AuditableEntity
{
    public Guid Id { get; set; }
    public string Title { get; set; }
    public string Description { get; set; }

    public DateTime StartDate { get; set; }
    public DateTime? EndDate { get; set; }

    public CalendarNoteType Type { get; set; } // CallAuto, Meeting, Task vs.
    public CalendarVisibility Visibility { get; set; } // Personal, Departmental, Public

    public Guid? CreatedByUserId { get; set; }
    public Guid? AssignedUserId { get; set; }

    public Guid? RelatedCustomerId { get; set; }
    public Guid? RelatedCompanyId { get; set; }

    public bool IsRecurring { get; set; }
    public RecurrenceRule? RecurrenceRule { get; set; }

    public ICollection<CalendarReminder> Reminders { get; set; } = [];
    public ICollection<CalendarNoteFile> Files { get; set; } = [];

    public ICollection<CalendarNoteAttendee> Attendees { get; set; } = [];

    public bool IsDone { get; set; } // İşaretleme özelliği
    public bool IsImportant { get; set; } // Önemli işaretleme özelliği
    public bool IsAllDay { get; set; } // Tüm gün mi?
    public bool IsCancelled { get; set; } // İptal edildi mi?

    // Soft Delete Properties
    public bool IsDeleted { get; set; }
    public DateTime? DeletedDate { get; set; }
    public Guid? DeletedByUserId { get; set; }

    public ICollection<CalendarNoteTag> Tags { get; set; } = [];
    public ICollection<CalendarNoteDepartment> Departments { get; set; } = [];


}
