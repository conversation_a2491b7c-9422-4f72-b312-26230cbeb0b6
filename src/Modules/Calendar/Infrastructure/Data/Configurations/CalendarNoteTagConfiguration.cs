using Calendar.Domain;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Calendar.Infrastructure.Data.Configurations;

public class CalendarNoteTagConfiguration : IEntityTypeConfiguration<CalendarNoteTag>
{
    public void Configure(EntityTypeBuilder<CalendarNoteTag> builder)
    {
        builder.ToTable("CalendarNoteTag", "Tasks");

        builder.HasKey(x => new { x.CalendarNoteId, x.TagId });

        builder.Property(x => x.CalendarNoteId)
            .IsRequired();

        builder.Property(x => x.TagId)
            .IsRequired();

        builder.HasOne(x => x.CalendarNote)
            .WithMany(x => x.Tags)
            .HasForeignKey(x => x.CalendarNoteId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(x => x.Tag)
            .WithMany(x => x.NoteTags)
            .HasForeignKey(x => x.TagId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
