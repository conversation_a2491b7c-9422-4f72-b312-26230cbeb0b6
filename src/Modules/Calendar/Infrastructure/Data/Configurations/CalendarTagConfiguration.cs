using Calendar.Domain;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Calendar.Infrastructure.Data.Configurations;

public class CalendarTagConfiguration : IEntityTypeConfiguration<CalendarTag>
{
    public void Configure(EntityTypeBuilder<CalendarTag> builder)
    {
        builder.ToTable("CalendarTag", "Tasks");

        builder.<PERSON><PERSON>ey(x => x.Id);

        builder.Property(x => x.Name)
            .IsRequired()
            .HasMaxLength(100);

        builder.HasIndex(x => x.Name)
            .IsUnique();

        builder.HasMany(x => x.NoteTags)
            .WithOne(x => x.Tag)
            .HasForeignKey(x => x.TagId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
