using System.Reflection;
using Calendar.Application.Abstractions;
using Calendar.Domain;
using Microsoft.EntityFrameworkCore;
using Shared.Application;
using Shared.Application.EventBus;
using Shared.Infrastructure.Data;


namespace Calendar.Infrastructure.Data;

public class CalendarDbContext(
    DbContextOptions<CalendarDbContext> options,
    IWorkContext workContext,
    IEventBus eventBus
) : BaseDbContext(options, workContext, eventBus), ICalendarDbContext
{
    public DbSet<CalendarNote> CalendarNotes { get; set; }
    public DbSet<CalendarNoteFile> CalendarNoteFiles { get; set; }
    public DbSet<CalendarReminder> CalendarReminders { get; set; }
    public DbSet<CalendarNoteAttendee> CalendarNoteAttendees { get; set; }
    public DbSet<RecurrenceRule> RecurrenceRules { get; set; }
    public DbSet<UserCalendarSettings> UserCalendarSettings { get; set; }
    public DbSet<CalendarTag> CalendarTags { get; set; }
    public DbSet<CalendarNoteTag> CalendarNoteTags { get; set; }
    public DbSet<CalendarNoteDepartment> CalendarNoteDepartments { get; set; }

    protected override void OnModelCreating(ModelBuilder builder)
    {
        
        base.OnModelCreating(builder);
        builder.HasDefaultSchema("Calendar");
        builder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());
    }
}
