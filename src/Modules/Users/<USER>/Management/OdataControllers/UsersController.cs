using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.OData.Query;
using Microsoft.AspNetCore.OData.Routing.Controllers;
using Microsoft.EntityFrameworkCore;
using Users.Application.Abstractions;

namespace Users.Application.Management.ODataControllers;

[Route("api/v1/users/management/odata")]
[Tags("Users.Management")]
public class UserManagementController(
    IUserDbContext context
) : ODataController
{
    private readonly IUserDbContext _context = context;

    [EnableQuery(PageSize = 10)]
    [HttpGet]
    public IActionResult Get()
    {
        return Ok(_context.Users
            .Include(c => c.UserRole)
            .Select(c => new UserDto
            {
                Id = c.Id,
                Name = c.Name,
                Surname = c.Surname,
                Email = c.Email,
                PhoneNumber = c.PhoneNumber,
                UserName = c.UserName,
            }).ToList());
    }
}
