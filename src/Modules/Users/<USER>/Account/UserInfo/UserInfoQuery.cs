using MediatR;
using Shared.Application;
using Users.Application.Pauses;

namespace Users.Application.Account.UserInfo;

public sealed record UserInfoQuery()
: IRequest<Result<UserInfoResponse>>;

public record UserInfoResponse(
    Guid Id,
    string? Email,
    string? Name,
    string? Surname,
    string? Fullname,
    string? PhoneNumber,
    string? PhonePrefix,
    string? ThreeCXExtension,
    string? ThreeCXEnabled,
    string? ThreeCXExternal,
    string? ThreeCXRecording,
    string? ChatURL,
    string? Role,
    DateTime? InsertDate)
{
    public PauseDto? ActivePause { get; set; }
};
