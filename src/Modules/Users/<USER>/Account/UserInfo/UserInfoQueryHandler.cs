using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;
using Users.Application.Abstractions;
using Users.Application.Pauses;
using Users.Domain.Account;
using Users.Domain.Pauses;

namespace Users.Application.Account.UserInfo;

public sealed class UserInfoQueryHandler(
    IUserDbContext userDbContext,
    IWorkContext workContext
) : IRequestHandler<UserInfoQuery, Result<UserInfoResponse>>
{
    private readonly IUserDbContext _userDbContext = userDbContext;
    private readonly IWorkContext _workContext = workContext;

    public async Task<Result<UserInfoResponse>> Handle(UserInfoQuery request, CancellationToken cancellationToken)
    {
        var currentUser = await _workContext.GetUserAsync();
        var user = await _userDbContext.Users.FindAsync(currentUser.Id, cancellationToken);
        if (user == null)
        {
            return Result.Failure<UserInfoResponse>(UserErrors.NotFoundError);
        }
        var userInfo = new UserInfoResponse(
            user.Id,
            user.Email,
            user.Name,
            user.Surname,
            currentUser.Fullname,
            user.PhoneNumber,
            user.PhonePrefix,
            user.ThreeCXExtension,
            user.ThreeCXEnabled,
            user.ThreeCXExternal,
            user.ThreeCXRecording,
            user.ChatURL,
            currentUser.Roles?.FirstOrDefault(),
            user.InsertDate);
        var pause = await _userDbContext.Pause
        .Include(x => x.Type)
            .FirstOrDefaultAsync(p => p.UserId == user.Id && p.Status == PauseStatus.Started, cancellationToken);
        if (pause is not null)
        {
            userInfo.ActivePause = new PauseDto(
                pause.Id,
                $"{user.Name} {user.Surname}",
                pause.Type.Name,
                pause.StartDateTime,
                pause.EndDateTime,
                pause.ActualStartTime,
                pause.PersonalDescription,
                pause.AdministratorDescription,
                pause.Duration,
                pause.Status
            );
        }
        return Result.Success(userInfo);
    }
}
