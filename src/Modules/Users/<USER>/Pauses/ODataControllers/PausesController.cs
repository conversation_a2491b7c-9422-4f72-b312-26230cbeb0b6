using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.OData.Query;
using Microsoft.AspNetCore.OData.Routing.Controllers;
using Microsoft.EntityFrameworkCore;
using Shared.Application;
using Users.Application.Abstractions;

namespace Users.Application.Pauses.ODataControllers;

[Route("api/v1/users/pauses/odata")]
[Tags("Users.Pauses")]
public class UserPausesController(
    IUserDbContext context,
    IWorkContext workContext
) : ODataController
{
    private readonly IUserDbContext _context = context;
    private readonly IWorkContext _workContext = workContext;

    [EnableQuery]
    [HttpGet]
    public IActionResult Get()
    {
        var query = _context.Pause.AsQueryable();
        if (!_workContext.HasRole("Admin"))
        {
            var userId = _workContext.UserId;
            query = query.Where(p => p.UserId == userId);
        }
        return Ok(query
            .Include(c => c.Type)
            .Select(c => new PauseDto(
                c.Id,
                $"{c.User.Name} {c.User.Surname}",
                c.Type.Name,
                c.StartDateTime,
                c.EndDateTime,
                c.ActualStartTime,
                c.PersonalDescription,
                c.AdministratorDescription,
                c.Duration,
                c.Status
            )));
    }
}
