using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;
using Users.Application.Abstractions;

namespace Users.Application.Notifications.GetNotifications;

public class GetNotificationQueryHandler(
    IUserDbContext dbContext,
    IWorkContext workContext
) : IRequestHandler<GetNotificationQuery, PagedResult<NotificationDto>>
{
    private readonly IUserDbContext _dbContext = dbContext;
    private readonly IWorkContext _workContext = workContext;

    public async Task<PagedResult<NotificationDto>> Handle(GetNotificationQuery request, CancellationToken cancellationToken)
    {
        var query = _dbContext.Notification.AsQueryable();
        if (!_workContext.HasRole("Admin"))
        {
            var userId = _workContext.UserId;
            query = query.Where(p => p.UserId == userId);
        }
        if (request.UserId.HasValue)
        {
            query = query.Where(x => x.UserId == request.UserId);
        }
        if (request.IsRead.HasValue)
        {
            query = query.Where(x => x.IsRead == request.IsRead);
        }
        var filteredCount = await query.CountAsync(cancellationToken);
        var totalCount = await _dbContext.Notification.CountAsync(cancellationToken);
        var items = await query
            .OrderByDescending(x => x.InsertDate)
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .Select(x => new NotificationDto(
                x.Id,
                x.UserId,
                x.User.Name + " " + x.User.Surname,
                x.Title,
                x.Message,
                x.Type,
                x.Data,
                x.IsRead,
                x.InsertDate,
                x.ReadDate))
            .ToListAsync(cancellationToken);
        return new PagedResult<NotificationDto>(items)
        {
            PageNumber = request.PageNumber,
            PageSize = request.PageSize,
            FilteredCount = filteredCount,
            Count = totalCount,
        };
    }

}
