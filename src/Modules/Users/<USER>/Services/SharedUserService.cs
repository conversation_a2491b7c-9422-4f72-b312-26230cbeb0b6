using Microsoft.EntityFrameworkCore;
using Shared.Application;
using Shared.Contracts;
using Users.Application.Abstractions;

namespace Users.Application.Services;

public class SharedUserService(
    IUserDbContext dbContext
) : ISharedUserService
{
    private readonly IUserDbContext _dbContext = dbContext;

    public async Task<Result<SharedUserDto>> GetUserAsync(Guid userId)
    {
        var user = await _dbContext.Users
            .Include(x => x.UserDepartment).FirstAsync(x => x.Id == userId);
        if (user == null)
        {
            return Result.Failure<SharedUserDto>("404", "User not found");
        }
        return new SharedUserDto
        {
            Id = user.Id,
            Active = user.Active,
            Name = user.Name,
            Surname = user.Surname,
            UserName = user.UserName,
            Email = user.Email,
            PhoneNumber = user.PhoneNumber,
            Extension = user.ThreeCXExtension,
            DepartmentIds = user.UserDepartment.Select(x => x.DepartmentId).ToList()
        };
    }

    public async Task<Result<SharedUserDto>> GetUserAsync(string extension)
    {
        var user = await _dbContext.Users.FirstOrDefaultAsync(x => x.ThreeCXExtension == extension);
        if (user == null)
        {
            return Result.Failure<SharedUserDto>("404", "User not found");
        }
        return new SharedUserDto
        {
            Id = user.Id,
            Active = user.Active,
            Name = user.Name,
            Surname = user.Surname,
            UserName = user.UserName,
            Email = user.Email,
            PhoneNumber = user.PhoneNumber,
            Extension = user.ThreeCXExtension
        };
    }

    public async Task<List<SharedUserDto>> GetUsersByExtensionsAsync(List<string> extensions)
    {
        var users = await _dbContext.Users
            .Where(u => extensions.Contains(u.ThreeCXExtension))
            .ToListAsync();

        return users.Select(user => new SharedUserDto
        {
            Id = user.Id,
            Active = user.Active,
            Name = user.Name,
            Surname = user.Surname,
            UserName = user.UserName,
            Email = user.Email,
            PhoneNumber = user.PhoneNumber,
            Extension = user.ThreeCXExtension
        }).ToList();
    }

    public async Task<List<SharedUserDto>> GetUsersByIdsAsync(List<Guid> userIds)
    {
        var users = await _dbContext.Users
            .Where(u => userIds.Contains(u.Id))
            .ToListAsync();

        return users.Select(user => new SharedUserDto
        {
            Id = user.Id,
            Active = user.Active,
            Name = user.Name,
            Surname = user.Surname,
            UserName = user.UserName,
            Email = user.Email,
            PhoneNumber = user.PhoneNumber,
            Extension = user.ThreeCXExtension
        }).ToList();
    }

    public async Task<bool> HasPermissionAsync(Guid userId, Guid[] roleIds, string permission)
    {
        return await _dbContext.PermissionRule.AnyAsync(x => (x.UserId == userId || roleIds.Contains(x.RoleId.Value)) && x.Permission.Key == permission);
    }

    public async Task<bool> HasPermissionAsync(Guid userId, string permission)
    {
        var roleIds = await _dbContext.UserRoles.Where(x => x.UserId == userId).Select(x => x.RoleId).ToArrayAsync();
        return await _dbContext.PermissionRule.AnyAsync(x => (x.UserId == userId || roleIds.Contains(x.RoleId.Value)) && x.Permission.Key == permission);
    }
}
