using Microsoft.EntityFrameworkCore;
using Shared.Contracts;
using Users.Application.Abstractions;

namespace Users.Application.Services;

public class SharedDepartmentService(
    IUserDbContext dbContext
) : ISharedDepartmentService
{
    private readonly IUserDbContext _dbContext = dbContext;

    public async Task<List<SharedDepartmentDto>> GetDepartmentsByIdsAsync(List<Guid> departmentIds)
    {
        var departments = await _dbContext.Department
            .Where(d => departmentIds.Contains(d.Id))
            .ToListAsync();

        return departments.Select(department => new SharedDepartmentDto
        {
            Id = department.Id,
            Name = department.Name,
            Code = department.Code ?? string.Empty
        }).ToList();
    }
}
