using MediatR;
using Microsoft.EntityFrameworkCore;
using Requests.Application.Abstractions;
using Shared.Application;
using Shared.Contracts;

namespace Requests.Application.TicketComments.ListTicketComments;

public class ListTicketCommentsQueryHandler(
    IRequestsDbContext dbContext,
    ISharedUserService userService
) : IRequestHandler<ListTicketCommentsQuery, PagedResult<TicketCommentDto>>
{
    private readonly IRequestsDbContext _dbContext = dbContext;
    private readonly ISharedUserService _userService = userService;

    public async Task<PagedResult<TicketCommentDto>> Handle(ListTicketCommentsQuery request, CancellationToken cancellationToken)
    {
        // Ticket'ı kontrol et
        var ticketExists = await _dbContext.Tickets
            .AnyAsync(t => t.Id == request.TicketId, cancellationToken);

        if (!ticketExists)
        {
            return PagedResult<TicketCommentDto>.Failure("404", "Ticket bulunamadı.");
        }

        var query = _dbContext.TicketComments
            .Where(c => c.TicketId == request.TicketId)
            .AsQueryable();

        // Toplam sayıyı al
        var totalCount = await _dbContext.Tickets.CountAsync(cancellationToken);
        var filteredCount = await query.CountAsync(cancellationToken);

        // Sayfalama uygula
        var comments = await query
            .OrderByDescending(c => c.InsertDate)
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .ToListAsync(cancellationToken);

        // Kullanıcı bilgilerini al
        var userIds = comments.Select(c => c.UserId).Distinct().ToList();
        var users = new Dictionary<Guid, SharedUserDto>();

        foreach (var userId in userIds)
        {
            var userResult = await _userService.GetUserAsync(userId);
            if (userResult.IsSuccess)
            {
                users[userId] = userResult.Value;
            }
        }

        // Sonuçları dönüştür
        var result = comments.Select(c => new TicketCommentDto
        {
            Id = c.Id,
            TicketId = c.TicketId,
            UserId = c.UserId,
            UserName = users.TryGetValue(c.UserId, out var user) ? $"{user.Name} {user.Surname}" : null,
            Comment = c.Comment,
            InsertDate = c.InsertDate
        }).ToList();

        var pagedResult = PagedResult<TicketCommentDto>.Success(result);
        pagedResult.PageNumber = request.PageNumber;
        pagedResult.PageSize = request.PageSize;
        pagedResult.Count = totalCount;
        pagedResult.FilteredCount = filteredCount;

        return pagedResult;
    }
}
