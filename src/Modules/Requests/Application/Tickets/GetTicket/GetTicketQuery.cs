using MediatR;
using Requests.Domain;
using Shared.Application;

namespace Requests.Application.Tickets.GetTicket;

public record GetTicketQuery(Guid Id) : IRequest<Result<TicketDto>>;

public record TicketDto
{
    public Guid Id { get; init; }
    public string Code { get; init; } = string.Empty;
    public TicketType TicketType { get; init; }
    public Guid? TopTicketId { get; init; }
    public Guid SubjectId { get; init; }
    public string? SubjectName { get; init; }
    public Guid CustomerId { get; init; }
    public string? CustomerName { get; init; }
    public string Title { get; init; } = string.Empty;
    public string Description { get; init; } = string.Empty;
    public Guid? NotificationWayId { get; init; }
    public string? NotificationWay { get; init; } = string.Empty;
    public Guid? UserId { get; init; }
    public string? UserName { get; init; }
    public Guid ReporterUserId { get; init; }
    public string? ReporterUserName { get; init; }
    public List<TicketDepartmentDto> Departments { get; init; } = [];
    public List<TicketFileDto> TicketFiles { get; init; } = [];
    public PriorityEnum Priority { get; init; }
    public Guid? StatusId { get; init; }
    public string? StatusName { get; init; } = string.Empty;
    public NodeType StatusType { get; set; }
    public DateTime? EndDate { get; init; }
    public List<Guid> Watchlist { get; init; } = [];
    public List<string> Tags { get; init; } = [];
    public Dictionary<string, string>? AttributeData { get; init; }
    public DateTime InsertDate { get; init; }
    public DateTime? UpdateDate { get; init; }
    public List<TicketCommentDto>? Comments { get; init; }
}

public record TicketDepartmentDto(
    Guid DepartmentId,
    string DepartmentName
);

public record TicketFileDto
{
    public Guid Id { get; init; }
    public Guid TicketId { get; init; }
    public Guid FileId { get; init; }
    public string FileName { get; init; } = string.Empty;
    public string FilePath { get; init; } = string.Empty;
}
