using MediatR;
using Microsoft.EntityFrameworkCore;
using Requests.Application.Abstractions;
using Requests.Domain;
using Shared.Application;

namespace Requests.Application.Tickets.PatchTicket;

public class PatchTicketCommandHandler(
    IRequestsDbContext dbContext,
    ITicketHistoryService historyService
) : IRequestHandler<PatchTicketCommand, Result>
{
    private readonly IRequestsDbContext _dbContext = dbContext;
    private readonly ITicketHistoryService _historyService = historyService;

    public async Task<Result> Handle(PatchTicketCommand request, CancellationToken cancellationToken)
    {
        var ticket = await _dbContext.Tickets
            .FirstOrDefaultAsync(t => t.Id == request.Id, cancellationToken);

        if (ticket == null)
        {
            return Result.Failure("Ticket.NotFound", "Ticket bulunamadı.");
        }

        // Eski değerleri sakla (history tracking için)
        var oldTicket = new Ticket
        {
            Id = ticket.Id,
            Title = ticket.Title,
            Description = ticket.Description,
            Priority = ticket.Priority,
            EndDate = ticket.EndDate,
            NotificationWayId = ticket.NotificationWayId,
            UserId = ticket.UserId,
            StatusId = ticket.StatusId,
            Watchlist = ticket.Watchlist?.ToList() ?? [],
            Tags = ticket.Tags?.ToList() ?? [],
            AttributeData = ticket.AttributeData
        };

        // Sadece gönderilen alanları güncelle
        if (request.Title != null)
            ticket.Title = request.Title;

        if (request.Description != null)
            ticket.Description = request.Description;

        if (request.NotificationWayId.HasValue)
            ticket.NotificationWayId = request.NotificationWayId;

        if (request.UserId.HasValue)
            ticket.UserId = request.UserId;

        if (request.Priority.HasValue)
            ticket.Priority = request.Priority.Value;

        if (request.StatusId.HasValue)
            ticket.StatusId = request.StatusId;

        if (request.EndDate.HasValue)
            ticket.EndDate = request.EndDate;

        if (request.Watchlist != null)
            ticket.Watchlist = request.Watchlist;

        if (request.Tags != null)
            ticket.Tags = request.Tags;

        if (request.AttributeData != null)
            ticket.AttributeData = request.AttributeData;

        // History tracking - sadece değişen alanlar için
        await _historyService.TrackTicketUpdatedAsync(ticket.Id, oldTicket, ticket, cancellationToken);

        if (request.Watchlist != null)
            await _historyService.TrackTicketWatchlistChangedAsync(ticket.Id, oldTicket.Watchlist, ticket.Watchlist, cancellationToken);

        if (request.Tags != null)
            await _historyService.TrackTicketTagsChangedAsync(ticket.Id, oldTicket.Tags, ticket.Tags, cancellationToken);

        await _dbContext.SaveChangesAsync(cancellationToken);
        return Result.Success();
    }
}
