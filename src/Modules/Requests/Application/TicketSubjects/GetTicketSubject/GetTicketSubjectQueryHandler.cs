using MediatR;
using Microsoft.EntityFrameworkCore;
using Requests.Application.Abstractions;
using Shared.Application;

namespace Requests.Application.TicketSubjects.GetTicketSubject;

public class GetTicketSubjectQueryHandler : IRequestHandler<GetTicketSubjectQuery, Result<TicketSubjectDto>>
{
    private readonly IRequestsDbContext _dbContext;

    public GetTicketSubjectQueryHandler(IRequestsDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<Result<TicketSubjectDto>> Handle(GetTicketSubjectQuery request, CancellationToken cancellationToken)
    {
        var ticketSubject = await _dbContext.TicketSubjects
            .Include(x => x.Flow)
            .FirstOrDefaultAsync(s => s.Id == request.Id, cancellationToken);

        if (ticketSubject == null)
        {
            return Result.Failure<TicketSubjectDto>("404", "Ticket konusu bulunamadı.");
        }

        var result = new TicketSubjectDto(
            ticketSubject.Id,
            ticketSubject.Name,
            ticketSubject.Flow?.Name ?? "",
            ticketSubject.FlowId,
            ticketSubject.InsertDate,
            ticketSubject.UpdateDate);

        return Result.Success(result);
    }
}
