using MediatR;
using Microsoft.EntityFrameworkCore;
using Requests.Application.Abstractions;
using Shared.Application;

namespace Requests.Application.TicketSubjects.ListTicketSubjects;

public class ListTicketSubjectsQueryHandler(
    IRequestsDbContext dbContext
) : IRequestHandler<ListTicketSubjectsQuery, PagedResult<TicketSubjectDto>>
{
    private readonly IRequestsDbContext _dbContext = dbContext;

    public async Task<PagedResult<TicketSubjectDto>> Handle(ListTicketSubjectsQuery request, CancellationToken cancellationToken)
    {
        var query = _dbContext.TicketSubjects
            .Include(x => x.Flow)
            .AsQueryable();

        // Apply search filter if provided
        if (!string.IsNullOrWhiteSpace(request.SearchTerm))
        {
            var searchTerm = request.SearchTerm.Trim().ToLower();
            query = query.Where(s => s.Name.ToLower().Contains(searchTerm));
        }
        if (request.HasFlow.HasValue)
        {
            query = request.HasFlow.Value
                ? query.Where(s => s.FlowId != null)
                : query.Where(s => s.FlowId == null);
        }

        // Get total count
        var totalCount = await _dbContext.TicketSubjects.CountAsync(cancellationToken);
        var filteredCount = await query.CountAsync(cancellationToken);

        // Apply pagination
        var ticketSubjects = await query
            .OrderBy(s => s.Name)
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .Select(s => new TicketSubjectDto(
                s.Id,
                s.Name,
                s.Flow.Name,
                s.FlowId,
                s.InsertDate,
                s.UpdateDate))
            .ToListAsync(cancellationToken);

        var result = PagedResult<TicketSubjectDto>.Success(ticketSubjects);
        result.PageNumber = request.PageNumber;
        result.PageSize = request.PageSize;
        result.Count = totalCount;
        result.FilteredCount = filteredCount;

        return result;
    }
}
