using Customers.Application.Abstractions;
using Customers.Domain;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;
using Shared.Contracts;

namespace Customers.Application.Customers.GetCustomerAdvisor;

internal sealed class GetCustomerAdvisorQueryHandler(
    ICustomersDbContext context,
    ISharedUserService userService
) : IRequestHandler<GetCustomerAdvisorQuery, Result<List<SharedUserDto>>>
{
    private readonly ICustomersDbContext _context = context;
    private readonly ISharedUserService _userService = userService;

    public async Task<Result<List<SharedUserDto>>> Handle(GetCustomerAdvisorQuery request, CancellationToken cancellationToken)
    {
        var cleanphone = request.Phone.TrimStart('0');
        var customer = await _context.Customers
            .FirstOrDefaultAsync(x => (x.PhonePrefix + x.Phone).Contains(cleanphone) && !x.IsDeleted, cancellationToken);

        if (customer is null)
        {
            return Result.Failure<List<SharedUserDto>>(CustomerErrors.CustomerNotFound(request.Phone));
        }
        if (customer.AdvisorIds == null || !customer.AdvisorIds.Any())
        {
            return Result.Failure<List<SharedUserDto>>("Müşteriye atan bir danışman yok.");
        }
        var userResult = await _userService.GetUsersByIdsAsync(customer.AdvisorIds);
        if (userResult?.Count == 0)
        {
            return Result.Failure<List<SharedUserDto>>("404", "Müşteriye atanan bir danışman bulunamadı.");
        }
        return Result.Success(userResult);
    }
}
