using Customers.Application.Abstractions;
using Microsoft.EntityFrameworkCore;
using PhoneNumbers;
using Shared.Application;
using Shared.Contracts;
using Shared.Domain;

namespace Customers.Application.Services;

public class SharedCustomerService(
    ICustomersDbContext dbContext,
    AppSettings appSettings
) : ISharedCustomerService
{
    private readonly ICustomersDbContext _dbContext = dbContext;
    private readonly AppSettings _appSettings = appSettings;

    public async Task<Result<SharedCustomerDto>> GetCustomerAsync(Guid customerId)
    {
        var customer = await _dbContext.Customers.FirstOrDefaultAsync(c => c.Id == customerId);
        if (customer is null)
        {
            return Result.Failure<SharedCustomerDto>("404", "Customer not found");
        }
        return new SharedCustomerDto
        {
            Id = customer.Id,
            Name = customer.Name,
            Surname = customer.Surname,
            Email = customer.Email,
            Phone = customer.Phone,
            PhonePrefix = customer.PhonePrefix
        };
    }

    public async Task<Result<SharedCustomerDto>> GetCustomerAsync(string phone)
    {
        var phoneUtil = PhoneNumberUtil.GetInstance();
        var parsedNumber = phoneUtil.Parse(phone, _appSettings.DefaultRegion);
        var cleanphone = parsedNumber.NationalNumber.ToString();
        var customer = await _dbContext.Customers.FirstOrDefaultAsync(c => (c.PhonePrefix + c.Phone).Contains(cleanphone));
        if (customer is null)
        {
            return Result.Failure<SharedCustomerDto>("404", "Customer not found");
        }
        return new SharedCustomerDto
        {
            Id = customer.Id,
            Name = customer.Name,
            Surname = customer.Surname,
            Email = customer.Email,
            Phone = customer.Phone,
            PhonePrefix = customer.PhonePrefix
        };
    }

    public async Task<Result<List<SharedCustomerDto>>> GetCustomerByIdsAsync(List<Guid> customerIds)
    {
        return await _dbContext.Customers
            .Where(c => customerIds.Contains(c.Id))
            .Select(c => new SharedCustomerDto
            {
                Id = c.Id,
                Name = c.Name,
                Surname = c.Surname,
                Email = c.Email,
                Phone = c.Phone,
                PhonePrefix = c.PhonePrefix
            }).ToListAsync();
    }

    public async Task<Result<List<SharedCustomerDto>>> GetCustomersByPhonesAsync(List<string> phones)
    {
        // var phoneUtil = PhoneNumberUtil.GetInstance();
        // var clearphones = phones.Select(x => phoneUtil.Parse(x, _appSettings.DefaultRegion).NationalNumber.ToString()).ToList();
        return await _dbContext.Customers
            .Where(c => phones.Contains(c.PhonePrefix + c.Phone))
            .Select(c => new SharedCustomerDto
            {
                Id = c.Id,
                Name = c.Name,
                Surname = c.Surname,
                Email = c.Email,
                Phone = c.Phone,
                PhonePrefix = c.PhonePrefix
            }).ToListAsync();
    }

    public async Task<Result<List<SharedNotificationWayDto>>> GetNotificationWaysAsync()
    {
        return await _dbContext.NotificationWays
            .Select(nw => new SharedNotificationWayDto
            {
                Id = nw.Id,
                Name = nw.Name
            }).ToListAsync();
    }

    public async Task<Result<SharedCustomerDto>> GetTempCustomerAsync(string phone)
    {
        var cleanphone = phone.TrimStart('0');
        var customer = await _dbContext.TempCustomers.FirstOrDefaultAsync(c => (c.PhonePrefix + c.Phone).Contains(cleanphone));
        if (customer is null)
        {
            return Result.Failure<SharedCustomerDto>("404", "Customer not found");
        }
        return new SharedCustomerDto
        {
            Id = customer.Id,
            Name = customer.Name,
            Surname = customer.Surname,
            Email = customer.Email,
            Phone = customer.Phone,
            PhonePrefix = customer.PhonePrefix
        };
    }
}
