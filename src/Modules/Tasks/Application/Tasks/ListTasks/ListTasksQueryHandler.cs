using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;
using Shared.Contracts;
using Tasks.Application.Abstractions;

namespace Tasks.Application.Tasks.ListTasks;

public class ListTasksQueryHandler(
    ITaskDbContext dbContext,
    ISharedUserService userService,
    ISharedDepartmentService departmentService,
    ISharedCustomerService customerService,
    IWorkContext workContext
) : IRequestHandler<ListTasksQuery, PagedResult<TaskDto>>
{
    private readonly ITaskDbContext _dbContext = dbContext;
    private readonly ISharedUserService _userService = userService;
    private readonly ISharedDepartmentService _departmentService = departmentService;
    private readonly ISharedCustomerService _customerService = customerService;
    private readonly IWorkContext _workContext = workContext;

    public async Task<PagedResult<TaskDto>> Handle(ListTasksQuery request, CancellationToken cancellationToken)
    {
        var user = await _workContext.GetUserAsync();
        var query = _dbContext.Tasks
            .Include(t => t.Status)
            .AsQueryable();
        if (!_workContext.HasRole("Admin"))
        {
            if (_workContext.HasRole("LEADER"))
            {
                var userDto = await _userService.GetUserAsync(user.Id);
                query = query.Where(x => x.TaskDepartments.Any(y => userDto.Value.DepartmentIds.Contains(y.Id)));
            }
            else
            {
                query = query.Where(x =>
                x.UserId == user.Id ||
                x.ReporterUserId == user.Id ||
                x.Watchlist.Any(y => y.UserId == user.Id) ||
                x.Comments.Any(y => y.UserId == user.Id) ||
                x.InsertUserId == user.Id);
            }
        }
        if (!string.IsNullOrWhiteSpace(request.SearchTerm))
        {
            var searchTerm = request.SearchTerm.ToLower();
            query = query.Where(t => t.Title.ToLower().Contains(searchTerm) ||
                                    t.Description != null && t.Description.ToLower().Contains(searchTerm));
        }
        if (!string.IsNullOrWhiteSpace(request.Title))
        {
            var searchTerm = request.Title.ToLower();
            query = query.Where(t => t.Title.ToLower().Contains(searchTerm));
        }
        if (request.UserId.HasValue)
        {
            query = query.Where(t => t.UserId == request.UserId);
        }
        if (request.ReporterUserId.HasValue)
        {
            query = query.Where(t => t.ReporterUserId == request.ReporterUserId);
        }
        if (request.Priority.HasValue)
        {
            query = query.Where(t => t.Priority == request.Priority);
        }
        if (request.StatusId.HasValue)
        {
            query = query.Where(t => t.StatusId == request.StatusId);
        }
        if (request.StartDate.HasValue)
        {
            query = query.Where(t => t.InsertDate >= request.StartDate);
        }
        if (request.EndDate.HasValue)
        {
            query = query.Where(t => t.EndDate <= request.EndDate);
        }
        if (request.DepartmentId.HasValue)
        {
            query = query.Where(t => t.TaskDepartments.Any(td => td.DepartmentId == request.DepartmentId));
        }
        var totalCount = await _dbContext.Tasks.CountAsync(cancellationToken);
        var filteredCount = await query.CountAsync(cancellationToken);
        var tasks = await query
            .OrderByDescending(t => t.InsertDate)
            .Include(x => x.TaskDepartments)
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .ToListAsync(cancellationToken);

        var userIds = tasks
            .SelectMany(t => new[] { t.UserId, t.ReporterUserId })
            .Where(id => id.HasValue)
            .Select(id => id!.Value)
            .Distinct()
            .ToList();
        var users = await _userService.GetUsersByIdsAsync(userIds);
        var userDict = users.ToDictionary(u => u.Id, u => $"{u.Name} {u.Surname}");
        var notificationWay = await _customerService.GetNotificationWaysAsync();
        var taskDtos = tasks.Select(t => new TaskDto(
            t.Id,
            t.Title,
            t.Description,
            t.UserId,
            t.ReporterUserId,
            t.Priority,
            new StatusDto(t.Status!.Id, t.Status.Name, t.Status.ColorCode, t.Status.Order, t.Status.IsClosed),
            t.EndDate,
            t.InsertDate,
            t.UpdateDate
        )
        {
            NotificationWayId = t.NotificationWayId,
            NotificationWay = notificationWay.Value.FirstOrDefault(x => x.Id == t.NotificationWayId)?.Name,
            UserFullName = t.UserId.HasValue && userDict.ContainsKey(t.UserId.Value) ? userDict[t.UserId.Value] : null,
            ReporterUserFullName = userDict.ContainsKey(t.ReporterUserId) ? userDict[t.ReporterUserId] : "Bilinmeyen Kullanıcı",
            Departments = [.. t.TaskDepartments.Select(td => new TaskDepartmentDto(
                td.DepartmentId,
                td.DepartmentName
            ))]
        }).ToList();

        return new PagedResult<TaskDto>(taskDtos)
        {
            Count = totalCount,
            FilteredCount = filteredCount,
            PageNumber = request.PageNumber,
            PageSize = request.PageSize
        };
    }
}
