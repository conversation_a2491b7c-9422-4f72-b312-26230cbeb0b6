using Conversations.Application.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Conversations.Application.Chats.AddMessage;

public class AddMessageCommandHandler(
    IConversationDbContext dbContext,
    IMessageSenderFactory messageSenderFactory
) : IRequestHandler<AddMessageCommand, Result<Guid>>
{
    private readonly IConversationDbContext _dbContext = dbContext;
    private readonly IMessageSenderFactory _messageSenderFactory = messageSenderFactory;

    public async Task<Result<Guid>> Handle(AddMessageCommand request, CancellationToken cancellationToken)
    {
        var chat = await _dbContext.Chat
            .FirstOrDefaultAsync(c => c.Id == request.ChatId, cancellationToken);

        if (chat == null)
        {
            return Result.Failure<Guid>($"Chat with ID {request.ChatId} not found");
        }

        var message = chat.AddMessage(
            request.Content,
            request.Direction,
            request.SenderId,
            request.ContentType);

        if (!string.IsNullOrEmpty(request.ExternalId))
        {
            message.SetExternalId(request.ExternalId);
        }

        if (!string.IsNullOrEmpty(request.MetaData))
        {
            message.SetMetaData(request.MetaData);
        }

        if (request.Attachments != null && request.Attachments.Any())
        {
            foreach (var attachment in request.Attachments)
            {
                message.AddAttachment(
                    attachment.Url,
                    attachment.Type,
                    attachment.FileName,
                    attachment.ContentType,
                    attachment.FileSize);
            }
        }

        await _dbContext.SaveChangesAsync(cancellationToken);
        // Send the message through the appropriate channel
        var messageSender = _messageSenderFactory.GetSender(chat.Channel);
        var sendResult = await messageSender.SendMessageAsync(new SendMessageRequest
        {
            MessageId = message.Id,
            ChatId = chat.Id,
            BaseChatId = chat.BaseChatId,
            RecipientIdentifier = chat.ExternalId,
            Content = request.Content,
            ContentType = request.ContentType,
            Attachments = request.Attachments?.Select(a => new AttachmentRequest
            {
                Url = a.Url,
                Type = a.Type,
                FileName = a.FileName,
                ContentType = a.ContentType,
                FileSize = a.FileSize
            }).ToList()
        });

        if (sendResult.Success)
        {
            // Update the message with the external ID
            message.SetExternalId(sendResult.ExternalMessageId);
            await _dbContext.SaveChangesAsync(cancellationToken);
        }
        else
        {
            // Log the error but still return success since the message is created in our system
            // In a real system, you might want to queue this for retry
            // For now, we'll just return the error
            return Result.Failure<Guid>($"Message created but failed to send: {sendResult.ErrorMessage}");
        }
        return Result.Success(message.Id);
    }
}
