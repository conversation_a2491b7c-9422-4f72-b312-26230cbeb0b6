using Conversations.Domain.Chats;
using MediatR;
using Shared.Application;

namespace Conversations.Application.Chats.AddMessage;

public record AddMessageCommand : IRequest<Result<Guid>>
{
    public Guid ChatId { get; init; }
    public string Content { get; init; }
    public MessageDirection Direction { get; init; }
    public string SenderId { get; init; }
    public MessageContentType ContentType { get; init; } = MessageContentType.Text;
    public string? ExternalId { get; init; }
    public string? MetaData { get; init; }
    public List<AttachmentInfoDto>? Attachments { get; init; }
}
