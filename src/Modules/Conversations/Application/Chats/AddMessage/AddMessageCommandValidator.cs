using FluentValidation;

namespace Conversations.Application.Chats.AddMessage;

public class AddMessageCommandValidator : AbstractValidator<AddMessageCommand>
{
    public AddMessageCommandValidator()
    {
        RuleFor(x => x.ChatId)
            .NotEmpty().WithMessage("Chat ID is required");

        RuleFor(x => x.Content)
            .NotEmpty().WithMessage("Content is required");

        RuleFor(x => x.Direction)
            .IsInEnum().WithMessage("Invalid message direction");

        RuleFor(x => x.SenderId)
            .NotEmpty().WithMessage("Sender ID is required")
            .MaximumLength(100).WithMessage("Sender ID cannot exceed 100 characters");

        RuleFor(x => x.ContentType)
            .IsInEnum().WithMessage("Invalid content type");

        RuleFor(x => x.ExternalId)
            .MaximumLength(100).WithMessage("External ID cannot exceed 100 characters");

        RuleForEach(x => x.Attachments).SetValidator(new AttachmentInfoDtoValidator());
    }
}
