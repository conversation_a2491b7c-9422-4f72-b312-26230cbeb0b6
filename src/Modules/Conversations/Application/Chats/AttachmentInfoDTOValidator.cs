using FluentValidation;

namespace Conversations.Application.Chats;

public class AttachmentInfoDtoValidator : AbstractValidator<AttachmentInfoDto>
{
    public AttachmentInfoDtoValidator()
    {
        RuleFor(x => x.Url)
            .NotEmpty().WithMessage("Attachment URL is required")
            .MaximumLength(1000).WithMessage("URL cannot exceed 1000 characters");

        RuleFor(x => x.Type)
            .IsInEnum().WithMessage("Invalid attachment type");

        RuleFor(x => x.FileName)
            .MaximumLength(255).WithMessage("File name cannot exceed 255 characters");

        RuleFor(x => x.ContentType)
            .MaximumLength(100).WithMessage("Content type cannot exceed 100 characters");
    }
}
