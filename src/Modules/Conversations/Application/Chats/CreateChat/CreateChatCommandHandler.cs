using Conversations.Application.Abstractions;
using Conversations.Domain.Chats;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Conversations.Application.Chats.CreateChat;

public class CreateChatCommandHandler(
    IConversationDbContext dbContext
) : IRequestHandler<CreateChatCommand, Result<Guid>>
{
    private readonly IConversationDbContext _dbContext = dbContext;

    public async Task<Result<Guid>> Handle(CreateChatCommand request, CancellationToken cancellationToken)
    {
        // Check if chat with the same external ID and channel already exists
        var existingChat = await _dbContext.Chat
            .FirstOrDefaultAsync(c => c.ExternalId == request.ExternalId && c.Channel == request.Channel,
                cancellationToken);

        if (existingChat != null)
        {
            return Result.Success(existingChat.Id);
        }

        // Create new chat
        var chat = new Chat(request.CustomerId, request.Channel, request.ExternalId, request.BaseChatId);

        if (!string.IsNullOrEmpty(request.Title))
        {
            chat.SetTitle(request.Title);
        }

        if (request.AssignedUserId.HasValue)
        {
            chat.AssignTo(request.AssignedUserId.Value);
        }

        _dbContext.Chat.Add(chat);
        await _dbContext.SaveChangesAsync(cancellationToken);

        return Result<Guid>.Success(chat.Id);
    }
}
