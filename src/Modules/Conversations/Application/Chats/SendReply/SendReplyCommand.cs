using Conversations.Application.Chats.AddMessage;
using Conversations.Domain.Chats;
using MediatR;
using Shared.Application;

namespace Conversations.Application.Chats.SendReply;

public record SendReplyCommand : IRequest<Result<Guid>>
{
    public Guid ChatId { get; init; }
    public string Content { get; init; }
    public string SenderId { get; init; }
    public MessageContentType ContentType { get; init; } = MessageContentType.Text;
    public Guid? ReplyToMessageId { get; init; }
    public List<AttachmentInfoDto>? Attachments { get; init; }
}
