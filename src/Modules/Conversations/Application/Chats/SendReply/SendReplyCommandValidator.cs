using FluentValidation;

namespace Conversations.Application.Chats.SendReply;

public class SendReplyCommandValidator : AbstractValidator<SendReplyCommand>
{
    public SendReplyCommandValidator()
    {
        RuleFor(x => x.ChatId)
            .NotEmpty().WithMessage("Chat ID is required");

        RuleFor(x => x.Content)
            .NotEmpty().WithMessage("Content is required");

        RuleFor(x => x.SenderId)
            .NotEmpty().WithMessage("Sender ID is required")
            .MaximumLength(100).WithMessage("Sender ID cannot exceed 100 characters");

        RuleFor(x => x.ContentType)
            .IsInEnum().WithMessage("Invalid content type");

        RuleForEach(x => x.Attachments).SetValidator(new AttachmentInfoDtoValidator());
    }
}
