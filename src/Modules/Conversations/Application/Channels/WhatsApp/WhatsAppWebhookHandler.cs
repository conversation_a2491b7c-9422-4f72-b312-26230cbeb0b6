using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using Conversations.Domain.Chats;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Shared.Contracts;
using Shared.Domain;

namespace Conversations.Application.Channels.WhatsApp;

public class WhatsAppWebhookHandler(
    IMediator mediator,
    AppSettings appSettings,
    IHttpContextAccessor httpContextAccessor,
    ISharedCustomerService sharedCustomerService,
    ILogger<WhatsAppWebhookHandler> logger)
{
    private readonly IMediator _mediator = mediator;
    private readonly AppSettings _appSettings = appSettings;
    private readonly IHttpContextAccessor _httpContextAccessor = httpContextAccessor;
    private readonly ISharedCustomerService _sharedCustomerService = sharedCustomerService;
    private readonly ILogger<WhatsAppWebhookHandler> _logger = logger;

    public async Task<IResult> HandleWebhookAsync(WhatsAppWebhookRequest request)
    {
        try
        {
            if (!await VerifyWebhook())
            {
                _logger.LogWarning("Invalid webhook signature");
                return Results.BadRequest("Invalid webhook signature");
            }
            if (request.IsMessageEvent())
            {
                await ProcessMessageEvent(request);
                return Results.Accepted();
            }
            else if (request.IsStatusEvent())
            {
                await ProcessStatusEvent(request);
                return Results.Accepted();
            }

            _logger.LogWarning("Unknown webhook event type: {Type}", request.Type);
            return Results.BadRequest("Unknown webhook event type");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing WhatsApp webhook");
            return Results.StatusCode(500);
        }
    }

    private async Task ProcessMessageEvent(WhatsAppWebhookRequest request)
    {
        // Resolve customer ID from WhatsApp number
        var customerResult = await _sharedCustomerService.GetCustomerAsync(request.CustomerPhone);
        var customerId = customerResult.IsSuccess ? customerResult.Value?.Id : null;
        if (customerId == Guid.Empty)
        {
            _logger.LogWarning("Could not resolve customer for WhatsApp number: {Number}", request.From);
            return;
        }
        // Process attachments if any
        List<IncomingAttachment>? attachments = null;
        if (request.Attachments != null && request.Attachments.Any())
        {
            attachments = request.Attachments.Select(a => new IncomingAttachment
            {
                Url = a.Url,
                Type = a.MapType(),
                FileName = a.FileName,
                ContentType = a.ContentType,
                FileSize = a.FileSize
            }).ToList();
        }
        await _mediator.Send(new ProcessIncomingMessageCommand
        {
            Channel = ChatChannel.WhatsApp,
            ExternalChatId = request.ConversationId ?? request.From, // Fallback to sender if no conversation ID
            BaseChatId = request.BaseChatId,
            ExternalMessageId = request.MessageId,
            CustomerId = customerId,
            Content = request.Content ?? string.Empty,
            ContentType = request.MapContentType(),
            SentAt = request.Timestamp,
            Sender = request.From,
            MetaData = request.MetaData != null ? JsonSerializer.Serialize(request.MetaData) : null,
            Attachments = attachments
        });
    }

    private async Task ProcessStatusEvent(WhatsAppWebhookRequest request)
    {
        // Update message status
        await _mediator.Send(new UpdateMessageStatusCommand
        {
            ExternalMessageId = request.MessageId,
            Status = request.MapStatus(),
            Timestamp = request.Timestamp
        });
    }

    private async Task<bool> VerifyWebhook()
    {
        return true;
        // _httpContextAccessor.HttpContext?.Request.EnableBuffering();
        // using var reader = new StreamReader(
        //     _httpContextAccessor.HttpContext.Request.Body,
        //     encoding: Encoding.UTF8,
        //     detectEncodingFromByteOrderMarks: false,
        //     leaveOpen: true);
        // var body = await reader.ReadToEndAsync();
        // _httpContextAccessor.HttpContext.Request.Body.Position = 0;
        // string signatureHeader = _httpContextAccessor.HttpContext?.Request.Headers["X-Hub-Signature"].ToString();
        // string appSecret = _appSettings.WhatsAppWebhookSecret;
        // if (string.IsNullOrEmpty(signatureHeader) || string.IsNullOrEmpty(appSecret))
        // {
        //     return false;
        // }
        // try
        // {
        //     var signature = signatureHeader.Replace("sha1=", "");
        //     var secretBytes = Encoding.UTF8.GetBytes(appSecret);
        //     var payloadBytes = Encoding.UTF8.GetBytes(body);
        //     using var hmac = new HMACSHA1(secretBytes);
        //     var hash = hmac.ComputeHash(payloadBytes);
        //     var hashString = Convert.ToHexStringLower(hash).Replace("-", "").ToLower();
        //     return hashString == signature;
        // }
        // catch
        // {
        //     return false;
        // }
    }
}
