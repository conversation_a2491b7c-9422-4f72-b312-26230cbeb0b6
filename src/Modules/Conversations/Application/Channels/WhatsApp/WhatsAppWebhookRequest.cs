using Conversations.Domain.Chats;

namespace Conversations.Application.Channels.WhatsApp;

public class WhatsAppWebhookRequest
{
    public string Type { get; set; } // "message" or "status"
    public string BaseChatId { get; set; }
    public string MessageId { get; set; }
    public string? ConversationId { get; set; }
    public string CustomerPhone { get; set; }
    public string From { get; set; }
    public string? To { get; set; }
    public string? Content { get; set; }
    public string? ContentType { get; set; }
    public string? Status { get; set; }
    public DateTime Timestamp { get; set; }
    public Dictionary<string, object>? MetaData { get; set; }
    public List<WhatsAppAttachment>? Attachments { get; set; }

    public bool IsMessageEvent() => Type?.ToLower() == "messages";
    public bool IsStatusEvent() => Type?.ToLower() == "status";

    public MessageContentType MapContentType()
    {
        return ContentType?.ToLower() switch
        {
            "text" => MessageContentType.Text,
            "image" => MessageContentType.Image,
            "audio" => MessageContentType.Voice,
            "document" => MessageContentType.Document,
            "location" => MessageContentType.Location,
            _ => MessageContentType.Text
        };
    }

    public MessageStatus MapStatus()
    {
        return Status?.ToLower() switch
        {
            "delivered" => MessageStatus.Delivered,
            "read" => MessageStatus.Read,
            _ => MessageStatus.Sent
        };
    }
}

public class WhatsAppAttachment
{
    public string Url { get; set; }
    public string Type { get; set; }
    public string? FileName { get; set; }
    public string? ContentType { get; set; }
    public long? FileSize { get; set; }

    public AttachmentType MapType()
    {
        return Type?.ToLower() switch
        {
            "image" => AttachmentType.Image,
            "audio" => AttachmentType.Voice,
            "document" => AttachmentType.Document,
            "location" => AttachmentType.Location,
            _ => AttachmentType.Document
        };
    }
}
