using System.Text.Json.Serialization;

namespace Conversations.Application.Channels.WhatsApp;

public record WhatsAppDto
{
    public string Object { get; set; }
    public WhatsappEntryDto[] Entry { get; set; }
}

public record WhatsappEntryDto
{
    public string Id { get; set; }
    public WhatsappChangeDto[] Changes { get; set; }
}

public record WhatsappChangeDto
{
    public string Field { get; set; }
    public WhatsappValueDto Value { get; set; }
}

public record WhatsappValueDto
{
    [JsonPropertyName("messaging_product")]
    public string MessagingProduct { get; set; }
    public WhatsappMetadataDto Metadata { get; set; }
    public WhatsappContactDto[] Contacts { get; set; }
    public WhatsappMessageDto[] Messages { get; set; }
}

public record WhatsappMessageDto
{
    public string From { get; set; }
    public string To { get; set; }
    public string Id { get; set; }
    public string Timestamp { get; set; }
    public string Type { get; set; }
    public WhatsappTextDto Text { get; set; }
}

public record WhatsappTextDto
{
    public string Body { get; set; }
}

public record WhatsappContactDto
{
    [JsonPropertyName("wa_id")]
    public string WaId { get; set; }
    public WhatsappProfileDto Profile { get; set; }
}

public record WhatsappProfileDto
{
    public string Name { get; set; }
}

public record WhatsappMetadataDto
{
    [JsonPropertyName("display_phone_number")]
    public string DisplayPhoneNumber { get; set; }
    [JsonPropertyName("phone_number_id")]
    public string PhoneNumberId { get; set; }
}
