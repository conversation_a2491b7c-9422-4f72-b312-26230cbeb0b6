using Conversations.Domain.Chats;

namespace Conversations.Application.Abstractions;

public interface IMessageSender
{
    Task<SendMessageResult> SendMessageAsync(SendMessageRequest request);
}

public class SendMessageRequest
{
    public Guid MessageId { get; set; }
    public Guid ChatId { get; set; }
    public string BaseChatId { get; set; }
    public string RecipientIdentifier { get; set; }
    public string Content { get; set; }
    public MessageContentType ContentType { get; set; }
    public string? ReplyToExternalId { get; set; }
    public List<AttachmentRequest>? Attachments { get; set; }
}

public class AttachmentRequest
{
    public string Url { get; set; }
    public AttachmentType Type { get; set; }
    public string? FileName { get; set; }
    public string? ContentType { get; set; }
    public long? FileSize { get; set; }
}

public class SendMessageResult
{
    public bool Success { get; set; }
    public string ExternalMessageId { get; set; }
    public string? ErrorCode { get; set; }
    public string? ErrorMessage { get; set; }
}
