using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using Conversations.Domain.Chats;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Shared.Domain;

namespace Conversations.Infrastructure.External.WhatsAppIntegration;

public interface IWhatsAppApiClient
{
    Task<WhatsAppSendResponse> SendMessageAsync(string FromPhoneId, WhatsAppSendRequest request);
}

public class WhatsAppApiClient : IWhatsAppApiClient
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<WhatsAppApiClient> _logger;
    private readonly AppSettings _settings;

    public WhatsAppApiClient(
        HttpClient httpClient,
        AppSettings settings,
        ILogger<WhatsAppApiClient> logger)
    {
        _httpClient = httpClient;
        _logger = logger;
        _settings = settings;

        // Configure the HTTP client
        _httpClient.BaseAddress = new Uri(_settings.WhatsAppApiUrl);
        _httpClient.DefaultRequestHeaders.Authorization =
            new AuthenticationHeaderValue("Bearer", _settings.WhatsAppApiToken);
    }

    public async Task<WhatsAppSendResponse> SendMessageAsync(string FromPhoneId, WhatsAppSendRequest request)
    {
        try
        {
            var content = new StringContent(
                JsonSerializer.Serialize(request),
                Encoding.UTF8,
                "application/json");

            var response = await _httpClient.PostAsync("/" + FromPhoneId + "/messages", content);

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                var result = JsonSerializer.Deserialize<WhatsAppSendResponse>(responseContent);

                if (result != null)
                {
                    result.IsSuccess = true;
                    return result;
                }
            }

            // Handle error
            var errorContent = await response.Content.ReadAsStringAsync();
            _logger.LogError("WhatsApp API error: {StatusCode} - {Content}",
                response.StatusCode, errorContent);

            return new WhatsAppSendResponse
            {
                IsSuccess = false,
                ErrorCode = response.StatusCode.ToString(),
                ErrorMessage = errorContent
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending WhatsApp message");
            return new WhatsAppSendResponse
            {
                IsSuccess = false,
                ErrorCode = "Exception",
                ErrorMessage = ex.Message
            };
        }
    }
}

public class WhatsAppSendRequest
{
    public string messaging_product { get; set; } = "whatsapp";
    public string recipient_type { get; set; } = "individual";
    public string to { get; set; }
    public string type { get; set; } = "text";
    public WhatsAppText text { get; set; }
    public WhatsAppContext context { get; internal set; }

    //public List<WhatsAppAttachment>? Attachments { get; set; }

    public class WhatsAppText
    {
        public bool preview_url { get; set; } = true;
        public string body { get; set; }
    }

    public class WhatsAppContext
    {
        public string message_id { get; set; }
    }

    public class WhatsAppAttachment
    {
        public string Url { get; set; }
        public string Type { get; set; }
        public string? FileName { get; set; }
        public string? ContentType { get; set; }
        public long? FileSize { get; set; }
    }
}

public class WhatsAppSendResponse
{
    public string messaging_product { get; set; } = "whatsapp";
    public List<WhatsAppSendContactResponse>? contacts { get; set; }
    public List<WhatsAppSendMessagesResponse>? messages { get; set; }
    public bool IsSuccess { get; internal set; }
    public string ErrorCode { get; internal set; }
    public string ErrorMessage { get; internal set; }
}

public class WhatsAppSendContactResponse
{
    public string input { get; set; }
    public string wa_id { get; set; }
}

public class WhatsAppSendMessagesResponse
{
    public string id { get; set; }
}

