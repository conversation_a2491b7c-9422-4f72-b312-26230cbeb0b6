using Conversations.Application.Abstractions;
using Conversations.Domain.Chats;
using Microsoft.Extensions.Logging;

namespace Conversations.Infrastructure.External.WhatsAppIntegration;

public class WhatsAppService(
    IWhatsAppApiClient apiClient,
    ILogger<WhatsAppService> logger
) : IMessageSender
{
    private readonly IWhatsAppApiClient _apiClient = apiClient;
    private readonly ILogger<WhatsAppService> _logger = logger;

    public async Task<SendMessageResult> SendMessageAsync(SendMessageRequest request)
    {
        try
        {
            // Map content type
            string contentType = request.ContentType switch
            {
                MessageContentType.Text => "text",
                MessageContentType.Image => "image",
                MessageContentType.Voice => "audio",
                MessageContentType.Document => "document",
                MessageContentType.Location => "location",
                _ => "text"
            };

            // Map attachments if any
            List<WhatsAppSendRequest.WhatsAppAttachment>? attachments = null;
            if (request.Attachments != null && request.Attachments.Any())
            {
                attachments = request.Attachments.Select(a => new WhatsAppSendRequest.WhatsAppAttachment
                {
                    Url = a.Url,
                    Type = a.Type.ToString().ToLower(),
                    FileName = a.FileName,
                    ContentType = a.ContentType,
                    FileSize = a.FileSize
                }).ToList();
            }

            // Create the request
            var apiRequest = new WhatsAppSendRequest
            {
                to = request.RecipientIdentifier,
                type = contentType,
                text = new WhatsAppSendRequest.WhatsAppText
                {
                    body = request.Content
                }
            };
            if (!string.IsNullOrEmpty(request.ReplyToExternalId))
            {
                apiRequest.context = new WhatsAppSendRequest.WhatsAppContext
                {
                    message_id = request.ReplyToExternalId
                };
            }

            // Send the message
            var response = await _apiClient.SendMessageAsync(request.BaseChatId, apiRequest);

            if (response.IsSuccess)
            {
                return new SendMessageResult
                {
                    Success = true,
                    ExternalMessageId = response.messages[0].id
                };
            }
            else
            {
                _logger.LogError("Failed to send WhatsApp message: {ErrorCode} - {ErrorMessage}",
                    response.ErrorCode, response.ErrorMessage);

                return new SendMessageResult
                {
                    Success = false,
                    ErrorCode = response.ErrorCode,
                    ErrorMessage = response.ErrorMessage
                };
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending WhatsApp message");
            return new SendMessageResult
            {
                Success = false,
                ErrorCode = "Exception",
                ErrorMessage = ex.Message
            };
        }
    }
}
