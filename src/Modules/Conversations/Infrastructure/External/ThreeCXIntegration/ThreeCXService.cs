using System.Net;
using System.Net.Http.Json;
using Conversations.Application.Abstractions;
using Conversations.Domain.Calls;
using Shared.Application;
using Shared.Domain;

namespace Conversations.Infrastructure.External.ThreeCXIntegration;

public class ThreeCXService(
    AppSettings appSettings
) : IThreeCXService
{
    private readonly AppSettings _appSettings = appSettings;

    public Task<CallStatus> CheckCallStatus(string callId)
    {
        throw new NotImplementedException();
    }

    public Task<bool> EndCallAsync(string callId)
    {
        throw new NotImplementedException();
    }

    public Task<CallStatus> GetCallStatusAsync(string callId)
    {
        throw new NotImplementedException();
    }

    public Task<string> GetRecordingUrlAsync(string callId)
    {
        throw new NotImplementedException();
    }

    public Task<string> GetTranscriptionAsync(string callId)
    {
        throw new NotImplementedException();
    }

    public Task<CallInfo> InitiateCallAsync(string phoneNumber, Guid agentId)
    {
        throw new NotImplementedException();
    }

    public async Task<Result> StartAutoDialer(Guid autoDialerId, string queueNumber, List<string> targetNumbers)
    {
        var tokenResult = await GetAuthTokenAsync();
        var apiUrl = _appSettings.ThreeCXApiUrl;
        using var client = new HttpClient();
        client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", tokenResult.Value);
        var requestData = new
        {
            autoDialId = autoDialerId,
            targetNumbers,
            agentGroup = queueNumber,
            retryCount = 3,
            callDelayAfterSuccess = 10
        };
        var response = await client.PostAsJsonAsync($"{apiUrl}/api/AutoDialer/StartAutoDial", requestData);
        if (!response.IsSuccessStatusCode)
        {
            var error = await response.Content.ReadAsStringAsync();
            return Result.Failure($"Failed to start 3CX autodial: {error}");
        }
        return Result.Success();
    }

    public async Task<Result> CancelAutoDialer(Guid autoDialerId)
    {
        var tokenResult = await GetAuthTokenAsync();
        var apiUrl = _appSettings.ThreeCXApiUrl;
        using var client = new HttpClient();
        client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", tokenResult.Value);
        var response = await client.PostAsJsonAsync($"{apiUrl}/api/Control/CancelAutodial", autoDialerId);
        if (!response.IsSuccessStatusCode)
        {
            var error = await response.Content.ReadAsStringAsync();
            return Result.Failure($"Failed to cancel 3CX autodial: {error}");
        }
        return Result.Success();
    }

    private async Task<Result<string>> GetAuthTokenAsync()
    {
        using var client = new HttpClient();
        var loginData = new
        {
            username = _appSettings.ThreeCXApiUsername,
            password = _appSettings.ThreeCXApiPassword
        };
        var response = await client.PostAsJsonAsync($"{_appSettings.ThreeCXApiUrl}/api/Auth/Login", loginData);
        if (!response.IsSuccessStatusCode)
        {
            var error = await response.Content.ReadAsStringAsync();
            return Result.Failure<string>($"Failed to authenticate with 3CX API: {error}.");
        }
        var tokenResponse = await response.Content.ReadFromJsonAsync<TokenResponse>();
        return tokenResponse?.Token ?? Result.Failure<string>("Token not found in response");
    }

    public async Task<Result<List<CallHistoryDto>?>> GetCallHistory(string historyOfTheCall)
    {
        var tokenResult = await GetAuthTokenAsync();
        var apiUrl = _appSettings.ThreeCXApiUrl;
        using var client = new HttpClient();
        client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", tokenResult.Value);
        var response = await client.GetAsync($"{apiUrl}/api/reports/call-chain/" + historyOfTheCall);
        if (response.StatusCode == HttpStatusCode.OK)
        {
            var callHistory = await response.Content.ReadFromJsonAsync<CallHistoryMainDto>();
            return Result.Success(callHistory?.HistoryOfTheCall);
        }
        else if (response.StatusCode == HttpStatusCode.NotFound)
        {
            return Result.Success<List<CallHistoryDto>?>([]);
        }
        else
        {
            var error = await response.Content.ReadAsStringAsync();
            return Result.Failure<List<CallHistoryDto>?>($"Failed to start 3CX callhistory: {error}");
        }

    }

    public async Task<Result<CallReportResultDto?>> GetCallReport(
        string[]? extensionNumbers,
        DateTime startDate,
        DateTime endDate,
        string? direction,
        bool? isAnswered,
        string? caller,
        string? callee,
        int? pageNumber,
        int? pageSize)
    {
        var tokenResult = await GetAuthTokenAsync();
        var apiUrl = _appSettings.ThreeCXApiUrl;
        using var client = new HttpClient();
        client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", tokenResult.Value);
        var requestData = new
        {
            extensionNumbers,
            startDate,
            endDate,
            direction,
            isAnswered,
            caller,
            callee,
            pageNumber,
            pageSize
        };
        var response = await client.PostAsJsonAsync($"{apiUrl}/api/reports/call-history", requestData);
        if (response.StatusCode == HttpStatusCode.OK)
        {
            var callHistory = await response.Content.ReadFromJsonAsync<CallReportResultDto?>();
            return Result.Success(callHistory);
        }
        else if (response.StatusCode == HttpStatusCode.NotFound)
        {
            return Result.Success<CallReportResultDto?>(null);
        }
        else
        {
            var error = await response.Content.ReadAsStringAsync();
            return Result.Failure<CallReportResultDto?>($"Failed to start 3CX callreport: {error}");

        }
    }

    private class CallHistoryMainDto
    {
        public List<CallHistoryDto>? HistoryOfTheCall { get; set; }
    }

    private class TokenResponse
    {
        public string Token { get; set; } = null!;
    }

    private class QueueResponse
    {
        public string Name { get; set; } = null!;
        public string Number { get; set; } = null!;
        public List<QueueAgent> Agents { get; set; } = null!;
    }

    private class QueueAgent
    {
        public string Name { get; set; } = null!;
        public string Surname { get; set; } = null!;
        public string Number { get; set; } = null!;
    }

    private class CallChainResponse
    {
        public string CallHistoryId { get; set; } = null!;
        public CallHistoryResponse[] HistoryOfTheCall { get; set; } = null!;
    }

    private class CallHistoryResponse
    {
        public Guid CdrId { get; set; }
        public Guid CallHistoryId { get; set; }
        public Guid SourceParticipantId { get; set; }
        public string SourceEntityType { get; set; }
        public string SourceDnNumber { get; set; }
        public string SourceParticipantPhoneNumber { get; set; }
        public string SourceParticipantName { get; set; }
        public string SourceParticipantTrunkDid { get; set; }
        public string CreationMethod { get; set; }
        public string TerminationReason { get; set; }
        public Guid BaseCdrId { get; set; }
        public Guid OriginatingCdrId { get; set; }
        public Guid MainCallHistoryId { get; set; }
        public int IdRecording { get; set; }
        public int? ClParticipantsId { get; set; }
        public string RecordingUrl { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public string Transcription { get; set; }
        public string Summary { get; set; }
        public Guid RecordingCdrId { get; set; }
    }
}

// using Conversations.Domain;
// using Microsoft.Extensions.Configuration;
// using Microsoft.Extensions.Logging;

// namespace Conversations.Infrastructure.External.ThreeCXIntegration;

// public class ThreeCXService : IThreeCXService
// {
//     private readonly HttpClient _httpClient;
//     private readonly ILogger<ThreeCXService> _logger;
//     private readonly string _apiUrl;
//     private readonly string _apiKey;

//     public ThreeCXService(
//         HttpClient httpClient,
//         IConfiguration configuration,
//         ILogger<ThreeCXService> logger)
//     {
//         _httpClient = httpClient;
//         _logger = logger;
//         _apiUrl = configuration["ThreeCX:ApiUrl"];
//         _apiKey = configuration["ThreeCX:ApiKey"];

//         _httpClient.BaseAddress = new Uri(_apiUrl);
//         _httpClient.DefaultRequestHeaders.Add("X-API-Key", _apiKey);
//     }

//     public async Task<CallInfo> InitiateCallAsync(string phoneNumber, Guid agentId)
//     {
//         try
//         {
//             var response = await _httpClient.PostAsJsonAsync("calls/initiate", new
//             {
//                 phoneNumber,
//                 agentId = agentId.ToString()
//             });

//             response.EnsureSuccessStatusCode();
//             var result = await response.Content.ReadFromJsonAsync<CallInfoResponse>();

//             return new CallInfo(result.CallId, result.RecordingUrl);
//         }
//         catch (Exception ex)
//         {
//             _logger.LogError(ex, "3CX çağrı başlatma hatası: {PhoneNumber}, {AgentId}", phoneNumber, agentId);
//             throw new ThreeCXException("Çağrı başlatılamadı", ex);
//         }
//     }

//     public async Task<bool> EndCallAsync(string callId)
//     {
//         try
//         {
//             var response = await _httpClient.PostAsync($"calls/{callId}/end", null);
//             response.EnsureSuccessStatusCode();
//             return true;
//         }
//         catch (Exception ex)
//         {
//             _logger.LogError(ex, "3CX çağrı sonlandırma hatası: {CallId}", callId);
//             throw new ThreeCXException("Çağrı sonlandırılamadı", ex);
//         }
//     }

//     public async Task<string> GetRecordingUrlAsync(string callId)
//     {
//         try
//         {
//             var response = await _httpClient.GetAsync($"calls/{callId}/recording");
//             response.EnsureSuccessStatusCode();
//             var result = await response.Content.ReadFromJsonAsync<RecordingResponse>();
//             return result.Url;
//         }
//         catch (Exception ex)
//         {
//             _logger.LogError(ex, "3CX kayıt URL'si alma hatası: {CallId}", callId);
//             throw new ThreeCXException("Kayıt URL'si alınamadı", ex);
//         }
//     }

//     public async Task<CallStatus> GetCallStatusAsync(string callId)
//     {
//         try
//         {
//             var response = await _httpClient.GetAsync($"calls/{callId}/status");
//             response.EnsureSuccessStatusCode();
//             var result = await response.Content.ReadFromJsonAsync<StatusResponse>();
//             return MapStatus(result.Status);
//         }
//         catch (Exception ex)
//         {
//             _logger.LogError(ex, "3CX çağrı durum alma hatası: {CallId}", callId);
//             throw new ThreeCXException("Çağrı durumu alınamadı", ex);
//         }
//     }

//     public async Task<string> GetTranscriptionAsync(string callId)
//     {
//         try
//         {
//             var response = await _httpClient.GetAsync($"calls/{callId}/transcription");
//             response.EnsureSuccessStatusCode();
//             var result = await response.Content.ReadFromJsonAsync<TranscriptionResponse>();
//             return result.Text;
//         }
//         catch (Exception ex)
//         {
//             _logger.LogError(ex, "3CX transkripsiyon alma hatası: {CallId}", callId);
//             throw new ThreeCXException("Transkripsiyon alınamadı", ex);
//         }
//     }

//     private static CallStatus MapStatus(string status) => status switch
//     {
//         "started" => CallStatus.Started,
//         "in-progress" => CallStatus.InProgress,
//         "on-hold" => CallStatus.OnHold,
//         "ended" => CallStatus.Ended,
//         "failed" => CallStatus.Failed,
//         "missed" => CallStatus.Missed,
//         _ => throw new ThreeCXException($"Bilinmeyen çağrı durumu: {status}")
//     };
// }

// internal record CallInfoResponse(string CallId, string RecordingUrl);
// internal record RecordingResponse(string Url);
// internal record StatusResponse(string Status);
// internal record TranscriptionResponse(string Text);
