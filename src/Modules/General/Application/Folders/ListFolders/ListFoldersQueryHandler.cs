using General.Application.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace General.Application.Folders.ListFolders;

internal sealed class ListFoldersQueryHandler(
    IGeneralDbContext dbContext
) : IRequestHandler<ListFoldersQuery, PagedResult<FolderDto>>
{
    private readonly IGeneralDbContext _dbContext = dbContext;

    public async Task<PagedResult<FolderDto>> Handle(ListFoldersQuery request, CancellationToken cancellationToken)
    {
        var query = _dbContext.Folder.AsQueryable();

        // Üst klasöre göre filtrele
        if (request.ParentFolderId.HasValue)
        {
            query = query.Where(f => f.ParentFolderId == request.ParentFolderId.Value);
        }
        else
        {
            // Kök klasörleri getir
            query = query.Where(f => f.ParentFolderId == null);
        }

        // Arama terimine göre filtrele
        if (!string.IsNullOrWhiteSpace(request.SearchTerm))
        {
            var searchTerm = request.SearchTerm.ToLower();
            query = query.Where(f => f.Name.ToLower().Contains(searchTerm));
        }

        // Get total count
        var totalCount = await _dbContext.Folder.CountAsync(cancellationToken);
        var filteredCount = await query.CountAsync(cancellationToken);

        // Apply pagination
        var folders = await query
            .OrderBy(f => f.Name)
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .Select(f => new FolderDto(
                f.Id,
                f.Name,
                f.ParentFolderId,
                f.Path,
                f.Attributes,
                f.InsertDate,
                f.UpdateDate))
            .ToListAsync(cancellationToken);

        var result = PagedResult<FolderDto>.Success(folders);
        result.PageNumber = request.PageNumber;
        result.PageSize = request.PageSize;
        result.Count = totalCount;
        result.FilteredCount = filteredCount;

        return result;
    }
}
