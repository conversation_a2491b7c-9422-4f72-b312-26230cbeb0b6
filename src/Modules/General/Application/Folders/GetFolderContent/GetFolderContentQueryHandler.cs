using General.Application.Abstractions;
using General.Application.Files;
using General.Domain;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace General.Application.Folders.GetFolderContent;

internal sealed class GetFolderContentQueryHandler(
    IGeneralDbContext dbContext
) : IRequestHandler<GetFolderContentQuery, Result<FolderContentDto>>
{
    private readonly IGeneralDbContext _dbContext = dbContext;

    public async Task<Result<FolderContentDto>> Handle(GetFolderContentQuery request, CancellationToken cancellationToken)
    {
        var folder = await _dbContext.Folder
            .FirstOrDefaultAsync(f => f.Id == request.FolderId, cancellationToken);

        if (folder == null)
        {
            return Result.Failure<FolderContentDto>(GeneralErrors.FolderNotFound(request.FolderId));
        }

        // Alt klasörleri getir
        var subFolders = await _dbContext.Folder
            .Where(f => f.ParentFolderId == request.FolderId)
            .OrderBy(f => f.Name)
            .Select(f => new FolderDto(
                f.Id,
                f.Name,
                f.ParentFolderId,
                f.Path,
                f.Attributes,
                f.InsertDate,
                f.UpdateDate))
            .ToListAsync(cancellationToken);

        // Dosyaları getir
        var files = await _dbContext.File
            .Where(f => f.FolderId == request.FolderId)
            .OrderBy(f => f.FileName)
            .Select(f => new FileDto(
                f.Id,
                f.FileName,
                f.OriginalFileName,
                f.FileExtension,
                f.MimeType,
                f.FileSizeInBytes,
                f.FolderId,
                f.StoragePath,
                f.Metadata,
                f.InsertDate,
                f.UpdateDate))
            .ToListAsync(cancellationToken);

        var folderContent = new FolderContentDto(
            folder.Id,
            folder.Name,
            folder.ParentFolderId,
            folder.Path,
            subFolders,
            files);

        return Result.Success(folderContent);
    }
}
