namespace General.Application.Files;

public record FileDto(
    Guid Id,
    string FileName,
    string OriginalFileName,
    string FileExtension,
    string MimeType,
    long FileSizeInBytes,
    Guid FolderId,
    string StoragePath,
    Dictionary<string, object> Metadata,
    DateTime InsertDate,
    DateTime? UpdateDate);

public record FileDetailDto(
    Guid Id,
    string FileName,
    string OriginalFileName,
    string FileExtension,
    string MimeType,
    long FileSizeInBytes,
    Guid FolderId,
    string FolderName,
    string StoragePath,
    Dictionary<string, object> Metadata,
    DateTime InsertDate,
    DateTime? UpdateDate);
