using General.Application.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace General.Application.Files.ListFiles;

internal sealed class ListFilesQueryHandler(
    IGeneralDbContext dbContext
) : IRequestHandler<ListFilesQuery, PagedResult<FileDto>>
{
    private readonly IGeneralDbContext _dbContext = dbContext;

    public async Task<PagedResult<FileDto>> Handle(ListFilesQuery request, CancellationToken cancellationToken)
    {
        var query = _dbContext.File.AsQueryable();

        // Klasöre göre filtrele
        if (request.FolderId.HasValue)
        {
            query = query.Where(f => f.FolderId == request.FolderId.Value);
        }

        // Arama terimine göre filtrele
        if (!string.IsNullOrWhiteSpace(request.SearchTerm))
        {
            var searchTerm = request.SearchTerm.ToLower();
            query = query.Where(f =>
                f.FileName.ToLower().Contains(searchTerm) ||
                f.OriginalFileName.ToLower().Contains(searchTerm));
        }

        // Get total count
        var totalCount = await _dbContext.File.CountAsync(cancellationToken);
        var filteredCount = await query.CountAsync(cancellationToken);

        // Sayfalama
        var files = await query
            .OrderByDescending(f => f.InsertDate)
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .Select(f => new FileDto(
                f.Id,
                f.FileName,
                f.OriginalFileName,
                f.FileExtension,
                f.MimeType,
                f.FileSizeInBytes,
                f.FolderId,
                f.StoragePath,
                f.Metadata,
                f.InsertDate,
                f.UpdateDate))
            .ToListAsync(cancellationToken);

        var result = PagedResult<FileDto>.Success(files);
        result.PageNumber = request.PageNumber;
        result.PageSize = request.PageSize;
        result.Count = totalCount;
        result.FilteredCount = filteredCount;

        return result;
    }
}
