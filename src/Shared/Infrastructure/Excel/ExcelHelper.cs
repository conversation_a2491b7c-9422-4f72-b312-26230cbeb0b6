using System.Reflection;
using ClosedXML.Excel;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;
using Shared.Application;
using Shared.Infrastructure.Localization;


namespace Shared.Infrastructure.Excel;

public class ExcelHelper
{
    private readonly ExcelImportSettings _settings;
    private readonly ILocalizer _localizer;

    public ExcelHelper(IOptions<ExcelImportSettings> settings, ILocalizer localizer)
    {
        _settings = settings.Value;
        _localizer = localizer;
    }

    public async Task<Result<ImportStructureResponse>> SaveTempAndGetStructureAsync<T>(IFormFile file, T model)
    {
        if (file == null || file.Length == 0)
        {
            return Result.Failure<ImportStructureResponse>(
                Error.Validation(
                    "File.Empty",
                    _localizer.Get("FileEmpty")
                )
            );
        }

        var ext = Path.GetExtension(file.FileName).ToLowerInvariant();
        if (!_settings.AllowedExtensions.Contains(ext))
        {
            return Result.Failure<ImportStructureResponse>(
                Error.Validation(
                    "File.InvalidExtension",
                    _localizer.Get("FileExtensionNotSupported", new { Extensions = string.Join(", ", _settings.AllowedExtensions) })
                )
            );
        }

        if (file.Length > _settings.MaxFileSizeMb * 1024 * 1024)
        {
            return Result.Failure<ImportStructureResponse>(
                Error.Validation(
                    "File.MaxSizeExceeded",
                    _localizer.Get("FileSizeExceeded", new { MaxMb = _settings.MaxFileSizeMb })
                )
            );
        }

        var tempDir = Path.Combine(Path.GetTempPath(), _settings.TempFolder);
        Directory.CreateDirectory(tempDir);

        var tempFilePath = Path.Combine(tempDir, $"{Guid.NewGuid()}{ext}");

        await using (var stream = new FileStream(tempFilePath, FileMode.Create))
        {
            await file.CopyToAsync(stream);
        }

        var structure = GetExcelStructure(tempFilePath, ext);

        var modelColumns = typeof(T).GetProperties()
            .Where(p => Attribute.IsDefined(p, typeof(ExcelImportableAttribute)))
            .Select(p =>
            {
                var attr = p.GetCustomAttribute<ExcelImportableAttribute>();
                return _localizer.Get(attr?.LocalizerKey ?? p.Name);
            })
            .ToList();


        return Result.Success(new ImportStructureResponse
        {
            FilePath = tempFilePath,
            Sheets = structure,
            ModelColumns = typeof(T).GetProperties()
                .Where(p => Attribute.IsDefined(p, typeof(ExcelImportableAttribute)))
                .Select(p =>
                {
                    var attr = p.GetCustomAttribute<ExcelImportableAttribute>();
                    return _localizer.Get(attr?.LocalizerKey ?? p.Name);
                })
                .ToList()
        });

    }

    public byte[] ExportToExcel<T>(List<T> data, string sheetName = "Sheet1")
    {
        using var workbook = new XLWorkbook();
        var worksheet = workbook.Worksheets.Add(sheetName);

        var properties = typeof(T).GetProperties()
            .Where(p => Attribute.IsDefined(p, typeof(ExcelExportableAttribute)))
            .ToList();

        // Header
        for (int i = 0; i < properties.Count; i++)
        {
            var attr = properties[i].GetCustomAttribute<ExcelExportableAttribute>();
            var headerName = _localizer.Get(attr?.LocalizerKey ?? properties[i].Name);
            worksheet.Cell(1, i + 1).Value = headerName;
            worksheet.Cell(1, i + 1).Style.Font.Bold = true;
        }

        // Data Rows
        for (int row = 0; row < data.Count; row++)
        {
            for (int col = 0; col < properties.Count; col++)
            {
                var value = properties[col].GetValue(data[row]);
                worksheet.Cell(row + 2, col + 1).Value = value?.ToString() ?? "";
            }
        }

        worksheet.Columns().AdjustToContents();

        using var stream = new MemoryStream();
        workbook.SaveAs(stream);
        return stream.ToArray();
    }


    private List<SheetStructure> GetExcelStructure(string filePath, string extension)
    {
        var result = new List<SheetStructure>();

        if (extension.Equals(".csv", StringComparison.OrdinalIgnoreCase))
        {
            var lines = File.ReadLines(filePath).ToList();
            if (lines.Count > 0)
            {
                var columns = lines[0].Split(',').Select(x => x.Trim()).ToList();
                result.Add(new SheetStructure
                {
                    SheetName = Path.GetFileNameWithoutExtension(filePath),
                    Columns = columns
                });
            }
        }
        else
        {
            using var workbook = new XLWorkbook(filePath);
            foreach (var worksheet in workbook.Worksheets)
            {
                var columns = new List<string>();
                var firstRow = worksheet.FirstRowUsed();

                if (firstRow != null)
                {
                    foreach (var cell in firstRow.CellsUsed())
                    {
                        var colName = cell.GetString()?.Trim();
                        if (!string.IsNullOrEmpty(colName))
                        {
                            columns.Add(colName);
                        }
                    }
                }

                result.Add(new SheetStructure
                {
                    SheetName = worksheet.Name,
                    Columns = columns
                });
            }
        }

        return result;
    }

    public List<Dictionary<string, object>> ReadExcelDataWithMapping(string tempFileName, string sheetName)
    {
        var tempDir = Path.Combine(Path.GetTempPath(), _settings.TempFolder);
        var filePath = Path.Combine(tempDir, tempFileName);

        if (!File.Exists(filePath))
        {
            throw new FileNotFoundException(_localizer.Get("FileNotFound"));
        }

        var result = new List<Dictionary<string, object>>();

        using var workbook = new XLWorkbook(filePath);
        var worksheet = workbook.Worksheet(sheetName);
        var firstRow = worksheet.FirstRowUsed();
        var headerCells = firstRow.CellsUsed().ToList();
        var headerDict = headerCells.ToDictionary(c => c.Address.ColumnNumber, c => c.GetString().Trim());

        foreach (var dataRow in worksheet.RowsUsed().Skip(1))
        {
            var rowData = new Dictionary<string, object>();

            foreach (var cell in dataRow.CellsUsed())
            {
                if (headerDict.TryGetValue(cell.Address.ColumnNumber, out var excelColName))
                {
                    rowData[excelColName] = cell.GetString();
                }
            }

            result.Add(rowData);
        }

        return result;
    }

    public class ImportStructureResponse
    {
        public string FilePath { get; set; } = string.Empty;
        public string TempFileName => Path.GetFileName(FilePath);
        public List<string> ModelColumns { get; set; } = new();
        public List<SheetStructure> Sheets { get; set; } = new();
    }

    public class SheetStructure
    {
        public string SheetName { get; set; } = string.Empty;
        public List<string> Columns { get; set; } = new();
    }
}
