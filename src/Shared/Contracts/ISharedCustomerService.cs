using Shared.Application;

namespace Shared.Contracts;

public interface ISharedCustomerService
{
    Task<Result<SharedCustomerDto>> GetCustomerAsync(Guid customerId);
    Task<Result<SharedCustomerDto>> GetCustomerAsync(string phone);
    Task<Result<SharedCustomerDto>> GetTempCustomerAsync(string phone);
    Task<Result<List<SharedCustomerDto>>> GetCustomerByIdsAsync(List<Guid> customerIds);
    Task<Result<List<SharedCustomerDto>>> GetCustomersByPhonesAsync(List<string> phones);
    Task<Result<List<SharedNotificationWayDto>>> GetNotificationWaysAsync();
}
