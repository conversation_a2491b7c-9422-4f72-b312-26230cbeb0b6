using Shared.Application;

namespace Shared.Contracts;

public interface ISharedUserService
{
    Task<Result<SharedUserDto>> GetUserAsync(Guid userId);
    Task<Result<SharedUserDto>> GetUserAsync(string extension);
    Task<List<SharedUserDto>> GetUsersByIdsAsync(List<Guid> userIds);
    Task<List<SharedUserDto>> GetUsersByExtensionsAsync(List<string> userIds);
    Task<bool> HasPermissionAsync(Guid userId, Guid[] roleIds, string permission);
    Task<bool> HasPermissionAsync(Guid userId, string permission);
}
