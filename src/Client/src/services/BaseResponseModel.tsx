export type Response = {
  Count: number;
  FilteredCount: number;
  IsSuccess: boolean;
  PageNumber: number;
  PageSize: number;
};

export type DataResponse<T> = Response & {
  Validations: Array<any>;
  code: number;
  data: any;
  errors: any[];
  internalMessage: string;
  message: string;
  success: boolean;
  PageIndex: number;
  PageSize: number;
  Count: number;
  FilteredCount: number;
  SortProperty: string;
  SortType: string;
  TotalPageSize: number;
  Value: T | T[]
};

export type ListResponse<T> = Response & {
  data: T[];
  errors: T[];
  internalMessage: string;
  message: string;
  success: boolean;
  PageSize: number;
  PageIndex: number;
  Count: number;
  FilteredCount: number;
  SortProperty: string;
  SortType: string;
  TotalCount: number;
};
