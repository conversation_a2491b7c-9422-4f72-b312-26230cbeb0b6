import React, { useRef, useCallback } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, useJs<PERSON><PERSON>Loader } from "@react-google-maps/api";

const GOOGLE_MAP_LIBRARIES = ["places"];

const SimpleMapComponent = ({
    marker,
    onMarkerChange,
    onAddressChange,
    className = ""
}) => {
    const geocoderRef = useRef(null);

    // Google Maps API yükleyici
    const { isLoaded } = useJsApiLoader({
        id: "google-map-script",
        googleMapsApiKey: import.meta.env.VITE_GOOGLE_MAP_API || "",
        libraries: GOOGLE_MAP_LIBRARIES,
    });

    // <PERSON><PERSON><PERSON> koordinatları
    const defaultCenter = {
        lat: 38.733509,
        lng: 35.485397,
    };

    // Geocoder'ı initialize et
    if (!geocoderRef.current && isLoaded) {
        geocoderRef.current = new google.maps.Geocoder();
    }

    // <PERSON>ta tıklama handler'ı
    const handleMapClick = useCallback(async (event) => {
        const latLng = event.latLng;
        if (!latLng) return;

        const coordinates = {
            lat: latLng.lat(),
            lng: latLng.lng(),
        };

        // Parent component'e marker değişikliğini bildir
        onMarkerChange?.(coordinates);

        // Reverse geocoding ile adres al
        if (geocoderRef.current) {
            geocoderRef.current.geocode({ location: latLng }, (results, status) => {
                if (status === "OK" && results && results[0]) {
                    const formattedAddress = results[0].formatted_address;
                    onAddressChange?.(formattedAddress);
                } else {
                    onAddressChange?.(`${coordinates.lat.toFixed(6)}, ${coordinates.lng.toFixed(6)}`);
                }
            });
        }
    }, [onMarkerChange, onAddressChange]);

    // Harita container style
    const mapContainerStyle = {
        width: "100%",
        height: "320px",
        borderRadius: "12px"
    };

    // Custom marker icon
    const markerIconConfig = isLoaded ? {
        path: google.maps.SymbolPath.BACKWARD_CLOSED_ARROW,
        scale: 8,
        fillColor: "#ef4444",
        fillOpacity: 1,
        strokeWeight: 3,
        strokeColor: "#ffffff",
        strokeOpacity: 1,
    } : null;

    // Loading state
    if (!isLoaded) {
        return (
            <div className={`max-w-2xl mx-auto p-0 ${className}`}>
                <div className="bg-white rounded-2xl shadow-sm border border-slate-200 p-8">
                    <div className="flex items-center justify-center h-80">
                        <div className="text-center">
                            <div className="inline-flex items-center justify-center w-16 h-16 bg-slate-100">
                                <svg className="w-8 h-8 text-slate-400 animate-spin" fill="none" viewBox="0 0 24 24">
                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                            </div>
                            <p className="text-slate-500 font-medium">Google Maps yükleniyor...</p>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className={`max-w-2xl mx-auto ${className}`}>
            <div className="bg-white shadow-sm border border-slate-200 overflow-hidden">

                {/* Header */}
                <div className="p-3 border-slate-100 pb-0">
                    <div className="flex items-center gap-3">
                        <div>
                            <p className="text-slate-500 text-sm">
                                Haritada istediğiniz konumu işaretleyin
                            </p>
                        </div>
                    </div>
                </div>

                {/* Google Map */}
                <div className="p-2 bt-0">
                    <div className="overflow-hidden shadow-inner border border-slate-200">
                        <GoogleMap
                            mapContainerStyle={mapContainerStyle}
                            center={marker || defaultCenter}
                            zoom={marker ? 16 : 13}
                            onClick={handleMapClick}
                            options={{
                                streetViewControl: false,
                                mapTypeControl: false,
                                fullscreenControl: false,
                                zoomControl: true,
                                gestureHandling: 'greedy',
                                styles: [
                                    {
                                        featureType: "poi",
                                        elementType: "labels",
                                        stylers: [{ visibility: "off" }]
                                    },
                                    {
                                        featureType: "transit",
                                        elementType: "labels",
                                        stylers: [{ visibility: "off" }]
                                    }
                                ]
                            }}
                        >
                            {marker && (
                                <Marker
                                    position={marker}
                                    icon={markerIconConfig}
                                    animation={google.maps.Animation.DROP}
                                    title="Seçili Konum"
                                />
                            )}
                        </GoogleMap>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default SimpleMapComponent;
