import React from "react";
import { Upload, message, Col, List, Button } from "antd";
import type { UploadProps, UploadFile } from "antd/es/upload/interface";
import type { ColProps } from "antd/es/col";
import { InboxOutlined, DeleteOutlined } from "@ant-design/icons";

const { Dragger } = Upload;

interface GeneralUploadFileProps extends ColProps {
  fileList: UploadFile[];
  setFileList: (files: UploadFile[]) => void;
  accepts?: string;
  multiple?: boolean;
  maxCount?: number;
  disabled?: boolean;
  customRequest?: UploadProps["customRequest"];
  showFileList?: boolean; // Dosya listesini göster/gizle
}

const GeneralUploadFile: React.FC<GeneralUploadFileProps> = ({
  fileList,
  setFileList,
  accepts = "*",
  multiple = true,
  maxCount,
  disabled = false,
  customRequest,
  showFileList = true,
  span = 24,
  xs,
  sm,
  md,
  lg,
  xl,
  xxl,
  ...colProps
}) => {
  const handleChange: UploadProps["onChange"] = (info) => {
    let newFileList = [...info.fileList];
    if (maxCount && newFileList.length > maxCount) {
      message.warning(`En fazla ${maxCount} dosya yüklenebilir.`);
      newFileList = newFileList.slice(0, maxCount);
    }
    setFileList(newFileList);
  };

  const removeFile = (file: UploadFile) => {
    const newFileList = fileList.filter(item => item.uid !== file.uid);
    setFileList(newFileList);
  };

  return (
    <Col
      span={span}
      xs={xs}
      sm={sm}
      md={md}
      lg={lg}
      xl={xl}
      xxl={xxl}
      {...colProps}
    >
      <Dragger
        fileList={fileList}
        onChange={handleChange}
        accept={accepts}
        multiple={multiple}
        disabled={disabled}
        customRequest={customRequest}
        beforeUpload={() => false}
        showUploadList={false} // Varsayılan listeyi gizle
      >
        <p className="ant-upload-drag-icon">
          <InboxOutlined />
        </p>
        <p className="ant-upload-text">
          Dosyaları buraya sürükleyin veya tıklayın
        </p>
        <p className="ant-upload-hint">
          {accepts != "*" ? `Yalnızca şu türler kabul edilir: ${accepts}` : "Her türlü dosya yüklenebilir."}
        </p>
      </Dragger>

      {/* Özel dosya listesi */}
      {showFileList && fileList.length > 0 && (
        <div style={{ marginTop: '12px' }}>
          <List
            size="small"
            dataSource={fileList}
            renderItem={(file) => (
              <List.Item
                actions={[
                  <Button
                    type="text"
                    danger
                    size="small"
                    icon={<DeleteOutlined />}
                    onClick={() => removeFile(file)}
                  />
                ]}
              >
                <List.Item.Meta
                  title={file.name}
                  description={`${Math.round((file.size || 0) / 1024)} KB`}
                />
              </List.Item>
            )}
          />
        </div>
      )}
    </Col>
  );
};

export default GeneralUploadFile;