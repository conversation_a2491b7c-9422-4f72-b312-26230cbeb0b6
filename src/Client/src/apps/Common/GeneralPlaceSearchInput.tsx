import { FC, useEffect, useState } from "react";
import { LoadScript, Autocomplete } from "@react-google-maps/api";
import { Col, FormInstance } from "antd";

const googleLibraries: any[] = ["places"];

interface GeneralPlaceSearchInputProps {
  xs?: number;
  sm?: number;
  md?: number;
  lg?: number;
  xl?: number;
  form: FormInstance;
  setAddressInfoes: (info: { country: string; state: string; city: string }) => void;
  inputValue: string;
  setInputValue: any;
  setMarker: (coords: { lat: number; lng: number }) => void;
}

const GeneralPlaceSearchInput: FC<GeneralPlaceSearchInputProps> = ({
  xs,
  sm,
  md,
  lg,
  xl,
  form,
  setAddressInfoes,
  inputValue,
  setInputValue,
  setMarker
}) => {
  const [autocomplete, setAutocomplete] =
    useState<google.maps.places.Autocomplete | null>(null);
  const [autocompleteService, setAutocompleteService] =
    useState<google.maps.places.AutocompleteService | null>(null);

  const onLoad = (autocompleteInstance: google.maps.places.Autocomplete) => {
    setAutocomplete(autocompleteInstance);
  };

  useEffect(() => {
    const interval = setInterval(() => {
      if (window.google && google.maps.places) {
        setAutocompleteService(new google.maps.places.AutocompleteService());
        clearInterval(interval);
      }
    }, 100);
    return () => clearInterval(interval);
  }, []);

  const handlePlaceChanged = () => {
    if (!autocomplete) return;

    const place = autocomplete.getPlace();

    if (place?.geometry?.location) {
      const lat = place.geometry.location.lat();
      const lng = place.geometry.location.lng();

      if (lat && lng && place.formatted_address) {
        // Form field'ını güncelle
        form.setFieldValue("Adress", place.formatted_address);

        // Marker pozisyonunu güncelle
        setMarker({ lat, lng });

        // Input değerini güncelle
        setInputValue(place.formatted_address);

        try {
          const addressComponents = place.address_components;
          if (addressComponents) {
            let country = "";
            let state = "";
            let city = "";

            for (const component of addressComponents) {
              const types = component.types;
              if (types.includes("country")) country = component?.long_name;
              if (types.includes("administrative_area_level_1")) state = component?.long_name;
              if (types.includes("locality") || types.includes("administrative_area_level_2")) city = component?.long_name;
            }

            setAddressInfoes({ country, state, city });
          }
        } catch (error) {
          console.error("Adres detaylarını işlerken hata oluştu:", error);
        }
      }
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setInputValue(value);

    // Manuel input değişikliklerinde marker'ı temizlemek isterseniz:
    // setMarker({ lat: 0, lng: 0 }); // veya null değer gönderebilirsiniz
  };

  return (
    <Col xs={xs} sm={sm} md={md} lg={lg} xl={xl}>
      <LoadScript
        googleMapsApiKey={import.meta.env.VITE_GOOGLE_MAP_API || ""} // API key düzeltildi
        libraries={googleLibraries}
        language="tr"
      >
        <Autocomplete onLoad={onLoad} onPlaceChanged={handlePlaceChanged}>
          <input
            type="text"
            name="Search"
            placeholder="Adres ara"
            className="ant-input w-full place-search-input !h-[33px] !border !border-[#d9d9d9]"
            style={{ height: 40, padding: "0 12px", borderRadius: 4 }}
            value={inputValue}
            onChange={handleInputChange}
          />
        </Autocomplete>
      </LoadScript>
    </Col>
  );
};

export default GeneralPlaceSearchInput;