import { useDispatch } from "react-redux";
import { FC, useEffect, useState } from "react";
import { Input } from "antd";
import { SearchOutlined } from "@ant-design/icons";
import { useDebouncedCallback } from "use-debounce";
import { useTranslation } from "react-i18next";

interface SearchProps {
  filter: Record<string, any>;
  filterActionFunc: (payload: { [key: string]: Record<string, any> }) => any;
  filterKey: string;
  placeholder?: string;
  searchFieldName: string;
  isShowSearchIcon?: boolean
  isShowBorder?: boolean
}

const GeneralSearch: FC<SearchProps> = (props) => {
  const globalFilter = props.filter;
  const dispatch = useDispatch();
  const { t } = useTranslation();

  const [searchValue, setSearchValue] = useState<string | undefined>(undefined);

  const debounce = useDebouncedCallback((inputValue: string) => {
    const value = inputValue?.trim();
    const newFilter = { ...globalFilter };

    if (value) {
      newFilter[props.searchFieldName] = value;
    } else {
      delete newFilter[props.searchFieldName];
    }

    dispatch(
      props.filterActionFunc({
        [props.filterKey]: newFilter,
      })
    );
  }, 1000);

  useEffect(() => {
    setSearchValue(globalFilter[props.searchFieldName] || "");
  }, [globalFilter]);

  return (
    <div className="!flex items-center">
      <Input
        allowClear
        prefix={props.isShowSearchIcon === false ? undefined : <SearchOutlined className="!text-lg !text-black" />}
        className={`${props.isShowBorder ? "" : "!border-0"} !w-full !text-xs !rounded-none`}
        placeholder={props.placeholder || t("generalSearch")}
        onChange={(e) => {
          setSearchValue(e.target.value);
          debounce(e.target.value);
        }}
        value={searchValue}
      />
    </div>
  );
};

export default GeneralSearch;
