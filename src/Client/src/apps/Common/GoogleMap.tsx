import React, { FC, useMemo, useRef } from "react";
import markerIcon from "@/assets/Images/marker.png";
import { Google<PERSON><PERSON>, Marker, useJsApiLoader } from "@react-google-maps/api";
import { Col, Row, Typography } from "antd";
import { FormInstance } from "antd/lib";

interface CustomGoogleMapProps {
  onFinish?: any;
  form?: FormInstance;
  marker: google.maps.LatLngLiteral | null;
  setMarker: (coords: google.maps.LatLngLiteral) => void;
  onAddressUpdate?: (address: string, coordinates?: google.maps.LatLngLiteral) => void;
}

const containerStyle = {
  width: "100%",
  height: "400px",
};

const defaultCenter = {
  lat: 38.733509, // Kayseri coordinates
  lng: 35.485397,
};

const GOOGLE_MAP_LIBRARIES: any = ["places"];

const CustomGoogleMap: FC<CustomGoogleMapProps> = (props) => {
  const { isLoaded } = useJsApiLoader({
    id: "google-map-script",
    googleMapsApiKey: import.meta.env.VITE_GOOGLE_MAP_API || "",
    libraries: GOOGLE_MAP_LIBRARIES,
  });

  const geocoder = useRef<google.maps.Geocoder | null>(null);

  if (!geocoder.current && isLoaded) {
    geocoder.current = new google.maps.Geocoder();
  }

  const onMapClick = async (event: google.maps.MapMouseEvent) => {
    const latLng = event.latLng;
    if (!latLng) return;

    const coordinates = {
      lat: latLng.lat(),
      lng: latLng.lng(),
    };

    props.setMarker(coordinates);

    // Reverse geocoding ile adres bilgisini al
    if (geocoder.current) {
      geocoder.current.geocode({ location: latLng }, (results, status) => {
        if (status === "OK" && results && results[0]) {
          const address = results[0].formatted_address;

          // Form field'ını güncelle
          if (props.form) {
            props.form.setFieldValue("Address", address);
          }

          // Parent component'e bildir
          if (props.onAddressUpdate) {
            props.onAddressUpdate(address, coordinates);
          }
        } else {
          console.error("Geocoder failed due to:", status);
        }
      });
    }
  };

  const { Text } = Typography;
  const mapCenter = useMemo(() => {
    // Marker varsa onun koordinatlarını, yoksa default center'ı kullan
    if (props.marker && props.marker.lat && props.marker.lng) {
      return props.marker;
    }
    return defaultCenter;
  }, [props.marker]);

  // Marker icon configuration
  const markerIconConfig = {
    url: markerIcon,
    scaledSize: new google.maps.Size(40, 40),
    anchor: new google.maps.Point(20, 40), // Alt ortadan anchor
    origin: new google.maps.Point(0, 0),
  };

  // Fallback icon (eğer custom icon yüklenemezse)
  const fallbackIcon = {
    path: google.maps.SymbolPath.CIRCLE,
    scale: 10,
    fillColor: "#ff0000",
    fillOpacity: 1,
    strokeWeight: 2,
    strokeColor: "#ffffff",
  };

  return isLoaded ? (
    <Row gutter={[10, 20]}>
      <Col span={24}>
        <Row gutter={[10, 10]} className="pr-2">
          {props.marker && (
            <Col xs={24}>
              <div>
                <Text strong>
                  📍 Seçili Konum:
                </Text>
              </div>
              <div>
                <Text type="secondary">
                  🌍 Koordinatlar: {props.marker.lat.toFixed(6)}, {props.marker.lng.toFixed(6)}
                </Text>
              </div>
            </Col>
          )}
        </Row>
      </Col>

      <Col span={24}>
        <GoogleMap
          mapContainerStyle={containerStyle}
          center={mapCenter}
          zoom={15}
          onClick={onMapClick}
          options={{
            streetViewControl: false,
            mapTypeControl: true,
            fullscreenControl: true,
            zoomControl: true,
          }}
        >
          {/* Marker'ı sadece position varsa render et */}
          {props.marker && props.marker.lat && props.marker.lng && (
            <Marker
              position={props.marker}
              icon={markerIconConfig}
              title="Seçili Konum"
              animation={google.maps.Animation.DROP}
              onLoad={() => console.log("Marker loaded successfully")}
            />
          )}
        </GoogleMap>
      </Col>
    </Row>
  ) : (
    <div style={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      height: '400px',
      backgroundColor: '#f5f5f5',
      borderRadius: '8px'
    }}>
      <div style={{ textAlign: 'center' }}>
        <div style={{ fontSize: '24px', marginBottom: '12px' }}>🗺️</div>
        <Text>Harita yükleniyor...</Text>
      </div>
    </div>
  );
};

export default React.memo(CustomGoogleMap);