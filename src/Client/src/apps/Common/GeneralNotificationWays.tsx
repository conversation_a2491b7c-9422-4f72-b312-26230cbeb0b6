

import { FC, } from "react";
import { GeneralSelectInputs } from "@/models/Client/GeneralSelectInputs";
import { normalizeString } from "@/helpers/TRNormalizedName";
import { MazakaSelect } from "./MazakaSelect";
import { useGetNotificationWays } from "../Admin/Pages/NotificationWay/ServerSideStates";


const GeneralNotificationWays: FC<GeneralSelectInputs> = (props) => {
  const notificationWays = useGetNotificationWays({ PageSize: 100, PageNumber: 1 })
  return (
    <>
      <MazakaSelect
        name={props.name}
        xs={props.xs}
        sm={props.sm}
        md={props.md}
        lg={props.lg}
        xl={props.xl}
        labelCol={props.labelCol}
        wrapperCol={props.wrapperCol}
        loading={notificationWays.isLoading || notificationWays.isFetching}
        disabled={notificationWays.isLoading || notificationWays.isFetching || props.disabled}
        className={props.className}
        showSearch={true}
        allowClear={props.allowClear || false}
        filterOption={(input: any, option: any) => {
          const normalizedInput = normalizeString(input.toLowerCase().trim());
          const normalizedLabel = normalizeString(option.label.toLowerCase().trim());
          const status = (normalizedLabel ?? "").includes(normalizedInput.toLowerCase())
          return status
        }}
        label={props.label}
        options={
          notificationWays.data?.Value
            ? notificationWays.data?.Value?.map((item: any) => {
              return {
                key: item.Id,
                value: item.Id,
                label: item.Name,
              };
            })
            : []
        }
        placeholder={props.placeholder}
        mode={props.mode}
        onChange={props.onChange}
        value={props.value}
        rules={props.rules}
      />
    </>
  );
};

export default GeneralNotificationWays;
