import { FC, } from "react";
import { GeneralSelectInputs } from "@/models/Client/GeneralSelectInputs";
import { normalizeString } from "@/helpers/TRNormalizedName";
import { MazakaSelect } from "./MazakaSelect";
import { useGetTicketStatus } from "../Admin/Pages/Ticket/ServerSideStates";

interface GeneralStatusTicketProps extends GeneralSelectInputs {
  ticketId?: any; // Ticket ID parametresi
}

const GeneralStatusTicket: FC<GeneralStatusTicketProps> = (props) => {
  const subjects = useGetTicketStatus({
    TicketId: props.ticketId
  });

  return (
    <>
      <MazakaSelect
        name={props.name}
        xs={props.xs}
        sm={props.sm}
        md={props.md}
        lg={props.lg}
        xl={props.xl}
        labelCol={props.labelCol}
        wrapperCol={props.wrapperCol}
        loading={subjects.isLoading || subjects.isFetching}
        disabled={subjects.isLoading || subjects.isFetching || props.disabled}
        className={props.className}
        showSearch={true}
        allowClear={props.allowClear || false}
        filterOption={(input: any, option: any) => {
          const normalizedInput = normalizeString(input.toLowerCase().trim());
          const normalizedLabel = normalizeString(option.label.toLowerCase().trim());
          const status = (normalizedLabel ?? "").includes(normalizedInput.toLowerCase());
          return status;
        }}
        label={props.label}
        options={
          subjects.data?.Value
            ? subjects.data?.Value.map((item: any) => {
              return {
                key: item.TransitionId,
                value: item.TransitionId,
                label: item.TransitionName,
              };
            })
            : []
        }
        placeholder={props.placeholder}
        mode={props.mode}
        onChange={props.onChange}
        value={props.value}
        rules={props.rules}
      />
    </>
  );
};

export default GeneralStatusTicket;