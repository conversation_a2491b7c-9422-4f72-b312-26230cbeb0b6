

import { FC, useState, } from "react";
import { GeneralSelectInputs } from "@/models/Client/GeneralSelectInputs";
import { normalizeString } from "@/helpers/TRNormalizedName";
import { MazakaSelect } from "./MazakaSelect";
import { useGetCustomerSources } from "../Admin/Pages/Customers/ServerSideStates";


const GeneralCustomerSource: FC<GeneralSelectInputs> = (props) => {
  const [filter, setFilter] = useState({
    PageNumber: 1,
    PageSize: 10000,
  });
  const sources = useGetCustomerSources(filter)
  console.log("KaynakSources", sources);
  return (
    <>
      <MazakaSelect
        name={props.name}
        xs={props.xs}
        sm={props.sm}
        md={props.md}
        lg={props.lg}
        xl={props.xl}
        loading={sources.isLoading || sources.isFetching}
        disabled={sources.isLoading || sources.isFetching || props.disabled}
        className={props.className}
        showSearch={true}
        allowClear={props.allowClear || false}
        filterOption={(input: any, option: any) => {
          const normalizedInput = normalizeString(input.toLowerCase().trim());
          const normalizedLabel = normalizeString(option.label.toLowerCase().trim());
          const status = (normalizedLabel ?? "").includes(normalizedInput.toLowerCase())
          return status
        }}
        label={props.label}
        options={
          sources.data?.Value
            ? sources.data?.Value?.map((item: any) => {
              return {
                key: item.Id,
                value: item.Id,
                label: item.Name,


              };
            })
            : []
        }
        placeholder={props.placeholder}
        mode={props.mode}
        onChange={props.onChange}
        value={props.value}
        rules={props.rules}
      />
    </>
  );
};

export default GeneralCustomerSource;
