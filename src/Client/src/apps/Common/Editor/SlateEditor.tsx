import isHotkey from 'is-hotkey'
import React, { KeyboardEvent, MouseEvent, useCallback, useMemo } from 'react'
import {
  Descendant,
  Editor,
  Element as SlateElement,
  Transforms,
  createEditor,
} from 'slate'
import { withHistory } from 'slate-history'
import {
  Editable,
  RenderElementProps,
  RenderLeafProps,
  Slate,
  useSlate,
  withReact,
} from 'slate-react'
import { Button, Icon, Toolbar } from './components.tsx'

// Type definitions
export type CustomElementType =
  | 'paragraph'
  | 'heading-one'
  | 'heading-two'
  | 'heading-three'
  | 'block-quote'
  | 'numbered-list'
  | 'bulleted-list'
  | 'list-item'
  | 'link'
  | 'image'

export type CustomTextKey = 'bold' | 'italic' | 'underline' | 'code' | 'strikethrough'

export interface CustomText {
  text: string
  bold?: boolean
  italic?: boolean
  underline?: boolean
  code?: boolean
  strikethrough?: boolean
}

export interface CustomElement {
  type: CustomElementType
  align?: 'left' | 'center' | 'right' | 'justify'
  url?: string
  children: (CustomText | CustomElement)[]
}

export interface CustomElementWithAlign extends CustomElement {
  align: 'left' | 'center' | 'right' | 'justify'
}

// Extend Slate's types
declare module 'slate' {
  interface CustomTypes {
    Element: CustomElement
    Text: CustomText
  }
}

const HOTKEYS: Record<string, CustomTextKey> = {
  'mod+b': 'bold',
  'mod+i': 'italic',
  'mod+u': 'underline',
  'mod+`': 'code',
  'mod+shift+x': 'strikethrough',
}

const LIST_TYPES = ['numbered-list', 'bulleted-list'] as const
const TEXT_ALIGN_TYPES = ['left', 'center', 'right', 'justify'] as const

type AlignType = (typeof TEXT_ALIGN_TYPES)[number]
type ListType = (typeof LIST_TYPES)[number]
type CustomElementFormat = CustomElementType | AlignType | ListType

const RichTextExample = () => {
  const renderElement = useCallback(
    (props: RenderElementProps) => <Element {...props} />,
    []
  )
  const renderLeaf = useCallback(
    (props: RenderLeafProps) => <Leaf {...props} />,
    []
  )
  const editor = useMemo(() => withHistory(withReact(createEditor())), [])

  return (
    <div style={{
      maxWidth: '800px',
      margin: '20px auto',
      boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
      borderRadius: '8px',
      overflow: 'hidden'
    }}>
      <Slate editor={editor} initialValue={initialValue}>
      <Toolbar>
        <MarkButton format="bold" icon="format_bold" />
        <MarkButton format="italic" icon="format_italic" />
        <MarkButton format="underline" icon="format_underlined" />
        <MarkButton format="strikethrough" icon="strikethrough_s" />
        <MarkButton format="code" icon="code" />
        <BlockButton format="heading-one" icon="looks_one" />
        <BlockButton format="heading-two" icon="looks_two" />
        <BlockButton format="heading-three" icon="looks_3" />
        <BlockButton format="block-quote" icon="format_quote" />
        <BlockButton format="numbered-list" icon="format_list_numbered" />
        <BlockButton format="bulleted-list" icon="format_list_bulleted" />
        <BlockButton format="left" icon="format_align_left" />
        <BlockButton format="center" icon="format_align_center" />
        <BlockButton format="right" icon="format_align_right" />
        <BlockButton format="justify" icon="format_align_justify" />
        <ClearFormattingButton />
        <SaveButton />
      </Toolbar>
      <Editable
        renderElement={renderElement}
        renderLeaf={renderLeaf}
        placeholder="Zengin metin yazmaya başlayın..."
        spellCheck
        autoFocus
        style={{
          minHeight: '300px',
          padding: '20px',
          border: '1px solid #e8e8e8',
          borderRadius: '0 0 8px 8px',
          fontSize: '16px',
          lineHeight: '1.6',
          fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
          outline: 'none',
          backgroundColor: 'white'
        }}
        onKeyDown={(event: KeyboardEvent<HTMLDivElement>) => {
          // Handle hotkeys for text formatting
          for (const hotkey in HOTKEYS) {
            if (isHotkey(hotkey, event as any)) {
              event.preventDefault()
              const mark = HOTKEYS[hotkey]
              toggleMark(editor, mark)
            }
          }

          // Handle undo/redo
          if (isHotkey('mod+z', event as any)) {
            event.preventDefault()
            editor.undo()
          }

          if (isHotkey('mod+shift+z', event as any)) {
            event.preventDefault()
            editor.redo()
          }

          // Handle Tab key for list indentation
          if (event.key === 'Tab') {
            const { selection } = editor
            if (selection) {
              const [match] = Editor.nodes(editor, {
                match: n => SlateElement.isElement(n) && n.type === 'list-item',
              })
              if (match) {
                event.preventDefault()
                // Simple indentation - in a real app, you'd implement proper nested lists
                Transforms.setNodes(editor, { type: 'list-item' })
              }
            }
          }

          // Handle Enter key for better list behavior
          if (event.key === 'Enter') {
            const { selection } = editor
            if (selection) {
              const [match] = Editor.nodes(editor, {
                match: n => SlateElement.isElement(n) && n.type === 'list-item',
              })
              if (match) {
                event.preventDefault()
                Transforms.splitNodes(editor)
              }
            }
          }

          // Handle Backspace for list items
          if (event.key === 'Backspace') {
            const { selection } = editor
            if (selection && selection.anchor.offset === selection.focus.offset) {
              const [match] = Editor.nodes(editor, {
                match: n => SlateElement.isElement(n) && n.type === 'list-item',
              })
              if (match) {
                const [, path] = match
                if (Editor.isStart(editor, selection.anchor, path)) {
                  event.preventDefault()
                  Transforms.setNodes(editor, { type: 'paragraph' })
                  Transforms.unwrapNodes(editor, {
                    match: n => SlateElement.isElement(n) && isListType(n.type),
                    split: true,
                  })
                }
              }
            }
          }
        }}
      />
      </Slate>
    </div>
  )
}

const toggleBlock = (editor: Editor, format: CustomElementFormat) => {
  const isActive = isBlockActive(
    editor,
    format,
    isAlignType(format) ? 'align' : 'type'
  )
  const isList = isListType(format)

  Transforms.unwrapNodes(editor, {
    match: n =>
      !Editor.isEditor(n) &&
      SlateElement.isElement(n) &&
      isListType(n.type) &&
      !isAlignType(format),
    split: true,
  })
  let newProperties: Partial<SlateElement>
  if (isAlignType(format)) {
    newProperties = {
      align: isActive ? undefined : format,
    }
  } else {
    newProperties = {
      type: isActive ? 'paragraph' : isList ? 'list-item' : format,
    }
  }
  Transforms.setNodes<SlateElement>(editor, newProperties)

  if (!isActive && isList) {
    const block = { type: format, children: [] }
    Transforms.wrapNodes(editor, block)
  }
}

const toggleMark = (editor: Editor, format: CustomTextKey) => {
  const isActive = isMarkActive(editor, format)

  if (isActive) {
    Editor.removeMark(editor, format)
  } else {
    Editor.addMark(editor, format, true)
  }
}

const isBlockActive = (
  editor: Editor,
  format: CustomElementFormat,
  blockType: 'type' | 'align' = 'type'
) => {
  const { selection } = editor
  if (!selection) return false

  const [match] = Array.from(
    Editor.nodes(editor, {
      at: Editor.unhangRange(editor, selection),
      match: n => {
        if (!Editor.isEditor(n) && SlateElement.isElement(n)) {
          if (blockType === 'align' && isAlignElement(n)) {
            return n.align === format
          }
          return n.type === format
        }
        return false
      },
    })
  )

  return !!match
}

const isMarkActive = (editor: Editor, format: CustomTextKey) => {
  const marks = Editor.marks(editor)
  return marks ? marks[format] === true : false
}

const Element = ({ attributes, children, element }: RenderElementProps) => {
  const style: React.CSSProperties = {}
  if (isAlignElement(element)) {
    style.textAlign = element.align as AlignType
  }

  switch (element.type) {
    case 'block-quote':
      return (
        <blockquote
          style={{
            ...style,
            borderLeft: '4px solid #ddd',
            paddingLeft: '16px',
            margin: '16px 0',
            fontStyle: 'italic',
            color: '#666',
            backgroundColor: '#f9f9f9',
            padding: '12px 16px',
            borderRadius: '4px'
          }}
          {...attributes}
        >
          {children}
        </blockquote>
      )
    case 'bulleted-list':
      return (
        <ul style={{ ...style, paddingLeft: '24px', margin: '8px 0' }} {...attributes}>
          {children}
        </ul>
      )
    case 'heading-one':
      return (
        <h1 style={{
          ...style,
          fontSize: '2em',
          fontWeight: 'bold',
          margin: '24px 0 16px 0',
          color: '#333'
        }} {...attributes}>
          {children}
        </h1>
      )
    case 'heading-two':
      return (
        <h2 style={{
          ...style,
          fontSize: '1.5em',
          fontWeight: 'bold',
          margin: '20px 0 12px 0',
          color: '#444'
        }} {...attributes}>
          {children}
        </h2>
      )
    case 'heading-three':
      return (
        <h3 style={{
          ...style,
          fontSize: '1.25em',
          fontWeight: 'bold',
          margin: '16px 0 8px 0',
          color: '#555'
        }} {...attributes}>
          {children}
        </h3>
      )
    case 'list-item':
      return (
        <li style={{ ...style, margin: '4px 0' }} {...attributes}>
          {children}
        </li>
      )
    case 'numbered-list':
      return (
        <ol style={{ ...style, paddingLeft: '24px', margin: '8px 0' }} {...attributes}>
          {children}
        </ol>
      )
    default:
      return (
        <p style={{ ...style, margin: '8px 0', lineHeight: '1.6' }} {...attributes}>
          {children}
        </p>
      )
  }
}

const Leaf = ({ attributes, children, leaf }: RenderLeafProps) => {
  if (leaf.bold) {
    children = <strong style={{ fontWeight: 'bold' }}>{children}</strong>
  }

  if (leaf.code) {
    children = (
      <code style={{
        backgroundColor: '#f4f4f4',
        padding: '2px 4px',
        borderRadius: '3px',
        fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
        fontSize: '0.9em',
        color: '#d63384'
      }}>
        {children}
      </code>
    )
  }

  if (leaf.italic) {
    children = <em style={{ fontStyle: 'italic' }}>{children}</em>
  }

  if (leaf.underline) {
    children = <u style={{ textDecoration: 'underline' }}>{children}</u>
  }

  if (leaf.strikethrough) {
    children = <del style={{ textDecoration: 'line-through', color: '#888' }}>{children}</del>
  }

  return <span {...attributes}>{children}</span>
}

interface BlockButtonProps {
  format: CustomElementFormat
  icon: string
}

const BlockButton = ({ format, icon }: BlockButtonProps) => {
  const editor = useSlate()
  return (
    <Button
      active={isBlockActive(
        editor,
        format,
        isAlignType(format) ? 'align' : 'type'
      )}
      onMouseDown={(event: MouseEvent<HTMLSpanElement>) => {
        event.preventDefault()
        toggleBlock(editor, format)
      }}
    >
      <Icon>{icon}</Icon>
    </Button>
  )
}

interface MarkButtonProps {
  format: CustomTextKey
  icon: string
}

const MarkButton = ({ format, icon }: MarkButtonProps) => {
  const editor = useSlate()
  return (
    <Button
      active={isMarkActive(editor, format)}
      onMouseDown={(event: MouseEvent<HTMLSpanElement>) => {
        event.preventDefault()
        toggleMark(editor, format)
      }}
    >
      <Icon>{icon}</Icon>
    </Button>
  )
}

const isAlignType = (format: CustomElementFormat): format is AlignType => {
  return TEXT_ALIGN_TYPES.includes(format as AlignType)
}

const isListType = (format: CustomElementFormat): format is ListType => {
  return LIST_TYPES.includes(format as ListType)
}

const isAlignElement = (
  element: CustomElement
): element is CustomElementWithAlign => {
  return 'align' in element
}

// Clear formatting function
const clearFormatting = (editor: Editor) => {
  // Remove all marks
  const marks: CustomTextKey[] = ['bold', 'italic', 'underline', 'code', 'strikethrough']
  marks.forEach(mark => {
    if (isMarkActive(editor, mark)) {
      Editor.removeMark(editor, mark)
    }
  })

  // Reset block type to paragraph
  Transforms.setNodes(editor, { type: 'paragraph', align: undefined })
}

// Clear formatting button component
const ClearFormattingButton = () => {
  const editor = useSlate()
  return (
    <Button
      onMouseDown={(event: MouseEvent<HTMLSpanElement>) => {
        event.preventDefault()
        clearFormatting(editor)
      }}
    >
      <Icon>format_clear</Icon>
    </Button>
  )
}

// Save button component
const SaveButton = () => {
  const editor = useSlate()

  const handleSave = () => {
    const content = JSON.stringify(editor.children)
    console.log('Saving content:', content)
    // Here you would typically send the content to your backend
    // For now, we'll just show an alert
    alert('İçerik kaydedildi! (Konsola bakınız)')
  }

  return (
    <Button
      onMouseDown={(event: MouseEvent<HTMLSpanElement>) => {
        event.preventDefault()
        handleSave()
      }}
    >
      <Icon>save</Icon>
    </Button>
  )
}

const initialValue: Descendant[] = [
  {
    type: 'heading-one',
    children: [{ text: 'Slate Editor Özellikleri' }],
  },
  {
    type: 'paragraph',
    children: [
      { text: 'Bu zengin metin editörü ile ' },
      { text: 'kalın', bold: true },
      { text: ', ' },
      { text: 'italik', italic: true },
      { text: ', ' },
      { text: 'altı çizili', underline: true },
      { text: ', ' },
      { text: 'üstü çizili', strikethrough: true },
      { text: ' ve ' },
      { text: 'kod', code: true },
      { text: ' formatlarını kullanabilirsiniz.' },
    ],
  },
  {
    type: 'heading-two',
    children: [{ text: 'Liste Özellikleri' }],
  },
  {
    type: 'bulleted-list',
    children: [
      {
        type: 'list-item',
        children: [{ text: 'Madde işaretli listeler' }],
      },
      {
        type: 'list-item',
        children: [{ text: 'Çoklu seviye destekli' }],
      },
    ],
  },
  {
    type: 'numbered-list',
    children: [
      {
        type: 'list-item',
        children: [{ text: 'Numaralı listeler' }],
      },
      {
        type: 'list-item',
        children: [{ text: 'Otomatik numaralandırma' }],
      },
    ],
  },
  {
    type: 'heading-three',
    children: [{ text: 'Alıntı Örneği' }],
  },
  {
    type: 'block-quote',
    children: [{ text: 'Bu bir blok alıntı örneğidir. Önemli metinleri vurgulamak için kullanılır.' }],
  },
  {
    type: 'paragraph',
    align: 'center',
    children: [{ text: 'Metin hizalama özellikleri de mevcuttur!' }],
  },
  {
    type: 'paragraph',
    align: 'right',
    children: [{ text: 'Sağa hizalanmış metin örneği' }],
  },
]

export default RichTextExample