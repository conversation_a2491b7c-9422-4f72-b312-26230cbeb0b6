import React, { <PERSON>actN<PERSON>, MouseEvent } from 'react'
import styled from 'styled-components'

// Styled components for the editor
export const StyledToolbar = styled.div`
  position: relative;
  padding: 12px 18px;
  margin: 0 -20px 20px;
  border-bottom: 2px solid #e8e8e8;
  background-color: #f8f9fa;
  border-radius: 8px 8px 0 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
`

export const StyledButton = styled.span<{ active?: boolean; reversed?: boolean }>`
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 12px;
  border-radius: 4px;
  transition: all 0.2s ease;
  background-color: ${props => props.active ? '#e3f2fd' : 'transparent'};
  color: ${props =>
    props.reversed
      ? props.active
        ? 'white'
        : '#aaa'
      : props.active
      ? '#1976d2'
      : '#666'};
  border: 1px solid ${props => props.active ? '#1976d2' : 'transparent'};

  &:hover {
    background-color: ${props => props.active ? '#e3f2fd' : '#f0f0f0'};
    color: ${props => props.active ? '#1976d2' : '#333'};
  }

  &:active {
    transform: translateY(1px);
  }
`

export const StyledIcon = styled.span`
  font-size: 18px;
  line-height: 1;
  font-family: 'Material Icons', sans-serif;
`

export const StyledMenu = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;

  & > * {
    flex-shrink: 0;
  }
`

// Component interfaces
interface ToolbarProps {
  children: ReactNode
}

interface ButtonProps {
  active?: boolean
  reversed?: boolean
  children: ReactNode
  onMouseDown?: (event: MouseEvent<HTMLSpanElement>) => void
}

interface IconProps {
  children: ReactNode
}

// Components
export const Toolbar: React.FC<ToolbarProps> = ({ children }) => (
  <StyledToolbar>
    <StyledMenu>{children}</StyledMenu>
  </StyledToolbar>
)

export const Button: React.FC<ButtonProps> = ({ 
  active, 
  reversed, 
  children, 
  onMouseDown 
}) => (
  <StyledButton
    active={active}
    reversed={reversed}
    onMouseDown={onMouseDown}
  >
    {children}
  </StyledButton>
)

export const Icon: React.FC<IconProps> = ({ children }) => (
  <StyledIcon className="material-icons">{children}</StyledIcon>
)

// Menu component for grouping buttons
export const Menu = StyledMenu
