import { useGetNotifications } from "@/apps/Notification/ServerSideStates";
import { commonRoutePrefix } from "@/routes/Prefix";
import { Badge } from "antd";
import { useEffect, useState } from "react";
import { useQueryClient } from "react-query";
import { useNavigate } from "react-router-dom";
import endPoints from "@/apps/Notification/EndPoints"
import {
  HubConnectionBuilder,
  LogLevel,
  HttpTransportType,
} from "@microsoft/signalr";
import { setBackEndUrl } from "@/helpers/SetBackEndUrl";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import { useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";
import sound from '@/assets/sounds/notification.mp3';

const Notifications = () => {
  const [connection, setConnection] = useState(null);
  const { userInfoes } = useSelector((state: RootState) => state.profile);
  const navigate = useNavigate();
  const [count, setCount] = useState(0);
  const queryClient = useQueryClient()

  // Ses çalmak için Audio instance'ı
  const [notificationSound] = useState(() => {
    const audio = new Audio(sound); // Import edilen ses dosyasını kullan
    audio.preload = 'auto';
    audio.volume = 0.5; // Ses seviyesi (0.0 - 1.0 arası)
    return audio;
  });

  const notifications = useGetNotifications({
    PageSize: 100,
    IsRead: false,
    PageNumber: 1,
    UserId: userInfoes?.Id
  });

  // Ses çalma fonksiyonu
  const playNotificationSound = async () => {
    try {
      // Tarayıcı ses çalma politikası nedeniyle kullanıcı etkileşimi gerekebilir
      await notificationSound.play();
    } catch (error) {
      console.warn('Bildirim sesi çalınamadı:', error);
      // Alternatif olarak basit bir beep sesi
      playBeepSound();
    }
  };

  // Alternatif beep sesi (ses dosyası yoksa)
  const playBeepSound = () => {
    const audioContext = new (window.AudioContext || window.AudioContext)();
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    oscillator.frequency.value = 800; // Frekans
    oscillator.type = 'sine';

    gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);

    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 0.5);
  };

  useEffect(() => {
    // SignalR bağlantısını kuruyoruz
    const newConnection = new HubConnectionBuilder()
      .withUrl(`${setBackEndUrl()}/hubs/notificationhub`, {
        accessTokenFactory: () => {
          return localStorage.getItem("access_token") || "";
        },
        skipNegotiation: true,
        transport: HttpTransportType.WebSockets,
        withCredentials: true,
      })
      .configureLogging(LogLevel.Information)
      .withAutomaticReconnect()
      .build();

    newConnection
      .start()
      .then(() => {
        console.log("SignalR bağlantısı kuruldu");
        setConnection(newConnection);
      })
      .catch((err) => console.error("SignalR bağlantı hatası:", err));

    return () => {
      if (connection) {
        connection.stop();
      }
    };
  }, []);

  useEffect(() => {
    if (connection) {
      connection.on("ReceiveMessage", async (data: any) => {
        // Görsel bildirim
        openNotificationWithIcon("success", data?.title || "", data?.message || "");

        // Ses bildirimi
        await playNotificationSound();
        xs
        // Query'leri resetle
        queryClient.resetQueries({
          queryKey: endPoints.getNotificationListFilter,
          exact: false,
        });
      });
    }

    return () => {
      if (connection) {
        connection.off("ReceiveMessage");
      }
    };
  }, [connection, notificationSound]);

  useEffect(() => {
    setCount(notifications.data?.Value?.length || 0);
  }, [notifications.data]);

  return (
    <>
      <span className={`!flex items-center ${count ? "!mx-2" : ""} cursor-pointer`}
        onClick={() => {
          queryClient.resetQueries({
            queryKey: endPoints.getNotificationListFilter,
            exact: false,
          });
          navigate(`${commonRoutePrefix}/notifications`);
        }}
      >
        <Badge count={count} size="small" >
          <i
            _ngcontent-ng-c2222475972=""
            app-bell-solid-icon=""
            className="!cursor-pointer"
          >
            <svg
              width={17}
              height={17}
              fill="white"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 448 512"
            >
              <path d="M224 0c-17.7 0-32 14.3-32 32V51.2C119 66 64 130.6 64 208v18.8c0 47-17.3 92.4-48.5 127.6l-7.4 8.3c-8.4 9.4-10.4 22.9-5.3 34.4S19.4 416 32 416H416c12.6 0 24-7.4 29.2-18.9s3.1-25-5.3-34.4l-7.4-8.3C401.3 319.2 384 273.9 384 226.8V208c0-77.4-55-142-128-156.8V32c0-17.7-14.3-32-32-32zm45.3 493.3c12-12 18.7-28.3 18.7-45.3H224 160c0 17 6.7 33.3 18.7 45.3s28.3 18.7 45.3 18.7s33.3-6.7 45.3-18.7z"></path>
            </svg>
          </i>
        </Badge>
      </span>
    </>
  );
};

export default Notifications;