import { Col, Divider, Row, Tag } from "antd";
import { useDispatch } from "react-redux";
import { FC } from "react";
import dayjs from 'dayjs';


interface GeneralTagsProps {
  excludedKeys: string[];
  filter: any;
  actionFunc: any;
  showFilterTagLength: number;
  actionFunkKey: string;
  isDontCloseableDate?: boolean;
}

const GeneralFilterTags: FC<GeneralTagsProps> = (props) => {
  const dispatch = useDispatch();

  const handleTagClose = (e: any, keyToRemove: string) => {
    e.preventDefault();
    const newFilter = { ...props.filter };
    const key = keyToRemove?.replace("customName", "");

    delete newFilter[key];
    delete newFilter[keyToRemove];

    dispatch(props.actionFunc({ [props.actionFunkKey]: newFilter }));
  };

  const formatIfDate = (value: string | undefined): string => {
    if (!value) return '';
    return dayjs(value).isValid() ? value.replace("T", " ") : value;
  };

  const handleTagCloseArray = (e: any, key: string, indexToRemove: number) => {
    e.preventDefault();
    const newFilter = { ...props.filter };

    const isCustomKey = key.startsWith("customName");
    const mainKey = isCustomKey ? key.replace("customName", "") : key;
    const customKey = isCustomKey ? key : `customName${key}`;

    // Ana dizi (mainKey)
    if (Array.isArray(newFilter[mainKey])) {
      const mainArray = [...newFilter[mainKey]];
      mainArray.splice(indexToRemove, 1);
      if (mainArray.length === 0) {
        delete newFilter[mainKey];
      } else {
        newFilter[mainKey] = mainArray;
      }
    }

    // customName dizi (customKey)
    if (Array.isArray(newFilter[customKey])) {
      const customArray = [...newFilter[customKey]];
      customArray.splice(indexToRemove, 1);
      if (customArray.length === 0) {
        delete newFilter[customKey];
      } else {
        newFilter[customKey] = customArray;
      }
    }

    dispatch(props.actionFunc({ [props.actionFunkKey]: newFilter }));
  };



  return (
    <>
      {props.filter &&
        Object.entries(props.filter).length > props.showFilterTagLength && (
          <Col xs={24}>
            <Row>
              <Col xs={24} className="!py-2 !px-2">
                {Object.entries(props.filter).map(([key, value]) => {
                  const isExcluded = props.excludedKeys?.includes(key);
                  const hasId = key.includes("Id");
                  const hasCustomName = key.includes("customName");

                  if (isExcluded) return null;
                  if (hasId && !hasCustomName) return null;

                  if (Array.isArray(value)) {
                    return value.map((item, index) => {
                      if (item && typeof item === "object" && "label" in item) {
                        return (
                          <Tag
                            key={`${key}-${index}`}
                            closable
                            onClose={(e) => handleTagCloseArray(e, key, index)}
                          >
                            <span>{item.label}</span>
                          </Tag>
                        );
                      }
                      return null;
                    });
                  }

                  return (
                    <Tag
                      key={key}
                      closable={props.isDontCloseableDate && key?.includes("Date") ? false : true}
                      onClose={(e) => handleTagClose(e, key)}
                    >

                      <span>  {dayjs(value).isValid() ? value?.replace("T", " ") : value}</span>
                    </Tag>
                  );
                })}
              </Col>
              <Col xs={24}>
                <Divider className="!m-0 !text-gray-400" />
              </Col>
            </Row>
          </Col>
        )}
    </>
  );
};

export default GeneralFilterTags;
