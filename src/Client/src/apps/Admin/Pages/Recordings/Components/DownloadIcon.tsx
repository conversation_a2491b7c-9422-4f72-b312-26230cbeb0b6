import { FC, useState } from "react";
import { DownloadOutlined, LoadingOutlined } from "@ant-design/icons";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import { useTranslation } from "react-i18next";
import { Tooltip } from "antd";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import { get3cxCallRecordData, get3cxCallReportDetails } from "../Services";

const DownloadIcon: FC<{ recordId: string }> = ({ recordId }) => {
  const { t } = useTranslation();
  const [isLoading, setIsLoading] = useState(false);

  const handleDownload = async () => {
    setIsLoading(true);
    try {
      const response = await get3cxCallReportDetails(recordId);
      const link = response?.Value?.RecordingUrl;

      if (link) {
        const responseData = await get3cxCallRecordData(link);
        console.log(responseData);
      } else {
        openNotificationWithIcon("error", t("recording.invalidLink"));
      }
    } catch (error) {
      showErrorCatching(error, null, false, t)
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Tooltip title={t("recording.downloadss")}>
      {isLoading ? (
        <LoadingOutlined className="!text-gray-500" />
      ) : (
        <DownloadOutlined className="!text-gray-500" onClick={handleDownload}></DownloadOutlined>
      )}
    </Tooltip>
  );
};

export default DownloadIcon;
