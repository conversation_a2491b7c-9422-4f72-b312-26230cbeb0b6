// ListItems.tsx
import React, { useState } from "react";
import { Col, Drawer, Modal, Table, Tag, Tooltip, Typography } from "antd";
import {
  ArrowDownOutlined,
  ArrowUpOutlined,
  EyeOutlined,
  FormOutlined,
} from "@ant-design/icons";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";
import { hanldleSetRecordingsFilter } from "../ClientSideStates";
import { useTranslation } from "react-i18next";
import dayjs from "dayjs";
import { useGet3cxRecordings } from "../ServerSideStates";
import DownloadIcon from "./DownloadIcon";
import RecordDescription from "./CallDetails";
import endPoints from "../EndPoints";
import { useQueryClient } from "react-query";
import CallDetails from "./CallDetails";
import { excludeUnnesessaryKey } from "@/helpers/ExcludeUnNesessaryKey";

const ListItems: React.FC = () => {
  const { filter } = useSelector((state: RootState) => state.recordings);
  const [selectedRecord, setSelectedRecord] = useState<any | null>(null);
  const queryClient = useQueryClient();
  const dispatch = useDispatch();
  const { t } = useTranslation();
  const recordings = useGet3cxRecordings(excludeUnnesessaryKey(filter));
  console.log(recordings);

  const { Text } = Typography;
  const columns = [
    {
      title: t("recording.caller"),
      dataIndex: "Caller",
      key: "Caller",

      render: (value: string, record: any) => {
        return (
          <div className=" !flex gap-1 items-center">
            <Tooltip
              title={
                record.IsAnswered
                  ? t("recording.answerd")
                  : t("recording.missed")
              }
            >
              <div
                className={`
                        w-[12px] h-[12px] ${record?.IsAnswered ? "!bg-green-500" : "!bg-red-500"
                  } transition-all duration-300 
                      group-hover:w-[90px] group-hover:h-[20px] flex items-center justify-center overflow-hidden
                        `}
              ></div>
            </Tooltip>
            <Text className="!text-xs" >{record?.CallerName}</Text>
            <Text className="!text-xs">{value}</Text>
            {record?.Direction?.toLowerCase()?.includes("inbound") ? (
              <Tooltip title={t("customers.add.inbound")}>
                <ArrowDownOutlined className="!text-xs !text-green-500 !rotate-[45deg]" />
              </Tooltip>
            ) : record?.Direction === 1 ? (
              <>
                <Tooltip title={t("customers.add.outbound")}>
                  <ArrowUpOutlined className="!text-xs !text-[#0096d1] !rotate-[45deg]" />
                </Tooltip>
              </>
            ) : (
              <></>
            )}
          </div>
        );
      },
    },

    {
      title: t("recording.callee"),
      dataIndex: "Callee",
      key: "Callee",
      render: (value: string, record: any) => {
        return (
          <div className="!flex gap-1 items-center">
            <Text className="!text-xs" >{record?.CalleeName}</Text>
            <Text className="!text-xs">{value}</Text>
          </div>
        );
      },
    },

    {
      title: t("recording.startTime"),
      dataIndex: "StartTime",
      key: "StartTime",
      render: (value: string) => {
        return (
          <>
            {value && (
              <Text className="!text-xs">
                {dayjs(value).format("YYYY-DD-MM HH:mm:ss")}
              </Text>
            )}
          </>
        );
      },
    },
    {
      title: t("recording.endTime"),
      dataIndex: "EndTime",
      key: "EndTime",
      render: (value: string) => {
        return (
          <>
            {value && (
              <Text className="!text-xs">
                {dayjs(value).format("YYYY-DD-MM HH:mm:ss")}
              </Text>
            )}
          </>
        );
      },
    },
    {
      title: t("recording.talkDurationInSeconds"),
      dataIndex: "TalkDurationInSeconds",
      key: "TalkDurationInSeconds",
      render: (value: string) => {
        return (
          <>
            <Text className="!text-xs">{value}</Text>
          </>
        );
      },
    },
    {
      title: t("recording.totalDurationInSeconds"),
      dataIndex: "TotalDurationInSeconds",
      key: "TotalDurationInSeconds",
      render: (value: string) => {
        return (
          <>
            <Text className="!text-xs">{value}</Text>
          </>
        );
      },
    },
    // {
    //   title: t("recording.summary"),

    //   render: (value: string, record: any) => {
    //     return (
    //       <div
    //         className="!flex gap-1"
    //         onClick={async () => {
    //           const data = { ...record };
    //           data["fieldName"] = "Summary";
    //           await setSelectedRecord({ ...data });
    //         }}
    //       >
    //         <Text className="!text-xs !text-blue-500">
    //           {" "}
    //           {t("recording.viewDetails")}
    //         </Text>
    //         <FormOutlined className="!text-blue-500 !text-xs" />
    //       </div>
    //     );
    //   },
    // },
    // {
    //   title: t("recording.fullContent"),

    //   render: (value: string, record: any) => {
    //     return (
    //       <div
    //         className="!flex gap-1"
    //         onClick={async () => {
    //           const data = { ...record };
    //           data["fieldName"] = "Transcription";
    //           await setSelectedRecord({ ...data });
    //         }}
    //       >
    //         <Text className="!text-xs !text-blue-500">
    //           {" "}
    //           {t("recording.viewDetails")}
    //         </Text>
    //         <FormOutlined className="!text-blue-500 !text-xs" />
    //       </div>
    //     );
    //   },
    // },
    {
      title: "",
      dataIndex: "edit",
      key: "edit",
      width: "8%",
      render: (key: any, record: any) => (
        <Col className="!flex justify-end mr-4">
          <Tooltip title={t("recording.viewDetails")}>
            <EyeOutlined
              className="!text-blue-500"
              onClick={async () => {
                await queryClient.resetQueries({
                  queryKey: endPoints.get3cxCallReportDetails,
                  exact: false,
                });
                await setSelectedRecord(record);
              }}
            />
          </Tooltip>
        </Col>
      ),
    },
  ];

  const handleChangePagination = (pageNum: number, pageSize: number) => {
    let newFilter = { ...filter, PageNumber: pageNum, PageSize: pageSize };
    dispatch(hanldleSetRecordingsFilter({ filter: newFilter }));
  };
  return (
    <>
      <Table
        columns={columns}
        dataSource={recordings?.data?.Value?.CallReportDtoList || []}
        loading={recordings.isLoading || recordings.isFetching}
        pagination={{
          position: ["bottomRight"],
          className: "!px-0",
          onChange: handleChangePagination,
          total: recordings.data?.Value?.TotalCount || 0,
          current: filter.PageNumber,
          pageSize: filter.PageSize || 30,
          showLessItems: true,
          size: "small",
          pageSizeOptions: ['10', '30', '50', '100'],
          showSizeChanger: true,
          locale: { items_per_page: "" },
          showTotal: (e) => `${e}`,
        }}
      />

      {selectedRecord && (
        <Drawer
          title={t("recording.callDetails")}
          open={selectedRecord}
          footer={false}
          onClose={() => {
            setSelectedRecord(false);
          }}
          width={"80%"}
        >
          <CallDetails
            recordId={selectedRecord?.HistoryOfTheCall}
          // fieldName={selectedRecord?.fieldName}
          />
        </Drawer>
      )}
    </>
  );
};

export default ListItems;
