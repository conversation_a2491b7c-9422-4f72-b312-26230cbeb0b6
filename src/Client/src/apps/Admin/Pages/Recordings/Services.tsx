import { DataResponse } from "@/services/BaseResponseModel";
import endpoints from "./EndPoints";
import headers from "@/services/Headers.json";
import { get } from "@/services/Client";
import { CreateUrlFilter } from "@/helpers/CreateURLFilter";

export const get3cxCallReportListFilter = async (
  inputFilter: any
): Promise<DataResponse<any>> => {
  const query = inputFilter ? CreateUrlFilter(inputFilter) : null;

  const url = `${endpoints.get3cxCallReportListFilter}?${query || ""}`;

  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};

export const get3cxCallReportDetails = async (
  historyCallId: string
): Promise<DataResponse<any>> => {
  const url = `${endpoints.get3cxCallReportDetails}/${historyCallId}`;
  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};


export const get3cxCallRecordData = async (
  recordPath: any
): Promise<DataResponse<any>> => {

  const url = `${endpoints.get3cxCallRecordingData}?CallRecordingPath=${encodeURIComponent(recordPath)}`;
  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};


