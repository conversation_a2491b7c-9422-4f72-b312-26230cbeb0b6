import {
  DeleteOutlined,
  EditOutlined,
  EllipsisOutlined,
  FormOutlined,
} from "@ant-design/icons";
import { Col, Drawer, Dropdown, Modal, Table, Tag, Tooltip, Typography } from "antd";

import endPoints from "../EndPoints";
import { useQueryClient } from "react-query";
import { ItemType } from "antd/es/menu/interface";

import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import {  useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";
import { useGetSubjectTickets } from "../ServerSideStates";
import { deleteSubjectTicket } from "../Services";
import { hanldleSetSubjectTicketFilter } from "../ClientSideStates";
import AddOrUpdateSubjectTicket from "./AddOrUpdateSubjectTicket";
import { useTranslation } from "react-i18next";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";




const ListItems = () => {
  const {Text} = Typography
  const [isShowEditDrawer, setIsShowEditDrawer] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<any | null>(null);
  const {filter} = useSelector((state:RootState)=>state.subjectTicket)
  const subjects = useGetSubjectTickets(filter);
  const queryClient = useQueryClient();
  const dispatch = useDispatch()
const {t} = useTranslation()
  const columns = [
    {
      title: t("subjectTicket.name"),
      dataIndex: "Name",
      key: "Name",
      sorter: (a: any, b: any) => a.Name.localeCompare(b.Name),
      width:"30%",
      render:(value:string)=>{
        return(
          <>
          <Text className="!text-xs" >{value}</Text>
          </>
        )
      },
    },

    {
      title: t("workFlow.workFlow"),
      dataIndex: "FlowName",
      key: "FlowName",
      sorter: (a: any, b: any) => a.Name.localeCompare(b.Name),
      width:"30%",
      render:(value:string)=>{
        return(
          <>
          <Tag color="blue" className="!text-xs" >{value}</Tag>
          </>
        )
      },
    },
   
    {
      title: "",
      dataIndex: "edit",
      key: "edit",
      width:"40%",
      render: (key: any, record: any) => (
        <Col className="!flex gap-2 justify-end !px-2">
        <Tooltip title={t("users.list.edit")}>
          <FormOutlined
            className=" !text-[#0096d1] !text-sm"
            onClick={async(e) => {
              e.preventDefault();
              e.stopPropagation();
              await setSelectedRecord(record);
              setIsShowEditDrawer(true);
             
            }}
          />
        </Tooltip>

        <Tooltip title={t("users.list.delete")}>
          <DeleteOutlined
            className=" !text-[#9da3af] !text-sm"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              confirm(record);
            }}
          />
        </Tooltip>
      </Col>
      ),
    },
  ];



  const confirm = (record: any) => {
    Modal.confirm({
      title: t("subjectTicket.warning"),
      icon: null,
      content: t("subjectTicket.deleteModalDesc"),
      okText:  t("subjectTicket.delete"),
      cancelText:  t("subjectTicket.cancel"),
      onOk: async () => {
        try {
          await deleteSubjectTicket(record);
          openNotificationWithIcon("success", t("form.transactionSuccessful"));
          queryClient.resetQueries({
            queryKey: endPoints.getSubjectTicketListFilter,
            exact: false,
          });
        } catch (error: any) {
         showErrorCatching(error,null,false,t)
        }
      },
    });
  };
const handleChangePagination = (pageNum: number, pageSize: number) => {
    let newFilter = { ...filter, PageNumber: pageNum, PageSize: pageSize };
    dispatch(hanldleSetSubjectTicketFilter({ filter: newFilter }));
  };
  return (
    <>
      <Table
        columns={columns}
        dataSource={subjects?.data?.Value}
        loading={subjects.isLoading||subjects.isFetching}
        onRow={(record) => {
          return {
            onClick: async(event) => {
              await setSelectedRecord(record);
              setIsShowEditDrawer(true);
            },
          };
        }}
        pagination={{
            position: ["bottomRight"],
            className: "!px-0",
            onChange: handleChangePagination,
            total: subjects.data?.FilteredCount || 0,
            current: subjects.data?.PageNumber,
            pageSize: subjects.data?.PageSize,
            showLessItems: true,
            size: "small",
            showSizeChanger: true,
            locale: { items_per_page: "" },
            showTotal: (e) => `${e}`,
          }}
        rowKey={"Id"}
      />
      <Drawer
        title={t("subjectTicket.editSubjectTicket")}
        open={isShowEditDrawer}
        onClose={() => setIsShowEditDrawer(false)}
      >
        <AddOrUpdateSubjectTicket
          selectedRecord={selectedRecord}
          onFinish={() => setIsShowEditDrawer(false)}
        />
      </Drawer>
      
    </>
  );
};

export default ListItems;
