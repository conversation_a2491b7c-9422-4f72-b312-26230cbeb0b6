import { useQuery } from "react-query";
import endpoints from "./EndPoints";
import { getTicketCommentListFilter, getTicketForDetail, getTicketForStatus, getTicketHistory, getTicketListFilter, getTransitions, postTicketTransitionsExecute } from "./Services";

export const useGetTickets = (filter?: any) => {
  const query = useQuery(
    [endpoints.getTicketListFilter, filter],
    () => {
      return getTicketListFilter(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};

export const useGetTicketHistory = (filter?: any) => {
  const query = useQuery(
    [endpoints.getTicketHistory, filter], () => {
      return getTicketHistory(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );
  return query;
}

export const useGetTicketComments = (filter?: any) => {
  const query = useQuery(
    [endpoints.getTicketComments, filter],
    () => {
      return getTicketCommentListFilter(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};


export const useGetTicketStatus = (filter?: any) => {
  const query = useQuery(
    [endpoints.getTicketComments, filter],
    () => {
      return getTicketForStatus(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};


export const useGetTicketDetail = (ticketId: string) => {
  const query = useQuery(
    [endpoints.getTicketDetail, ticketId],
    () => {
      return getTicketForDetail(ticketId);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};

export const usePostTransitionsExecute = (ticketId: string, transitionId: string) => {
  const query = useQuery(
    [endpoints.postTicketTransitionExecute, ticketId, transitionId],
    () => {
      return postTicketTransitionsExecute(ticketId, transitionId);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};

export const useTransitions = (transitionId: string) => {
  const query = useQuery(
    [endpoints.getTransitions, transitionId],
    () => {
      return getTransitions(transitionId);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};




