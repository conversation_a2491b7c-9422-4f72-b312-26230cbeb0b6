import { Col, Row } from "antd";
import ListItems from "./Components/ListItems";
import TopOptions from "./Components/TopOptions";
import { FC, useEffect } from "react";
import Title from "./Components/Title";
import { useQueryClient } from "react-query";
import endPoints from "./EndPoints"

const TicketIndex: FC<{ mode: "calling" | "customer", pageType: "customer" | "ticket" }> = ({ mode, pageType }) => {
  const queryClient = useQueryClient()
  useEffect(() => {
    queryClient.resetQueries({
      queryKey: endPoints.getTicketListFilter,
      exact: false,
    });
  }, [])
  return (
    <>
      <Col xs={24} >
        <Row gutter={[0, 0]} >
          {
            pageType === "ticket" &&

            <Col xs={24}  >
              <Title />
            </Col>
          }
          <Col xs={24}  >
            <TopOptions />
          </Col>
          <Col xs={24} className="!px-2"  >
            <ListItems />
          </Col>

        </Row>
      </Col>
    </>
  );
};

export default TicketIndex;
