const baseRequestTicketUrl = "requests/tickets"
const baseRequestTicketsComments = "requests/tickets/comments"
const baseRequestTicketHistort = "requests/tickets"
const baseRequestTransitions = "request/transitions"
const endpoints = {
  getTicketListFilter: `${baseRequestTicketUrl}`,
  createTicket: `${baseRequestTicketUrl}`,
  updateTicketWithUrl: `${baseRequestTicketUrl}`,
  deleteTicket: `${baseRequestTicketUrl}`,
  getTicketComments: `${baseRequestTicketsComments}`,
  addCommentToTicket: `${baseRequestTicketsComments}`,
  deleteCommentTicket: `${baseRequestTicketsComments}`,
  getTicketHistory: `${baseRequestTicketHistort}`,
  getTicketForStatus: `${baseRequestTicketUrl}`,
  getTicketDetail: `${baseRequestTicketUrl}`,
  postTicketTransitionExecute: `${baseRequestTicketUrl}`,
  getTransitions: `${baseRequestTransitions}`,

};

export default endpoints;
