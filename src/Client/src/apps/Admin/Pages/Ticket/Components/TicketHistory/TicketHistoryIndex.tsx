import { Col, Row, Typography } from "antd";
import TicketHistory from "./ListItem";
import { useTranslation } from "react-i18next";

const TicketCommentIndex = () => {
  const { t } = useTranslation()
  return (
    <>
      <Row gutter={[0, 20]}>
        <Col xs={24} className="!max-h-[700px]">
          <TicketHistory />
        </Col>
      </Row>
    </>
  );
};

export default TicketCommentIndex;
