import { MazakaButton } from "@/apps/Common/MazakaButton";
import { MazakaForm } from "@/apps/Common/MazakaForm";
import { MazakaInput } from "@/apps/Common/MazakaInput";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import useMazakaForm from "@/hooks/useMazakaForm";
import { Button, Card, Checkbox, Col, Divider, Form, Input, Modal, Badge, Row, Space, Tabs, Upload, Image } from "antd";
import { FC, useEffect, useState } from "react";
import { useQueryClient } from "react-query";
import endPoints from "../EndPoints";
import { createTicket, postTicketTransitionsExecute, updateTicketWithPut } from "../Services";
import GeneralSubjectTicket from "@/apps/Common/GeneralSubjectTicket";
import GeneralCustomer from "@/apps/Common/GeneralCustomer";
import GeneralDepartments from "@/apps/Common/Departments/GeneralDepartments";
import { MessageOutlined, HistoryOutlined, FileTextOutlined, EyeOutlined } from "@ant-design/icons";
import { MazakaSelect } from "@/apps/Common/MazakaSelect";
import { MazakaDatePicker } from "@/apps/Common/MazakaDatePicker";
import { UploadProps } from "antd/lib";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";
import { MazakaTextArea } from "@/apps/Common/MazakaTextarea";
import dayjs from "dayjs";
import { useTranslation } from "react-i18next";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import GeneralUsers from "@/apps/Common/GenerralUsers";
import GeneralNotificationWays from "@/apps/Common/GeneralNotificationWays";
import { hanldleSetTicketDetails } from "../ClientSideStates";
import GeneralPlaceSearchInput from "@/apps/Common/GeneralPlaceSearchInput";
import CustomGoogleMap from "@/apps/Common/GoogleMap";
import { DeleteOutlined, InboxOutlined, PlusOutlined } from "@ant-design/icons";
import { Typography } from "antd";
import GeneralStatusTicket from "@/apps/Common/GeneralStatusTicket";
import { useGetTicketDetail } from "../ServerSideStates";
import FileManagerIndex from "@/apps/FileManager/FileManagerIndex";
import GeneralTicketList from "@/apps/Common/GeneralAllTickets";
import TicketCommentIndex from "./Comments/TicketCommentIndex";
import TicketHistoryIndex from "./TicketHistory/TicketHistoryIndex";
import TicketWatchersIndex from "./TicketWatchs/TicketWatchers";
import Title from "./Title";
import { handleResetAllFieldsFolder } from "@/apps/FileManager/ClientSideStates";
import { setBackEndUrl } from "@/helpers/SetBackEndUrl";
import SimpleMapComponent from "@/apps/Common/GoogleMapPin";

const { Text } = Typography;

// Dosya tipi tanımı
interface TicketFile {
  FileId: string;
  FileName: string;
  FilePath: string;
  MimeType?: string;
  FileSizeInBytes?: number;
}

// Address Info interface (backend için string tiplerini de destekler)
interface AddressAttributeData {
  latitude?: number;
  longitude?: number;
  address?: string;
  AddressDetail?: string;
}

const AddOrUpdateTicket = () => {
  const [isShowFileUploaderModal, setIsShowFileUploaderModal] = useState(false);
  const [showAddressFields, setShowAddressFields] = useState(true);
  const queryClient = useQueryClient();
  const [form] = Form.useForm();
  const { formActions, mazakaForm } = useMazakaForm(form);
  const { userInfoes } = useSelector((state: RootState) => state.profile);
  const { filter, pageType, ticketDetails } = useSelector((state: RootState) => state.ticket);
  const dispatch = useDispatch();
  const [fileList, setFileList] = useState<TicketFile[]>([]);
  const { t } = useTranslation();
  const [marker, setMarker] = useState<google.maps.LatLngLiteral | null>(null);
  const [isShowMap, setIsShowMap] = useState(false);
  const serverTicketDetails = useGetTicketDetail(ticketDetails?.Id);

  // Address management state (basitleştirildi)
  const [addressInfo, setAddressInfo] = useState<AddressAttributeData>({
    latitude: undefined,
    longitude: undefined,
    address: '',
    AddressDetail: ''
  });
  const [inputValue, setInputValue] = useState('');

  useEffect(() => {
    if (serverTicketDetails.data?.Value) {
      dispatch(hanldleSetTicketDetails({ data: serverTicketDetails.data.Value }));
    }
  }, [serverTicketDetails.data?.Value, dispatch]);

  // AttributeData'dan address bilgilerini yükleme (string'den number'a çevirme)
  const loadAddressFromAttributeData = (attributeData: any) => {
    if (attributeData && (attributeData.latitude || attributeData.longitude || attributeData.address || attributeData.AddressDetail)) {
      const addressData: AddressAttributeData = {
        // String değerleri number'a çevir
        latitude: attributeData.latitude ? parseFloat(attributeData.latitude) : undefined,
        longitude: attributeData.longitude ? parseFloat(attributeData.longitude) : undefined,
        address: attributeData.address || '',
        AddressDetail: attributeData.AddressDetail || ''
      };

      setAddressInfo(addressData);
      setInputValue(addressData.address || '');

      // Marker'ı set et
      if (addressData.latitude && addressData.longitude) {
        setMarker({
          lat: addressData.latitude,
          lng: addressData.longitude
        });
        // Eğer koordinatlar varsa address fields'i aç
        setShowAddressFields(true);
      }

      // Form field'larını set et
      form.setFieldsValue({
        Address: addressData.address,
        AddressDetail: addressData.AddressDetail
      });
    }
  };

  // AttributeData'ya address bilgilerini kaydetme (tüm değerler string)
  const prepareAttributeData = (currentAttributeData: any = {}) => {
    const attributeData = { ...currentAttributeData };

    if (showAddressFields) {
      // Koordinat bilgilerini STRING olarak ekle
      if (marker) {
        attributeData.latitude = marker.lat.toString();
        attributeData.longitude = marker.lng.toString();
      }

      // Adres bilgilerini ekle
      if (addressInfo.address) {
        attributeData.address = addressInfo.address;
      }

      const addressDetailValue = form.getFieldValue('AddressDetail');
      if (addressDetailValue) {
        attributeData.AddressDetail = addressDetailValue;
      }
    } else {
      // Address alanları kapalıysa address verilerini temizle
      delete attributeData.latitude;
      delete attributeData.longitude;
      delete attributeData.address;
      delete attributeData.AddressDetail;
    }

    return attributeData;
  };

  // Address seçimi fonksiyonu (basitleştirildi)
  const handleAddressSelection = (info: { country: string; state: string; city: string }) => {
    // Sadece address değerini güncelle, diğer bilgileri almıyoruz
    setAddressInfo(prev => ({
      ...prev,
      address: inputValue
    }));

    form.setFieldValue('Address', inputValue);
  };

  // Input değişikliği
  const handleInputValueChange = (value: string) => {
    setInputValue(value);
    setAddressInfo(prev => ({
      ...prev,
      address: value
    }));
    form.setFieldValue('Address', value);
  };

  // Marker değişikliği
  const handleMarkerChange = (coords: { lat: number; lng: number }) => {
    setMarker(coords);
    setAddressInfo(prev => ({
      ...prev,
      latitude: coords.lat,
      longitude: coords.lng
    }));
  };

  // Address alanlarını temizleme (basitleştirildi)
  const clearAddressData = () => {
    setAddressInfo({
      latitude: undefined,
      longitude: undefined,
      address: '',
      AddressDetail: ''
    });
    setMarker(null);
    setInputValue('');
    form.setFieldsValue({
      Address: '',
      AddressDetail: ''
    });
  };

  // Checkbox değişikliği
  const handleShowAddressChange = (checked: boolean) => {
    setShowAddressFields(checked);
    if (!checked) {
      clearAddressData();
    }
  };

  // Dosya seçimi tamamlandığında çalışacak fonksiyon
  const handleFileSelection = (selectedFiles: any) => {
    console.log("Selected files from modal:", selectedFiles);

    // Gelen objeyi array formatına çevirme
    const filesArray = Object.values(selectedFiles).map((file: any) => ({
      FileId: file.Id,
      FileName: file.FileName || file.OriginalFileName,
      FilePath: file.StoragePath,
      MimeType: file.MimeType,
      FileSizeInBytes: file.FileSizeInBytes
    }));

    // Mevcut dosyalarla birleştirme (duplikasyon kontrolü ile)
    setFileList(prevFiles => {
      const existingIds = prevFiles.map(f => f.FileId);
      const newFiles = filesArray.filter((file: TicketFile) => !existingIds.includes(file.FileId));
      return [...prevFiles, ...newFiles];
    });

    setIsShowFileUploaderModal(false);
    dispatch(handleResetAllFieldsFolder());
  };

  const handleRemoveFile = (fileId: string) => {
    setFileList((prev) => prev.filter((f) => f.FileId !== fileId));
  };

  // Dosya boyutunu formatlamak için yardımcı fonksiyon
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Dosya önizleme fonksiyonu
  const handleFilePreview = (file: TicketFile) => {
    const fileUrl = `${setBackEndUrl()}/Uploads/${file.FileId}`;

    if (isImageFile(file.MimeType || '')) {
      // Resimler için Ant Design Image component preview
      return;
    } else if (file.MimeType?.includes('pdf')) {
      // PDF için yeni tab'da aç
      window.open(fileUrl, '_blank');
    } else if (file.MimeType?.includes('word') || file.MimeType?.includes('text') ||
      file.MimeType?.includes('excel') || file.MimeType?.includes('spreadsheet')) {
      // Office dosyaları ve text dosyaları için indirme
      downloadFile(fileUrl, file.FileName);
    } else {
      // Diğer dosyalar için genel indirme
      downloadFile(fileUrl, file.FileName);
    }
  };

  // Dosya indirme fonksiyonu
  const downloadFile = (url: string, filename: string) => {
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Dosya tipine göre ikon döndüren fonksiyon (iyileştirildi)
  const getFileIcon = (mimeType: string) => {
    if (mimeType?.startsWith('image/')) {
      return '🖼️';
    } else if (mimeType?.includes('pdf')) {
      return '📄';
    } else if (mimeType?.includes('word')) {
      return '📝';
    } else if (mimeType?.includes('excel') || mimeType?.includes('spreadsheet')) {
      return '📊';
    } else if (mimeType?.includes('video/')) {
      return '🎥';
    } else if (mimeType?.includes('audio/')) {
      return '🎵';
    } else if (mimeType?.includes('zip') || mimeType?.includes('rar')) {
      return '📦';
    } else {
      return '📎';
    }
  };

  // Görsel dosya olup olmadığını kontrol eden fonksiyon
  const isImageFile = (mimeType: string) => {
    return mimeType?.startsWith('image/');
  };

  const tabItems = [
    {
      key: '1',
      label: (
        <Space size="small">
          <MessageOutlined style={{ fontSize: '16px' }} />
          <span>Aktiviteler</span>
        </Space>
      ),
      children: (
        <Card size="small" style={{ minHeight: '400px' }}>
          <TicketCommentIndex />
        </Card>
      ),
    },
    {
      key: '2',
      label: (
        <Space size="small">
          <HistoryOutlined style={{ fontSize: '16px' }} />
          <span>Geçmiş</span>
        </Space>
      ),
      children: (
        <Card size="small" style={{ minHeight: '400px' }}>
          <TicketHistoryIndex />
        </Card>
      ),
    },
    {
      key: '3',
      label: (
        <Space size="small">
          <FileTextOutlined style={{ fontSize: '16px' }} />
          <span>Sistem Logları</span>
        </Space>
      ),
      children: (
        <Card size="small" style={{ minHeight: '400px' }}>
          <div style={{ padding: '20px', textAlign: 'center', color: '#8c8c8c' }}>
            <FileTextOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
            <Title level={4} type="secondary">Aktiviteler</Title>
            <p>Ticket aktiviteleri burada gösterilecek...</p>
          </div>
        </Card>
      ),
    },
    {
      key: '4',
      label: (
        <Space size="small">
          <EyeOutlined style={{ fontSize: '16px' }} />
          <span>Takip Edenler</span>
          {ticketDetails?.Watchlist?.length > 0 && (
            <Badge
              count={ticketDetails.Watchlist.length}
              size="small"
              style={{ backgroundColor: '#52c41a' }}
            />
          )}
        </Space>
      ),
      children: (
        <Card size="small" style={{ minHeight: '400px' }}>
          <TicketWatchersIndex />
        </Card>
      ),
    },
  ];

  const handlePostTransitionStatus = async (transitionId: string) => {
    try {
      await postTicketTransitionsExecute(ticketDetails.Id, transitionId);
      queryClient.resetQueries({
        queryKey: endPoints.getTicketDetail,
        exact: false,
      });
    } catch (error) {
      showErrorCatching(error, null, false, t)
    }
  }

  const priorityOptions = [
    { label: t("ticket.list.low"), value: 1 },
    { label: t("ticket.list.medium"), value: 2 },
    { label: t("ticket.list.high"), value: 3 },
    { label: t("ticket.list.critical"), value: 4 },
  ];

  useEffect(() => {
    if (ticketDetails) {
      const data = { ...ticketDetails };
      data["EndDate"] = dayjs(data["EndDate"]);
      data["StatusId"] = ticketDetails?.Status?.Id;
      data["DepartmentIds"] = ticketDetails?.Departments?.map(
        (item: any) => item?.DepartmentId
      );
      form.setFieldsValue(data);

      // Mevcut ticket dosyalarını yükle
      if (ticketDetails.TicketFiles) {
        setFileList(ticketDetails.TicketFiles);
      }

      // AttributeData'dan address bilgilerini yükle
      if (ticketDetails.AttributeData) {
        try {
          const attributeData = typeof ticketDetails.AttributeData === 'string'
            ? JSON.parse(ticketDetails.AttributeData)
            : ticketDetails.AttributeData;
          loadAddressFromAttributeData(attributeData);
        } catch (error) {
          console.error("AttributeData parse edilemedi:", error);
        }
      }
    }
  }, [ticketDetails]);

  const hangleOnFinish = async () => {
    const formValues = form.getFieldsValue();
    formValues["TicketFiles"] = fileList;

    // AttributeData'yı hazırla - JSON.stringify YAPMA, object olarak gönder
    const currentAttributeData = ticketDetails?.AttributeData
      ? (typeof ticketDetails.AttributeData === 'string'
        ? JSON.parse(ticketDetails.AttributeData)
        : ticketDetails.AttributeData)
      : {};

    // AttributeData'yı object olarak gönder (string değil)
    formValues["AttributeData"] = prepareAttributeData(currentAttributeData);

    if (pageType === "customerDetails") {
      formValues["CustomerId"] = filter?.CustomerId;
    }

    console.log("Form submit values:", formValues);
    console.log("AttributeData being saved:", formValues["AttributeData"]);

    try {
      if (ticketDetails) {
        await updateTicketWithPut({ ...ticketDetails, ...formValues });
      } else {
        const response = await createTicket(formValues);
        dispatch(hanldleSetTicketDetails({ data: { Id: response?.Value } }))
      }

      openNotificationWithIcon("success", t("form.transactionSuccessful"));
      form.resetFields();
      setFileList([]);
      clearAddressData();
      setShowAddressFields(false);

      queryClient.resetQueries({
        queryKey: endPoints.getTicketListFilter,
        exact: false,
      });

    } catch (error) {
      showErrorCatching(error, mazakaForm, true, t);
    }
  };

  return (
    <>
      <MazakaForm
        className="rounded-none ticket-form !rounded-none !border-none"
        form={form}
        onFinish={hangleOnFinish}
        submitButtonVisible={false}
        initialValues={{
          NotificationWay: 4,
          Priority: 2,
        }}
      >
        <Row gutter={[16, 16]} className="!rounded-none border-none">
          {/* Sol Taraf - Ticket Detayları */}
          <Col xs={24} lg={16} className="rounded-none border-0 rounded-none">
            <Space direction="vertical" size="middle" className="rounded-none border-none" style={{ width: '100%' }}>
              <Card
                size="small"
                className="!rounded-none !border-none"
                headStyle={{ backgroundColor: '#fafafa', fontWeight: 600 }}
              >
                <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                  <GeneralSubjectTicket
                    label={t("ticket.list.subject")}
                    placeholder={t("ticket.list.subject")}
                    xs={24}
                    name="SubjectId"
                    className="d-flex !flex-row"
                    rules={[{ required: true, message: "" }]}
                    labelCol={{ span: 4 }}
                    wrapperCol={{ span: 20 }}
                  />

                  <MazakaInput
                    label={t("ticket.list.title")}
                    placeholder={t("ticket.list.title")}
                    xs={24}
                    name="Title"
                    labelCol={{ span: 4 }}
                    wrapperCol={{ span: 20 }}
                    rules={[{ required: true, message: "" }]}
                  />

                  <MazakaTextArea
                    xs={24}
                    name="Description"
                    labelCol={{ span: 4 }}
                    wrapperCol={{ span: 20 }}
                    label={t("ticket.list.description")}
                    placeholder={t("ticket.list.description")}
                    rows={6}
                  />

                  <GeneralNotificationWays
                    label={t("notificationWay.notificationMethod")}
                    labelCol={{ span: 4 }}
                    wrapperCol={{ span: 20 }}
                    placeholder={t("notificationWay.notificationMethod")}
                    name="NotificationWayId"
                    xs={24}
                  />
                </Space>

                {/* File Upload Section */}
                <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                  <Row gutter={[16, 12]}>
                    <Col
                      xs={24}
                      className="border-2 border-dashed border-gray-300 mt-4 mb-4 rounded-lg p-6 text-center cursor-pointer hover:border-blue-400 hover:bg-blue-50 transition-all duration-200"
                      onClick={() => setIsShowFileUploaderModal(true)}
                    >
                      <div className="flex flex-col items-center gap-2">
                        <InboxOutlined className="text-2xl text-gray-400" />
                        <span className="text-sm text-gray-600">
                          {t("ticket.list.uploadFile")}
                        </span>
                      </div>
                    </Col>

                    {fileList.length > 0 && (
                      <Col xs={24}>
                        <Card
                          className="!rounded-none !border-none"
                          size="small"
                          title={<Text strong style={{ fontSize: '12px' }}>Yüklenen Dosyalar ({fileList.length})</Text>}
                          style={{ backgroundColor: '#f9f9f9' }}
                        >
                          <Row gutter={[6, 6]}>
                            {fileList.map((file, index) => (
                              <Col xs={12} sm={8} md={6} key={file.FileId}>
                                <div
                                  style={{
                                    padding: '8px',
                                    backgroundColor: 'white',
                                    position: 'relative',
                                    minHeight: '80px',
                                    transition: 'all 0.2s ease'
                                  }}
                                  className="hover:shadow-sm hover:border-blue-300"
                                >
                                  <Button
                                    type="text"
                                    size="small"
                                    danger
                                    icon={<DeleteOutlined style={{ fontSize: '10px' }} />}
                                    onClick={() => handleRemoveFile(file.FileId)}
                                    style={{
                                      position: 'absolute',
                                      top: '2px',
                                      right: '2px',
                                      zIndex: 1,
                                      width: '20px',
                                      height: '20px',
                                      display: 'flex',
                                      alignItems: 'center',
                                      justifyContent: 'center'
                                    }}
                                  />

                                  <div className="text-center mb-1">
                                    {isImageFile(file.MimeType || '') ? (
                                      <div
                                        style={{
                                          width: '40px',
                                          height: '40px',
                                          margin: '0 auto',
                                          border: '1px solid #f0f0f0',
                                          borderRadius: '4px',
                                          overflow: 'hidden',
                                          display: 'flex',
                                          alignItems: 'center',
                                          justifyContent: 'center',
                                          backgroundColor: '#fafafa',
                                          cursor: 'pointer'
                                        }}
                                        title={file.FileName}
                                      >
                                        <Image
                                          width={38}
                                          height={38}
                                          src={`${setBackEndUrl()}/Uploads/${file.FilePath}`}
                                          style={{ objectFit: 'cover' }}
                                          preview={{
                                            src: `${setBackEndUrl()}/Uploads/${file.FilePath}`,
                                            mask: false
                                          }}
                                        />
                                      </div>
                                    ) : (
                                      <div
                                        style={{
                                          fontSize: '24px',
                                          margin: '0 auto',
                                          width: '40px',
                                          height: '40px',
                                          display: 'flex',
                                          alignItems: 'center',
                                          justifyContent: 'center',
                                          backgroundColor: '#f5f5f5',
                                          borderRadius: '4px'
                                        }}
                                        title={file.FileName}
                                      >
                                        {getFileIcon(file.MimeType || '')}
                                      </div>
                                    )}
                                  </div>

                                  <div style={{ textAlign: 'center' }}>
                                    <Text
                                      style={{
                                        fontSize: '10px',
                                        fontWeight: '500',
                                        display: 'block',
                                        lineHeight: '1.2',
                                        maxWidth: '100%',
                                        overflow: 'hidden',
                                        textOverflow: 'ellipsis',
                                        whiteSpace: 'nowrap'
                                      }}
                                      title={file.FileName}
                                    >
                                      {file.FileName}
                                    </Text>

                                    {file.FileSizeInBytes && (
                                      <Text style={{
                                        fontSize: '9px',
                                        color: '#999',
                                        display: 'block',
                                        marginTop: '2px'
                                      }}>
                                        {formatFileSize(file.FileSizeInBytes)}
                                      </Text>
                                    )}
                                  </div>
                                </div>
                              </Col>
                            ))}
                          </Row>
                        </Card>
                      </Col>
                    )}
                  </Row>
                </Space>

                {/* Address Section */}
                <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                  <Row align="middle" justify="space-between">
                    <Col>
                      <Checkbox
                        checked={showAddressFields}
                        onChange={(e) => handleShowAddressChange(e.target.checked)}
                      >
                        Detaylı adres bilgilerini göster
                      </Checkbox>
                    </Col>

                    {showAddressFields && (
                      <Col>
                        <Button
                          type="link"
                          size="small"
                          danger
                          onClick={clearAddressData}
                        >
                          Temizle
                        </Button>
                      </Col>
                    )}
                  </Row>

                  {showAddressFields && (
                    <Card
                      size="small"
                      title="Adres Detayları"
                      className="border border-gray-200 !rounded-none"
                    >
                      <Row>
                        <Col xs={12}>
                          <MazakaTextArea
                            name="AddressDetail"
                            className="mt-4"
                            labelCol={{ span: 24 }}
                            wrapperCol={{ span: 22 }}
                            label={t("ticket.list.adress")}
                            xs={24}
                            rows={3}
                          />
                        </Col>
                        <Col xs={12}>
                          <SimpleMapComponent></SimpleMapComponent>
                        </Col>

                      </Row>
                    </Card>
                  )}
                </Space>

                {/* Ticket Details Tabs */}
                {ticketDetails && (
                  <Card
                    size="small"
                    className="mt-2 !border-none !rounded-none"
                  >
                    <Tabs
                      defaultActiveKey="1"
                      items={tabItems}
                      type="line"
                      tabBarStyle={{
                        marginBottom: '16px',
                        borderBottom: '1px solid #f0f0f0'
                      }}
                      tabBarGutter={32}
                      animated={{ inkBar: true, tabPane: true }}
                    />
                  </Card>
                )}
              </Card>
            </Space>
          </Col>

          {/* Sağ Taraf - Form Controls */}
          <Col xs={24} lg={8} className="h-100 ticket-form-attach">
            <Card
              size="small"
              style={{ height: '100%', backgroundColor: '#fafafa' }}
              className="mt-1 rounded-none"
              headStyle={{ backgroundColor: '#fafafa', fontWeight: 600 }}
            >
              <Space direction="vertical" className="rounded-none p-0 m-0" size="middle" style={{ width: '100%', backgroundColor: '#fafafa' }}>
                <Row gutter={8} align="bottom" style={{ width: '100%' }}>
                  {ticketDetails && (
                    <Col flex={1}>
                      <GeneralStatusTicket
                        placeholder={t("ticket.list.status")}
                        xs={24}
                        ticketId={ticketDetails?.Id}
                        name="Status"
                        onChange={(value: string) => {
                          handlePostTransitionStatus(value);
                        }}
                      />
                    </Col>
                  )}

                  <Col>
                    <Form.Item className="mb-0">
                      <MazakaButton
                        processType={formActions.submitProcessType}
                        htmlType="submit"
                        status="save"
                        className="h-8 max-h-8"
                        style={{ height: '32px', maxHeight: '32px' }}
                      >
                        {ticketDetails ? t("ticket.list.edit") : t("ticket.list.add")}
                      </MazakaButton>
                    </Form.Item>
                  </Col>
                </Row>

                <Divider className="m-1" />

                <GeneralUsers
                  label={t("task.list.assignedUser")}
                  placeholder={t("task.list.assignedUser")}
                  name="UserId"
                  rules={[{ required: true, message: "" }]}
                  xs={24}
                />

                <GeneralDepartments
                  label={t("department.departments")}
                  placeholder={t("department.departments")}
                  multiple
                  name="DepartmentIds"
                />

                <GeneralTicketList
                  label="Ana Ticket"
                  placeholder="Ana Ticket"
                  ticketId={ticketDetails?.Id}
                  name="TopTicketId"
                />

                {pageType === "tickets" && (
                  <GeneralCustomer
                    label={t("ticket.list.customer")}
                    placeholder={t("ticket.list.customer")}
                    xs={24}
                    name="CustomerId"
                    rules={[{ required: true, message: "" }]}
                  />
                )}

                {pageType === "tickets" && (
                  <GeneralUsers
                    label={t("task.list.watchers")}
                    placeholder={t("task.list.watchers")}
                    name="Watchlist"
                    mode="multiple"
                    xs={24}
                  />
                )}

                <MazakaSelect
                  label={t("ticket.list.priority")}
                  placeholder={t("ticket.list.priority")}
                  xs={24}
                  name="Priority"
                  options={priorityOptions}
                  rules={[{ required: true, message: "" }]}
                />

                <MazakaDatePicker
                  label={t("ticket.list.endDate")}
                  xs={24}
                  name="EndDate"
                  disablePastDates
                  rules={[{ required: true, message: "" }]}
                />
              </Space>
            </Card>
          </Col>
        </Row>
      </MazakaForm>

      {/* Map Modal */}
      <Modal
        title="Haritadan Konum Seçin"
        width="80%"
        open={isShowMap}
        onCancel={() => setIsShowMap(false)}
        footer={[
          <Button key="cancel" onClick={() => setIsShowMap(false)}>
            İptal
          </Button>,
          <Button
            key="confirm"
            type="primary"
            onClick={() => setIsShowMap(false)}
            disabled={!marker}
          >
            Konumu Onayla
          </Button>
        ]}
      >
        <CustomGoogleMap
          form={form}
          marker={marker}
          setMarker={handleMarkerChange}
        />
      </Modal>

      {/* File Upload Modal */}
      <Modal
        open={isShowFileUploaderModal}
        onCancel={() => {
          setIsShowFileUploaderModal(false);
          dispatch(handleResetAllFieldsFolder());
        }}
        footer={false}
        width="90%"
      >
        <FileManagerIndex
          onFinishSelectFile={handleFileSelection}
        />
      </Modal>
    </>
  );
};

export default AddOrUpdateTicket;