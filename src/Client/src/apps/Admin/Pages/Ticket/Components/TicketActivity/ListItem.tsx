import { RootState } from "@/store/Reducers";
import { useSelector } from "react-redux";

import {
  Avatar,
  Row,
  Col,
  Typography,
  Tooltip
} from "antd";
import {
  EyeOutlined
} from "@ant-design/icons";

import dayjs from "dayjs";
import PageTitle from "@/apps/Common/PageTitle";
import { useTranslation } from "react-i18next";
import { getTransitions } from "../../Services";

const { Text } = Typography;

const TicketActivityItem = () => {
  const { ticketDetails } = useSelector((state: RootState) => state.ticket);
  const { t } = useTranslation();
  const history = getTransitions(ticketDetails?.TransitionId);
  const historyData = history ?? [];
  console.log("history", history);

  // Watchlist Component
  const WatchlistUsers = () => {
    let watchlist = ticketDetails?.Watchlist || [];

    if (!watchlist || watchlist.length === 0) {
      return null;
    }

    return (
      <div className="mb-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
        <div className="flex items-center gap-3 mb-2">
          <EyeOutlined className="text-blue-600" />
          <Text strong className="text-blue-800">İzleyenler ({watchlist.length})</Text>
        </div>
      </div>
    );
  };

  return (
    <Row>
      <Col xs={24}>
        <PageTitle title="İzleyenler" isSubTitle={true} />
        <WatchlistUsers />
      </Col>
    </Row>
  );
};

export default TicketActivityItem;