import { Col, Row, Tabs, Card, Space, Typography, Badge } from "antd";
import { MessageOutlined, HistoryOutlined, FileTextOutlined, EyeOutlined } from "@ant-design/icons";
import AddOrUpdateTicket from "./AddOrUpdateTicket";
import { useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";


const { Title } = Typography;

const AddOrUpdateIndex = () => {
  const { ticketDetails } = useSelector((state: RootState) => state.ticket);


  return (
    <Space direction="vertical" size="large" style={{ width: '100%' }}>
      {/* Ticket Form */}
      <Card
        size="small"
        className="!rounded-none !border-none"
      >
        <AddOrUpdateTicket />
      </Card>

    </Space>
  );
};

export default AddOrUpdateIndex;