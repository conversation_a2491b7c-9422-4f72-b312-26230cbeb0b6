import { RootState } from "@/store/Reducers";
import { useSelector } from "react-redux";

import {
  Avatar,
  Row,
  Col,
  Typography,
  Tooltip
} from "antd";
import {
  EyeOutlined
} from "@ant-design/icons";

import dayjs from "dayjs";
import PageTitle from "@/apps/Common/PageTitle";
import { useTranslation } from "react-i18next";

const { Text } = Typography;

const TicketHistoryIndex = () => {
  const { ticketDetails } = useSelector((state: RootState) => state.ticket);
  const { t } = useTranslation();

  // Watchlist Component
  const WatchlistUsers = () => {
    let watchlist = ticketDetails?.Watchlist || [];

    if (!watchlist || watchlist.length === 0) {
      return null;
    }

    return (
      <div className="mb-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
        <div className="flex items-center gap-3 mb-2">
          <EyeOutlined className="text-blue-600" />
          <Text strong className="text-blue-800">İzleyenler ({watchlist.length})</Text>
        </div>
        <Avatar.Group
          maxCount={8}
          maxPopoverPlacement="bottom"
          size="default"
          maxStyle={{
            color: '#1890ff',
            backgroundColor: '#e6f7ff',
            border: '1px solid #91d5ff'
          }}
        >
          {watchlist.map((user: any) => (
            <Tooltip
              key={user.Id || user.UserId}
              title={
                <div>
                  <div><strong>{user.UserName || user.Name}</strong></div>
                  {user.Email && <div>{user.Email}</div>}
                  {user.Department && <div>{user.Department}</div>}
                  <div className="text-xs mt-1 opacity-75">
                    Ekleme Tarihi: {user.AddedDate ? dayjs(user.AddedDate).format("DD.MM.YYYY") : '-'}
                  </div>
                </div>
              }
              placement="top"
            >
              <Avatar
                size="default"
                src={user.ProfileImage || user.Avatar}
                style={{
                  backgroundColor: user.ProfileImage ? 'transparent' : '#1890ff',
                  border: '2px solid #fff',
                  cursor: 'pointer'
                }}
              >
                {!user.ProfileImage && !user.Avatar && (
                  (user.UserName || user.Name)?.charAt(0)?.toUpperCase() || 'U'
                )}
              </Avatar>
            </Tooltip>
          ))}
        </Avatar.Group>
      </div>
    );
  };

  return (
    <Row>
      <Col xs={24}>
        <PageTitle title="İzleyenler" isSubTitle={true} />
        <WatchlistUsers />
      </Col>
    </Row>
  );
};

export default TicketHistoryIndex;