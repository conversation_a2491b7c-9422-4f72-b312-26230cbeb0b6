import { RootState } from "@/store/Reducers";
import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { useGetTicketHistory } from "../../ServerSideStates";

import {
  Timeline,
  Avatar,
  Space,
  Row,
  Col,
  Skeleton,
  Tag,
  Typography,
  Card
} from "antd";
import {
  UserOutlined,
  EditOutlined,
  UserSwitchOutlined,
  MessageOutlined,
  FileAddOutlined,
  TagOutlined,
  InfoCircleOutlined,
  ArrowRightOutlined,
  DeleteOutlined,
  TeamOutlined,
  EyeOutlined,
  PlusCircleOutlined
} from "@ant-design/icons";

import dayjs from "dayjs";
import PageTitle from "@/apps/Common/PageTitle";
import { useTranslation } from "react-i18next";

const { Text } = Typography;

// History Action Types - Modelinizdeki enum'a göre
enum TicketHistoryChangeType {
  Created = 1,
  Updated = 2,
  Assigned = 3,
  StatusChanged = 4,
  CommentAdded = 5,
  FileAdded = 6,
  FileRemoved = 7,
  DepartmentChanged = 8,
  WatchlistChanged = 9,
  TagsChanged = 10
}

const TicketHistoryIndex = () => {
  const { ticketDetails } = useSelector((state: RootState) => state.ticket);
  const { t } = useTranslation();

  const [filter, setFilter] = useState({
    PageNumber: 1,
    PageSize: 100,
    TicketId: ticketDetails?.Id,
  });

  useEffect(() => {
    setFilter(prev => ({ ...prev, TicketId: ticketDetails?.Id }));
  }, [ticketDetails?.Id]);

  const history = useGetTicketHistory(filter);
  const historyData = history?.data?.Value ?? [];

  // Action type'a göre icon ve renk belirleme
  const getActionIcon = (changeType: number) => {
    switch (changeType) {
      case TicketHistoryChangeType.Created:
        return { icon: <PlusCircleOutlined />, color: '#52c41a' };
      case TicketHistoryChangeType.Updated:
        return { icon: <EditOutlined />, color: '#faad14' };
      case TicketHistoryChangeType.Assigned:
        return { icon: <UserSwitchOutlined />, color: '#722ed1' };
      case TicketHistoryChangeType.StatusChanged:
        return { icon: <ArrowRightOutlined />, color: '#1890ff' };
      case TicketHistoryChangeType.CommentAdded:
        return { icon: <MessageOutlined />, color: '#13c2c2' };
      case TicketHistoryChangeType.FileAdded:
        return { icon: <FileAddOutlined />, color: '#eb2f96' };
      case TicketHistoryChangeType.FileRemoved:
        return { icon: <DeleteOutlined />, color: '#ff4d4f' };
      case TicketHistoryChangeType.DepartmentChanged:
        return { icon: <TeamOutlined />, color: '#52c41a' };
      case TicketHistoryChangeType.WatchlistChanged:
        return { icon: <EyeOutlined />, color: '#fa8c16' };
      case TicketHistoryChangeType.TagsChanged:
        return { icon: <TagOutlined />, color: '#722ed1' };
      default:
        return { icon: <InfoCircleOutlined />, color: '#d9d9d9' };
    }
  };

  // Action type'a göre açıklama metni
  const getActionDescription = (item: any) => {
    const fieldName = item.FieldName || '';

    switch (item.ChangeType) {
      case TicketHistoryChangeType.Created:
        return `Ticket oluşturuldu`;

      case TicketHistoryChangeType.Updated:
        return (
          <span>
            <strong>{fieldName}</strong> güncellendi
            {item.OldValue && item.NewValue && (
              <>
                : <Tag color="red" style={{ margin: '0 4px' }}>{item.OldValue}</Tag>
                →
                <Tag color="green" style={{ margin: '0 4px' }}>{item.NewValue}</Tag>
              </>
            )}
          </span>
        );

      case TicketHistoryChangeType.StatusChanged:
        return (
          <span>
            Durum değiştirildi:
            <Tag color="red" style={{ margin: '0 4px' }}>{item.OldValue}</Tag>
            →
            <Tag color="green" style={{ margin: '0 4px' }}>{item.NewValue}</Tag>
          </span>
        );

      case TicketHistoryChangeType.Assigned:
        return (
          <span>
            Atama yapıldı:
            {item.OldValue && <Tag style={{ margin: '0 4px' }}>{item.OldValue}</Tag>}
            {item.OldValue && '→'}
            <Tag color="blue" style={{ margin: '0 4px' }}>{item.NewValue}</Tag>
          </span>
        );

      case TicketHistoryChangeType.CommentAdded:
        return (
          <div>
            <div style={{ marginBottom: 8 }}>Yorum eklendi:</div>
            {item.Description && (
              <Card size="small" style={{ backgroundColor: '#f6f6f6' }}>
                <div dangerouslySetInnerHTML={{ __html: item.Description }} />
              </Card>
            )}
          </div>
        );

      case TicketHistoryChangeType.FileAdded:
        return (
          <span>
            Dosya eklendi:
            <Tag color="purple" style={{ margin: '0 4px' }}>{item.NewValue}</Tag>
          </span>
        );

      case TicketHistoryChangeType.FileRemoved:
        return (
          <span>
            Dosya kaldırıldı:
            <Tag color="red" style={{ margin: '0 4px' }}>{item.OldValue}</Tag>
          </span>
        );

      case TicketHistoryChangeType.DepartmentChanged:
        return (
          <span>
            Departman değiştirildi:
            <Tag style={{ margin: '0 4px' }}>{item.OldValue}</Tag>
            →
            <Tag color="green" style={{ margin: '0 4px' }}>{item.NewValue}</Tag>
          </span>
        );

      case TicketHistoryChangeType.WatchlistChanged:
        return (
          <span>
            İzleme listesi güncellendi:
            <Tag color="orange" style={{ margin: '0 4px' }}>{item.NewValue || 'Değişiklik yapıldı'}</Tag>
          </span>
        );

      case TicketHistoryChangeType.TagsChanged:
        return (
          <span>
            Etiketler güncellendi
            {item.OldValue && item.NewValue && (
              <>
                : <Tag color="red" style={{ margin: '0 4px' }}>{item.OldValue}</Tag>
                →
                <Tag color="blue" style={{ margin: '0 4px' }}>{item.NewValue}</Tag>
              </>
            )}
          </span>
        );

      default:
        return item.Description || `${fieldName} değiştirildi`;
    }
  };

  return (
    <Row>
      <Col xs={24}>
        <PageTitle title="Ticket Geçmişi" isSubTitle={true} />
      </Col>

      <Col xs={24}>
        <Skeleton loading={history.isLoading || history.isFetching}>
          {historyData.length > 0 ? (
            <Timeline
              className="!mt-4"
              mode="left"
              items={historyData.map((item: any) => {
                const { icon, color } = getActionIcon(item.ChangeType);
                return {
                  dot: (
                    <Avatar
                      size={32}
                      style={{ backgroundColor: color }}
                      icon={icon}
                    />
                  ),
                  children: (
                    <Space align="start" className="w-full">
                      <Avatar
                        size={24}
                        icon={<UserOutlined />}
                        className="flex-shrink-0"
                      />
                      <div className="flex-1 min-w-0">
                        <div className="flex gap-2 flex-col sm:flex-row sm:justify-between sm:items-center mb-2">
                          <Text strong className="text-sm">
                            {item.ChangedByUserName || 'Sistem'}
                          </Text>
                          <Text type="secondary" className="text-xs">
                            {dayjs(item.InsertDate || item.CreatedDate).format("DD.MM.YYYY HH:mm")}
                          </Text>
                        </div>
                        <div
                          className="text-sm break-words"
                          style={{
                            marginTop: 4,
                            wordWrap: 'break-word',
                            overflowWrap: 'break-word'
                          }}
                        >
                          {getActionDescription(item)}
                        </div>
                      </div>
                    </Space>
                  ),
                };
              })}
            />
          ) : (
            <div className="text-center py-8">
              <Text type="secondary">Henüz aktivite bulunmamaktadır.</Text>
            </div>
          )}
        </Skeleton>
      </Col>
    </Row>
  );
};

export default TicketHistoryIndex;