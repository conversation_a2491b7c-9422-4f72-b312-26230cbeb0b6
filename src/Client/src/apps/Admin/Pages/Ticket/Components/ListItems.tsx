import {
  DeleteOutlined,
  EditOutlined,
  EllipsisOutlined,
  FormOutlined,
} from "@ant-design/icons";
import {
  Col,
  Drawer,
  Dropdown,
  Modal,
  Table,
  Tag,
  Tooltip,
  Typography,
} from "antd";
import endPoints from "../EndPoints";
import { useQueryClient } from "react-query";
import { ItemType } from "antd/es/menu/interface";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";
import { useGetTickets } from "../ServerSideStates";
import { deleteTicket, getTicketForDetail } from "../Services";
import {
  hanldleSetTicketDetails,
  hanldleSetTicketFilter,
  hanldleSetTicketPageType,
} from "../ClientSideStates";
import AddOrUpdateTicket from "./AddOrUpdateTicket";
import {
  determineTicketNotificationWay,
  determineTicketPriority,
} from "@/helpers/Ticket";
import { useTranslation } from "react-i18next";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import dayjs from "dayjs";
import { excludeUnnesessaryKey } from "@/helpers/ExcludeUnNesessaryKey";
import AddOrUpdateIndex from "./AddOrUpdateIndex";

const ListItems = () => {
  const { Text } = Typography;
  const [isShowEditDrawer, setIsShowEditDrawer] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<any | null>(null);
  const { filter, pageType, ticketDetails } = useSelector((state: RootState) => state.ticket);
  const tickets = useGetTickets(excludeUnnesessaryKey(filter));
  const queryClient = useQueryClient();
  const dispatch = useDispatch();
  const { t } = useTranslation();
  const columns = [
    {
      title: t("ticket.list.title"),
      dataIndex: "Title",
      key: "Title",
      sorter: (a: any, b: any) => a?.Title.localeCompare(b?.Title),
      render: (value: string) => {
        return (
          <>
            <Text className="!text-xs">{value}</Text>
          </>
        );
      },
    },
    {
      title: t("ticket.list.subject"),
      dataIndex: "SubjectName",
      key: "Subject",
      sorter: (a: any, b: any) => a?.SubjectName.localeCompare(b?.SubjectName),
      render: (value: string) => {
        return (
          <>
            <Text className="!text-xs">{value}</Text>
          </>
        );
      },
    },
    {
      title: t("task.list.assignedUser"),
      dataIndex: "UserName",
      key: "UserName",
      sorter: (a: any, b: any) => a?.UserName.localeCompare(b?.UserName),
      render: (value: string) => {
        return (
          <>
            <Text className="!text-xs">{value}</Text>
          </>
        );
      },
    },
    {
      title: t("ticket.list.customer"),
      dataIndex: "CustomerName",
      key: "CustomerName",
      sorter: (a: any, b: any) =>
        a?.CustomerName.localeCompare(b?.CustomerName),
      render: (value: string) => {
        return (
          <>
            <Text className="!text-xs">{value}</Text>
          </>
        );
      },
    },

    {
      title: t("ticket.list.departments"),
      dataIndex: "Departments",
      key: "Departments",

      render: (value: any) => {
        return (
          <>
            {value?.length > 0 && (
              <>
                {value?.map((item: any) => {
                  return (
                    <>
                      <span>
                        <Tag color="#0096d1">{item?.Name || ""}</Tag>
                      </span>
                    </>
                  );
                })}
              </>
            )}
          </>
        );
      },
    },
    {
      title: t("task.list.notificationWay"),
      dataIndex: "NotificationWay",
      key: "Notification Way",
      sorter: (a: any, b: any) => a?.NotificationWay - b?.NotificationWay,

      render: (value: number) => {
        return (
          <>
            <Text className="!text-xs">{value || ""}</Text>
          </>
        );
      },
    },

    // {
    //   title: t("ticket.list.numberDaysPassed"),
    //   dataIndex: "NumberDaysPassed",
    //   key: "NumberDaysPassed",

    //   render: (value: number) => {
    //     return (
    //       <>
    //         <Text className="!text-xs">{value || ""}</Text>
    //       </>
    //     );
    //   },
    // },
    {
      title: t("ticket.list.priority"),
      dataIndex: "Priority",
      key: "Priority",
      sorter: (a: any, b: any) =>
        determineTicketPriority(a?.Priority, t)?.localeCompare(
          determineTicketPriority(b?.Priority, t)
        ),
      render: (value: number) => {
        return (
          <>
            <Text className="!text-xs">
              {determineTicketPriority(value, t)}
            </Text>
          </>
        );
      },
    },
    {
      title: t("ticket.list.insertDate"),
      dataIndex: "InsertDate",
      key: "InsertDate",

      render: (value: number) => {
        return (
          <>
            <Text className="!text-xs">
              {dayjs(value).format("YYYY-DD-MM HH:mm")}
            </Text>
          </>
        );
      },
    },
    {
      title: "",
      dataIndex: "edit",
      key: "edit",
      width: "8%",
      render: (key: any, record: any) => (
        <Col className="!flex gap-2 justify-end !px-2">
          <Tooltip title={t("ticket.list.edit")}>
            <FormOutlined
              className=" !text-[#0096d1] !text-sm"
              onClick={async (e) => {
                console.log("Record", record);
                e.preventDefault();
                e.stopPropagation();

                await dispatch(hanldleSetTicketDetails({ data: record }));

                queryClient.resetQueries({
                  queryKey: endPoints.getTicketComments,
                  exact: false,
                });
                setIsShowEditDrawer(true);
              }}
            />
          </Tooltip>

          <Tooltip title={t("ticket.list.delete")}>
            <DeleteOutlined
              className=" !text-[#9da3af] !text-sm"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                confirm(record);
              }}
            />
          </Tooltip>
        </Col>
      ),
    },
  ];

  const confirm = (record: any) => {
    Modal.confirm({
      title: t("ticket.list.warning"),
      icon: null,
      content: t("ticket.list.deleteModalDesc"),
      okText: t("ticket.list.delete"),
      cancelText: t("ticket.list.cancel"),
      onOk: async () => {
        try {
          await deleteTicket(record);
          openNotificationWithIcon("success", t("form.transactionSuccessful"));
          queryClient.resetQueries({
            queryKey: endPoints.getTicketListFilter,
            exact: false,
          });
        } catch (error: any) {
          showErrorCatching(error, null, false, t);
        }
      },
    });
  };
  const handleChangePagination = (pageNum: number, pageSize: number) => {
    let newFilter = { ...filter, PageNumber: pageNum, PageSize: pageSize };
    dispatch(hanldleSetTicketFilter({ filter: newFilter }));
  };
  return (
    <>
      <Table
        columns={columns}
        dataSource={tickets?.data?.Value}
        scroll={{ x: 700 }}
        loading={tickets.isLoading || tickets.isFetching}
        onRow={(record) => {
          return {
            onClick: async (event) => {
              await dispatch(hanldleSetTicketDetails({ data: record }));
              queryClient.resetQueries({
                queryKey: endPoints.getTicketDetail,
                exact: false,
              });
              setIsShowEditDrawer(true);
            },
          };
        }}
        pagination={{
          position: ["bottomRight"],
          className: "!px-0",
          onChange: handleChangePagination,
          total: tickets.data?.FilteredCount || 0,
          current: tickets.data?.PageNumber,
          pageSize: tickets.data?.PageSize,
          showLessItems: true,
          size: "small",
          showSizeChanger: true,
          locale: { items_per_page: "" },
          showTotal: (e) => `${e}`,
        }}
        rowKey={"Id"}
      />
      <Drawer
        width={ticketDetails != null ? "80%" : "40%"}
        title={t("ticket.list.editTicket") + " (" + ticketDetails?.Code + ")"}
        open={isShowEditDrawer}
        onClose={() => setIsShowEditDrawer(false)}
      >
        <AddOrUpdateIndex />
      </Drawer>
    </>
  );
};

export default ListItems;
