import { RootState } from "@/store/Reducers";
import { createSlice } from "@reduxjs/toolkit";
import { useSelector } from "react-redux";


const InitialState: { filter: any, } = {

  filter: {
    PageNumber: 1,
    PageSize: 30,

  },
};

const notificationWaySlice = createSlice({
  name: "NotificationWaySlice",
  initialState: InitialState,
  reducers: {
    hanldleSetNotificationWayFilter: (state, action) => {
      let data = action.payload;
      state.filter = data.filter;
    },



    handleResetAllFieldsNotificationWay: (state) => {
      Object.assign(state, InitialState);
    },
    handleResetFilterNotificationWay: (state) => {
      state.filter = {
        PageNumber: 1,
        PageSize: 30,
      }
    },
  },
});

export const { handleResetAllFieldsNotificationWay, handleResetFilterNotificationWay, hanldleSetNotificationWayFilter } = notificationWaySlice.actions;
export default notificationWaySlice;
