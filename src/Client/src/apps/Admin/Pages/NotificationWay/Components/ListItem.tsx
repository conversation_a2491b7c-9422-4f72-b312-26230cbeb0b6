import {
  DeleteOutlined,
  FormOutlined,
} from "@ant-design/icons";
import { Col, Drawer, Modal, Table, Tooltip, Typography } from "antd";

import endPoints from "../EndPoints";
import { useQueryClient } from "react-query";

import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";
import { useTranslation } from "react-i18next";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import { useGetNotificationWays } from "../ServerSideStates";
import { deleteNotificationWay } from "../Services";
import { hanldleSetNotificationWayFilter } from "../ClientSideStates";
import AddOrUpdateNotificationWay from "./AddOrUpdateNotificationWay";



const ListItems = () => {
  const { Text } = Typography
  const [isShowEditDrawer, setIsShowEditDrawer] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<any | null>(null);
  const { filter } = useSelector((state: RootState) => state.notificationWay)
  const { userInfoes } = useSelector((state: RootState) => state.profile);

  const notificationWays = useGetNotificationWays(filter);
  const queryClient = useQueryClient();
  const dispatch = useDispatch()
  const { t } = useTranslation()

  const columns = [
    {
      title: t("notificationWay.name"),
      dataIndex: "Name",
      key: "Name",
      width: "30%",
      sorter: (a: any, b: any) => a.Name.localeCompare(b.Name),
      render: (value: string) => {
        return (
          <>
            <Text className="!text-xs" >{value}</Text>
          </>
        )
      },
    },

    {
      title: "",
      dataIndex: "edit",
      key: "edit",
      width: "70%",
      render: (key: any, record: any) => (
        <Col className="!flex gap-2 justify-end !px-2">
          <Tooltip title={t("notificationWay.edit")}>
            <FormOutlined
              className=" !text-[#0096d1] !text-sm"
              onClick={async (e) => {
                e.preventDefault();
                e.stopPropagation();
                await setSelectedRecord(record);
                setIsShowEditDrawer(true);

              }}
            />
          </Tooltip>

          <Tooltip title={t("notificationWay.delete")}>
            <DeleteOutlined
              className=" !text-[#9da3af] !text-sm"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                confirm(record);
              }}
            />
          </Tooltip>
        </Col>
      ),
    },
  ];



  const confirm = (record: any) => {
    Modal.confirm({
      title: t("notificationWay.warning"),
      icon: null,
      content: t("notificationWay.deleteModalDesc"),
      okText: t("notificationWay.delete"),
      cancelText: t("notificationWay.cancel"),
      onOk: async () => {
        try {
          await deleteNotificationWay(record);
          openNotificationWithIcon("success", t("form.transactionSuccessful"));
          queryClient.resetQueries({
            queryKey: endPoints.getNotificationWayListFilter,
            exact: false,
          });
        } catch (error: any) {
          showErrorCatching(error, null, false, t)
        }
      },
    });
  };
  const handleChangePagination = (pageNum: number, pageSize: number) => {
    let newFilter = { ...filter, PageNumber: pageNum, PageSize: pageSize };
    dispatch(hanldleSetNotificationWayFilter({ filter: newFilter }));
  };
  return (
    <>
      <Table
        columns={columns}
        dataSource={notificationWays?.data?.Value?.Value || []}
        loading={notificationWays.isLoading || notificationWays.isFetching}
        onRow={(record) => {
          return {
            onClick: async (event) => {
              await setSelectedRecord(record);
              setIsShowEditDrawer(true);
            },
          };
        }}
        pagination={{
          position: ["bottomRight"],
          className: "!px-0",
          onChange: handleChangePagination,
          total: notificationWays.data?.FilteredCount || 0,
          current: notificationWays.data?.PageNumber,
          pageSize: notificationWays.data?.PageSize,
          showLessItems: true,
          size: "small",
          showSizeChanger: true,
          locale: { items_per_page: "" },
          showTotal: (e) => `${e}`,
        }}
        rowKey={"Id"}
      />
      <Drawer
        title={t("notificationWay.editNotificationWay")}
        open={isShowEditDrawer}
        onClose={() => setIsShowEditDrawer(false)}
      >
        <AddOrUpdateNotificationWay
          selectedRecord={selectedRecord}
          onFinish={() => setIsShowEditDrawer(false)}
        />
      </Drawer>

    </>
  );
};

export default ListItems;
