import React, { useState, useEffect } from 'react';
import { Tabs, Modal, message, Row } from 'antd';
import { useSelector, useDispatch } from 'react-redux';
import { DeleteOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { RootState } from '@/store/Reducers';
import { getCustomerQuickListFilter, deleteCustomerQuickListFilter } from '../Services';
import { useMutation, useQuery, useQueryClient } from 'react-query';
import { t } from 'i18next';
import { openNotificationWithIcon } from '@/helpers/OpenNotificationWithIcon';
import endpoints from '../EndPoints';
import { useGetCustomerQuickFilters } from '../ServerSideStates';
import { hanldleSetCustomerFilter, handleResetFilterCustomer } from '../ClientSideStates';

interface QuickFilterTabsProps {
    type?: "tempCustomer" | "customer";
    onFilterChange?: (filter: any) => void;
    style?: React.CSSProperties;
}

interface QuickFilter {
    Id: string;
    Name: string;
    Filters: any;
    CreatedAt: string;
}

const QuickFilterTabs: React.FC<QuickFilterTabsProps> = ({
    type = "customer",
    onFilterChange,
    style = { height: 60 }
}) => {
    const queryClient = useQueryClient();
    const dispatch = useDispatch();
    const [activeTabKey, setActiveTabKey] = useState<string>("");
    const [deleteModalVisible, setDeleteModalVisible] = useState(false);
    const [selectedFilterForDelete, setSelectedFilterForDelete] = useState<QuickFilter | null>(null);

    const { userInfoes } = useSelector((state: RootState) => state.profile);
    const { filter: currentFilter } = useSelector((state: RootState) => state.customer);

    // Hızlı filtreleri getir
    const {
        data: quickFiltersResponse,
        isLoading,
        error,
        refetch: refetchQuickFilters
    } = useGetCustomerQuickFilters(userInfoes?.Id);

    // API response'dan filtre listesini çıkar
    const quickFilters: QuickFilter[] = quickFiltersResponse?.Value || [];
    const hasFilters = quickFilters.length > 0;

    // Mevcut filtre ile eşleşen tab'ı bul ve aktif yap
    useEffect(() => {
        if (quickFilters.length > 0) {
            // Eğer currentFilter yoksa veya boşsa "Tümünü Göster" aktif olsun
            if (!currentFilter || Object.keys(currentFilter).length === 0) {
                setActiveTabKey("show-all");
                return;
            }

            // Quick filter verileri ile mevcut filter'ı karşılaştır
            const matchingFilter = quickFilters.find(filter => {
                return JSON.stringify(filter.Filters) === JSON.stringify(currentFilter);
            });

            if (matchingFilter) {
                // Eşleşen quick filter varsa o tab'ı aktif yap
                setActiveTabKey(matchingFilter.Id);
            } else {
                // Eşleşen quick filter yoksa sadece PageNumber ve PageSize varsa "Tümünü Göster" aktif olsun
                if (Object.keys(currentFilter).length === 2) {
                    const keys = Object.keys(currentFilter);
                    const hasOnlyPaginationKeys = keys.includes('PageNumber') && keys.includes('PageSize');

                    if (hasOnlyPaginationKeys) {
                        setActiveTabKey("show-all");
                    } else {
                        // 2 key var ama PageNumber ve PageSize değilse hiçbir tab aktif olmasın
                        setActiveTabKey("");
                    }
                } else {
                    // 2'den farklı key sayısı varsa hiçbir tab aktif olmasın
                    setActiveTabKey("");
                }
            }
        }
    }, [quickFilters, currentFilter]);

    // İki filtrenin eşit olup olmadığını kontrol et
    const isFilterMatching = (filter: QuickFilter) => {
        if (!currentFilter) return false;
        return JSON.stringify(filter.Filters) === JSON.stringify(currentFilter);
    };

    // Filtre silme mutation'ı
    const deleteFilterMutation = useMutation({
        mutationFn: async (filterId: string) => {
            return await deleteCustomerQuickListFilter({ id: filterId });
        },
        onMutate: async (filterId: string) => {
            // Optimistic update - UI'ı hemen güncelle
            await queryClient.cancelQueries(['customerQuickFilters', userInfoes?.Id]);

            // Mevcut veriyi sakla (rollback için)
            const previousData = queryClient.getQueryData(['customerQuickFilters', userInfoes?.Id]);

            // Cache'i hemen güncelle
            queryClient.setQueryData(['customerQuickFilters', userInfoes?.Id], (old: any) => {
                if (old?.Value) {
                    return {
                        ...old,
                        Value: old.Value.filter((filter: QuickFilter) => filter.Id !== filterId)
                    };
                }
                return old;
            });

            if (filterId === activeTabKey) {
                setActiveTabKey("show-all");

                // Redux store'ı ClientSideStates ile güncelle
                dispatch(handleResetFilterCustomer());

                // onFilterChange callback'i varsa da çağır
                if (onFilterChange) {
                    onFilterChange({ PageNumber: 1, PageSize: 20 });
                }

                // Query'leri sıfırla
                if (type === "tempCustomer") {
                    queryClient.invalidateQueries({
                        queryKey: ['tempCustomers']
                    });
                } else {
                    queryClient.invalidateQueries({
                        queryKey: ['customers']
                    });
                }
            }

            return { previousData };
        },
        onSuccess: () => {
            openNotificationWithIcon("success", t("quickfilter.deleteSuccess"));
            // Gerçek veriyi getir (optimistic update'i doğrula)
            refetchQuickFilters();
        },
        onError: (error: any, filterId: string, context: any) => {
            // Hata durumunda geri al
            if (context?.previousData) {
                queryClient.setQueryData(['customerQuickFilters', userInfoes?.Id], context.previousData);
            }
            openNotificationWithIcon("error", t("quickfilter.filterLoadingError"));
        },
        onSettled: () => {
            setDeleteModalVisible(false);
            setSelectedFilterForDelete(null);
        }
    });

    // Tab değiştiğinde veya aynı tab'a tekrar tıklandığında çalışacak fonksiyon
    const handleTabChange = async (tabKey: string) => {
        setActiveTabKey(tabKey);

        // "Tümünü Göster" tab'ı seçildiyse filtreleri sıfırla
        if (tabKey === "show-all") {
            // Redux store'ı ClientSideStates ile sıfırla
            dispatch(handleResetFilterCustomer());

            // onFilterChange callback'i varsa da çağır
            if (onFilterChange) {
                onFilterChange({ PageNumber: 1, PageSize: 20 });
            }

            if (type === "tempCustomer") {
                queryClient.invalidateQueries({
                    queryKey: ['tempCustomers']
                });
            } else {
                queryClient.invalidateQueries({
                    queryKey: ['customers']
                });
            }
            return;
        }

        const selectedFilter = quickFilters.find(filter => filter.Id === tabKey);

        if (selectedFilter && selectedFilter.Filters) {
            const filterValues = selectedFilter.Filters;

            // Redux store'ı ClientSideStates ile güncelle
            dispatch(hanldleSetCustomerFilter({ filter: filterValues }));

            if (type === "tempCustomer") {
                queryClient.invalidateQueries({
                    queryKey: ['tempCustomers', filterValues]
                });
            } else {
                queryClient.invalidateQueries({
                    queryKey: ['customers', filterValues]
                });
            }

            if (onFilterChange) {
                onFilterChange(filterValues);
            }
        }
    };

    const confirmDelete = (filter: QuickFilter) => {
        Modal.confirm({
            title: t("quickfilter.filterDeleteText"),
            icon: null,
            content: (
                <p>
                    <strong>"{filter.Name}"</strong> {t("quickfilter.filterDeleteDescp")}
                </p>
            ),
            okText: t("quickfilter.delete"),
            cancelText: t("quickfilter.filterCancelText"),
            onOk: async () => {
                // Mutation'ı çağır - optimistic update otomatik çalışacak
                deleteFilterMutation.mutate(filter.Id);
            },
        });
    };

    // Tab'a her tıklandığında çalışacak fonksiyon (aktif tab'a bile)
    const handleTabClick = (filterId: string) => {
        // "Tümünü Göster" tab'ı için
        if (filterId === "show-all") {
            // Redux store'ı default değerlerle güncelle
            if (onFilterChange) {
                onFilterChange({ PageNumber: 1, PageSize: 20 });
            }

            if (type === "tempCustomer") {
                queryClient.invalidateQueries({
                    queryKey: ['tempCustomers']
                });
            } else {
                queryClient.invalidateQueries({
                    queryKey: ['customers']
                });
            }
            return;
        }

        const selectedFilter = quickFilters.find(filter => filter.Id === filterId);

        if (selectedFilter && selectedFilter.Filters) {
            const filterValues = selectedFilter.Filters;

            // Her zaman query'leri yenile ve onFilterChange'i çağır
            if (type === "tempCustomer") {
                queryClient.invalidateQueries({
                    queryKey: ['tempCustomers', filterValues]
                });
            } else {
                queryClient.invalidateQueries({
                    queryKey: ['customers', filterValues]
                });
            }

            if (onFilterChange) {
                onFilterChange(filterValues);
            }
        }
    };

    // Silme butonuna tıklandığında
    const handleDeleteClick = (e: React.MouseEvent, filter: QuickFilter) => {
        e.stopPropagation();
        confirmDelete(filter);
        setSelectedFilterForDelete(null);
    };

    // Silme onaylama
    const handleConfirmDelete = () => {
        if (selectedFilterForDelete) {
            deleteFilterMutation.mutate(selectedFilterForDelete.Id);
        }
    };

    // Modal iptal
    const handleCancelDelete = () => {
        setDeleteModalVisible(false);
        setSelectedFilterForDelete(null);
    };

    // Tabs için items array'ini oluştur
    const tabItems = [
        // "Tümünü Göster" tab'ını en başa ekle (sadece hızlı filtreler varsa)
        ...(hasFilters ? [{
            label: (
                <div
                    className="ant-btn-color-primary radius-0 border !text-xs !p-2 !transition-all mzk-button-default !flex gap-2 !cursor-pointer"
                    onClick={() => handleTabClick("show-all")}
                >
                    <span className="tab-text">
                        {t("quickfilter.showAll") || "Tümünü Göster"}
                    </span>
                </div>
            ),
            key: "show-all",
            children: null,
        }] : []),
        // Mevcut hızlı filtreleri ekle
        ...quickFilters.map((filter) => ({
            label: (
                <div
                    className="ant-btn-color-primary radius-0 border !text-xs !p-2 !transition-all mzk-button-default !flex gap-2 !cursor-pointer"
                    onClick={() => handleTabClick(filter.Id)}
                >
                    <span className="tab-text">
                        {filter.Name}
                    </span>
                    <DeleteOutlined
                        className="tab-delete-icon !text-xs"
                        onClick={(e) => handleDeleteClick(e, filter)}
                        title={t("quickfilter.filterDeleteText")}
                    />
                </div>
            ),
            key: filter.Id,
            children: null,
        }))
    ];

    if (isLoading) {
        return (
            <div style={{ ...style, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                {t("quickfilter.filterLoading")}
            </div>
        );
    }

    if (error) {
        console.error(t("quickfilter.filterLoadingError"), error);
        return null;
    }

    if (!hasFilters) {
        return null;
    }

    return (
        <>
            <Tabs
                onChange={handleTabChange}
                activeKey={activeTabKey || undefined}
                tabPosition="top"
                style={{ ...style, flex: 1 }}
                items={tabItems}
                size="small"
                hideAdd
                className={`custom-filter-tabs ${!activeTabKey ? 'no-active-tab-quick' : ''}`}
                tabBarGutter={4}
            />

        </>
    );
};

export default QuickFilterTabs;