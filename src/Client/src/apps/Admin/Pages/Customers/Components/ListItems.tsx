import { DeleteOutlined, FormOutlined, UserOutlined } from "@ant-design/icons";
import { Col, Table, Tag, Typography, Modal, Tooltip, Tabs } from "antd";
import { FC, useState } from "react";

import {

  useSortable,
} from "@dnd-kit/sortable";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";
import {
  handleSetCustomersListSelectedItems,
  hanldleSetCustomerFilter,
} from "../ClientSideStates";
import { useGetCustomers } from "../ServerSideStates";
import { determineCustomerTypeColorValue } from "@/helpers/DetermineCustomerTypeColorValue";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import { convertToCustomer, deleteCustomer } from "../Services";
import customerEndPoints from "../EndPoints";
import tempCustomerEndPoints from "@/apps/Admin/Pages/TempCustomer/EndPoints";
import { useLocation, useNavigate, useSearchParams } from "react-router-dom";
import { useQueryClient } from "react-query";
import { useGetTempCustomers } from "../../TempCustomer/ServerSideStates";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import { useTranslation } from "react-i18next";
import { deleteTempCustomer } from "../../TempCustomer/Services";
import { hanldleSetTicketPageType } from "../../Ticket/ClientSideStates";
import { hanldleSetFindCustomerDrawer } from "../../AutoDialer/ClientSideStates";
import { commonRoutePrefix } from "@/routes/Prefix";
import ConvertToCustomer from "./AddOrUpdate/ConvertToCustomer";
import QuickFilterTabs from "./QuickFilterTabs";

interface ListItemsProps {
  type: "tempCustomer" | "customer" | "callInOut";
}

// Table Header Cell with drag-and-drop functionality
const TableHeaderCell = (props: any) => {
  const { attributes, listeners, setNodeRef, isDragging } = useSortable({
    id: props.id,
  });
  return (
    <th
      {...props}
      ref={setNodeRef}
      {...attributes}
      {...listeners}
    // className="!ant-th"
    />
  );
};

const ListItems: FC<ListItemsProps> = ({ type }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [searchParams, setSearchParams] = useSearchParams();
  const { customersListSelectedIds, filter } = useSelector(
    (state: RootState) => state.customer
  );
  const location = useLocation();
  const { userInfoes } = useSelector((state: RootState) => state.profile);
  const isEditAutoDialer = location.pathname.includes("dialer");
  const isIncomingCall = Boolean(searchParams.get("unSavedNumber"));

  const handleQuickFilterChange = (newFilter: any) => {
    dispatch(hanldleSetCustomerFilter({
      filter: {
        ...newFilter,
        PageNumber: 1
      }
    }));
  };




  let customers =
    type === "tempCustomer"
      ? useGetTempCustomers(filter)
      : useGetCustomers(filter);
  const dispatch = useDispatch();
  const allColumns = [
    {
      title: t("customers.filter.fullName"),
      dataIndex: "Name",
      render: (value: string, record: any) => {
        return (
          <div className="!flex items-center gap-1">
            <div
              className={`w-[12px] !h-[12px] ${determineCustomerTypeColorValue(
                "color",
                record?.Status
              )}`}
            ></div>
            <Text className="!text-xs ">{`${value || ""} ${record?.Surname
              }`}</Text>
          </div>
        );
      },
      key: "customer",
      sorter: (a: any, b: any) => {
        return a.Name.localeCompare(b.Name);
      },
    },
    {
      title: t("customers.list.type"),
      dataIndex: "Type",
      key: "",
      render: (value: number) => {
        return (
          <Text className="!text-xs">
            {value === 1
              ? t("customers.add.individual")
              : t("customers.add.corporate")}
          </Text>
        );
      },
      sorter: (a: any, b: any) => {
        const labelA =
          a?.Type === 1
            ? t("customers.add.individual")
            : t("customers.add.corporate");
        const labelB =
          b?.Type === 1
            ? t("customers.add.individual")
            : t("customers.add.corporate");
        return labelA.localeCompare(labelB);
      },
    },

    ...(type === "tempCustomer"
      ? [
        {
          title: t("customers.add.country"),
          dataIndex: "Country",
          render: (value: string) => {
            return <Text className="!text-xs">{value}</Text>;
          },
        },
        {
          title: t("customers.add.state"),
          dataIndex: "State",
          render: (value: string) => {
            return <Text className="!text-xs">{value}</Text>;
          },
        },
        {
          title: t("customers.add.city"),
          dataIndex: "City",
          render: (value: string) => {
            return <Text className="!text-xs">{value}</Text>;
          },
        },
      ]
      : [
        {
          title: t("customers.list.sector"),
          dataIndex: "Sector",
          key: "Sector",
          render: (value: string) => {
            return <Text className="!text-xs">{value}</Text>;
          },
          sorter: (a: any, b: any) => {
            return a?.Sector.localeCompare(b?.Sector);
          },
        },
      ]),

    {
      title: t("customers.list.phone"),
      dataIndex: "Phone",
      key: "Phone",
      render: (value: string) => {
        return <Text className="!text-xs">{value}</Text>;
      },
    },
    {
      title: t("customers.list.email"),
      dataIndex: "Email",
      key: "Email",
      render: (value: string) => {
        return <Text className="!text-xs">{value}</Text>;
      },
      sorter: (a: any, b: any) => {
        return a?.Email.localeCompare(b?.Email);
      },
    },

    {
      title: t("customers.list.classification"),
      dataIndex: "Classifications",

      render: (value: string[], record: any) => {
        return (
          <>
            {value?.map((name: string) => {
              return (
                <>
                  <Tag color="#0096d1">{name}</Tag>
                </>
              );
            })}
          </>
        );
      },
    },

    {
      title: t("customers.add.customerSource"),
      dataIndex: "CustomerSource",
      render: (value: string[], record: any) => {
        return (
          <>
            <Text className="!text-xs">{value}</Text>
          </>
        );
      },
    },
  ];
  const { Text } = Typography;
  const [visibleColumns, setVisibleColumns] = useState<string[]>(
    allColumns.map((col: any) => col.dataIndex)
  );
  const [columnOrder, setColumnOrder] = useState<string[]>(
    allColumns.map((col: any) => col.dataIndex)
  );


  const columns = [
    ...columnOrder
      .filter((dataIndex) => visibleColumns.includes(dataIndex))
      .map((dataIndex) => {
        const col = allColumns.find((c) => c.dataIndex === dataIndex)!;
        return {
          ...col,
          onHeaderCell: () => ({ id: dataIndex }),
        };
      }),
    ...(isEditAutoDialer
      ? []
      : [
        {
          title: "",
          dataIndex: "edit",
          key: "edit",
          width: "8%",
          render: (_: any, record: any) => (
            <>
              {!isIncomingCall && (
                <Col className="!flex gap-2 justify-end !px-2">
                  <Tooltip title={t("users.list.edit")}>
                    <FormOutlined
                      className=" !text-[#0096d1] !text-sm"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        if (type === "tempCustomer") {
                          navigate(
                            `${commonRoutePrefix}/edit-temp-customer?customerId=${record.Id}&type=tempCustomer`
                          );
                        } else {
                          if (isIncomingCall) {
                            const currentParams = Object.fromEntries(
                              searchParams.entries()
                            );
                            setSearchParams({
                              ...currentParams,
                              customerId: record.Id,
                            });
                          }

                          navigate(
                            `${commonRoutePrefix}/edit-customer/${record.Id}`
                          );
                        }
                        dispatch(
                          hanldleSetTicketPageType({
                            type: "customerDetails",
                          })
                        );
                        dispatch(
                          handleSetCustomersListSelectedItems({
                            selectedIds: [],
                            selectedItems: [],
                          })
                        );
                      }}
                    />
                  </Tooltip>

                  <Tooltip title={t("users.list.delete")}>
                    <DeleteOutlined
                      className=" !text-[#9da3af] !text-sm"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        confirm(record);
                      }}
                    />
                  </Tooltip>

                  {type === "tempCustomer" && (
                    <ConvertToCustomer
                      record={record}
                      pageType="tempCustomer"
                      showingType="icon"
                    />
                    // <Tooltip title={"Müşteriyi Dönüştür"}>
                    //   <UserOutlined
                    //     className=" !text-[#9da3af] !text-sm"
                    //     onClick={(e) => {
                    //       e.preventDefault();
                    //       e.stopPropagation();
                    //       convertToCustomerConfirm(record) 
                    //     }}
                    //   />
                    // </Tooltip>
                  )}
                </Col>
              )}
            </>
          ),
        },
      ]),
  ];

  const confirm = (record: any) => {
    Modal.confirm({
      title: t("customers.list.warning"),
      icon: null,
      content: t("customers.list.deleteModalDesc"),
      okText: t("customers.list.delete"),
      cancelText: t("customers.list.cancel"),
      onOk: async () => {
        try {
          type === "tempCustomer"
            ? await deleteTempCustomer(record)
            : await deleteCustomer(record);
          openNotificationWithIcon("success", t("form.transactionSuccessful"));

          queryClient.resetQueries({
            queryKey:
              type === "tempCustomer"
                ? tempCustomerEndPoints.getTempCustomerListFilter
                : customerEndPoints.getCustomerListFilter,
            exact: false,
          });
        } catch (error: any) {
          showErrorCatching(error, null, false, t);
        }
      },
    });
  };

  const handleChangePagination = (pageNum: number, pageSize: number) => {
    let newFilter = { ...filter, PageNumber: pageNum, PageSize: pageSize };
    dispatch(hanldleSetCustomerFilter({ filter: newFilter }));
  };

  const onSelectChange = (
    newSelectedRowKeys: React.Key[],
    newSelectedItems: any[]
  ) => {
    dispatch(
      handleSetCustomersListSelectedItems({
        selectedIds: newSelectedRowKeys,
        selectedItems: newSelectedItems,
      })
    );
  };
  const rowSelection: any =
  {
    // preserveSelectedRowKeys: true,
    selectedRowKeys: customersListSelectedIds,
    onChange: onSelectChange,
  };



  return (
    <>

      {type === "customer" && <QuickFilterTabs
        onFilterChange={handleQuickFilterChange}
        style={{
          height: 60, // Daha kompakt görünüm
          marginBottom: 16,
          backgroundColor: '#fafafa',
          padding: '8px',
          borderRadius: '6px'
        }}
      />}

      <Table
        size="small"
        columns={columns}
        loading={customers.isLoading || customers.isFetching}
        dataSource={customers?.data?.Value || []}
        components={{ header: { cell: TableHeaderCell } }}
        rowKey="Id"
        onRow={
          isEditAutoDialer
            ? undefined
            : (record) => {
              return {
                onClick: (event) => {
                  if (type === "tempCustomer") {
                    queryClient.resetQueries({
                      queryKey:
                        tempCustomerEndPoints.getTempCustomerListFilter,
                      exact: false,
                    });

                    navigate(
                      `${commonRoutePrefix}/edit-temp-customer?customerId=${record.Id}&type=tempCustomer`
                    );
                  } else {
                    queryClient.resetQueries({
                      queryKey: customerEndPoints.getCustomerListFilter,
                      exact: false,
                    });
                    if (isIncomingCall) {
                      const currentParams = Object.fromEntries(
                        searchParams.entries()
                      );
                      setSearchParams({
                        ...currentParams,
                        customerId: record.Id,
                      });
                      dispatch(
                        hanldleSetFindCustomerDrawer({ status: false })
                      );
                    } else {
                      navigate(
                        `${commonRoutePrefix}/edit-customer/${record.Id}`
                      );
                    }
                  }
                  dispatch(
                    hanldleSetTicketPageType({ type: "customerDetails" })
                  );
                  dispatch(
                    handleSetCustomersListSelectedItems({
                      selectedIds: [],
                      selectedItems: [],
                    })
                  );
                },
              };
            }
        }
        rowSelection={isIncomingCall ? undefined : rowSelection}
        pagination={{
          position: ["bottomRight"],
          className: "!px-0",
          onChange: handleChangePagination,
          total: customers.data?.FilteredCount || 0,
          current: customers.data?.PageNumber,
          pageSize: customers.data?.PageSize,
          showLessItems: true,
          size: "small",
          showSizeChanger: true,
          locale: { items_per_page: "" },
          showTotal: (e) => `${e}`,
        }}
      />



    </>
  );
};

export default ListItems;
