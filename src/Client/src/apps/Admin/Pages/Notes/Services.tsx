import { DataResponse } from "@/services/BaseResponseModel";
import endpoints from "./EndPoints";
import headers from "@/services/Headers.json";
import { deleteRequest, get, post, put } from "@/services/Client";
import { CreateUrlFilter } from "@/helpers/CreateURLFilter";


export const getThreeCXQueuListFilter = async (
  filter: any
): Promise<DataResponse<any>> => {
  const query = filter ? CreateUrlFilter(filter) : null

  const url = `${endpoints.getThreeCXQueuListFilter}?${query}`;
  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};

export const createThreeCXQueu = async (data: any): Promise<DataResponse<any>> => {
  const url = `${endpoints.createThreeCXQueu}`;
  const config = headers.content_type.application_json;
  return post<DataResponse<any>>(url, data, config);
};

export const updateThreeCXQueuWithPut = async (
  data: any
): Promise<DataResponse<any>> => {
  const url = `${endpoints.updateThreeCXQueuWithUrl}`;
  const config = headers.content_type.application_json;
  return put<DataResponse<any>>(url, data, config);
};

export const deleteThreeCXQueu = async (data: any): Promise<DataResponse<any>> => {
  const url = `${endpoints.deleteThreeCXQueu}/${data.Id}`;
  const config = headers.content_type.application_json;
  return deleteRequest<DataResponse<any>>(url, data, config);
};

export const getCallDetails = async (
  callId: string
): Promise<DataResponse<any>> => {
  const url = `${endpoints.getCallDetail}/${callId}`;

  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};
