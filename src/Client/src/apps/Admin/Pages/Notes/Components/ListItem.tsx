import { Col, Drawer, Dropdown, Modal, Table, Typography } from "antd";
import { useQueryClient } from "react-query";
import { useState } from "react";
import { useGetThreeCXQueus } from "../ServerSideStates";
import { useTranslation } from "react-i18next";
import { RootState } from "@/store/Reducers";
import { useDispatch, useSelector } from "react-redux";
import { hanldleSetNotesFilter } from "../ClientSideStates";
import CallItems from "../Components/ListItems";
import dayjs from "dayjs";

// Drawer içerisine ekleyeceğiniz component'i import edin
// import YourDrawerComponent from "./YourDrawerComponent";

const ListItems = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { Text } = Typography;
  const [isShowEditDrawer, setIsShowEditDrawer] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<any | null>(null);
  const [selectedCallId, setSelectedCallId] = useState<string | null>(null);
  const { filter } = useSelector((state: RootState) => state.notes);

  const ThreeCXQueus = useGetThreeCXQueus(filter);
  const queryClient = useQueryClient();

  const columns = [
    {
      title: t("notes.callPhone"),
      dataIndex: "CallPhone",
      key: "CallPhone",
      sorter: (a: any, b: any) => a.CallPhone.localeCompare(b.CallPhone),
      render: (value: string) => {
        return (
          <>
            <Text className="!text-xs">{value}</Text>
          </>
        );
      },
      width: "15%"
    },
    {
      title: t("notes.noteDescription"),
      dataIndex: "Content",
      key: "Content",
      sorter: (a: any, b: any) => a.Content.localeCompare(b.Content),
      render: (value: string) => {
        return (
          <>
            <Text className="!text-xs">{value}</Text>
          </>
        );
      },
      width: "30%"
    },
    {
      title: t("notes.customerName"),
      dataIndex: "CustomerName",
      key: "CustomerName",
      sorter: (a: any, b: any) => a.CustomerName.localeCompare(b.CustomerName),
      width: "15%"
    },
    {
      title: t("notes.insertUser"),
      dataIndex: "InsertUser",
      key: "InsertUser",
      sorter: (a: any, b: any) => a.InsertUser.localeCompare(b.InsertUser),
      width: "15%"
    },
    {
      title: t("notes.insertDate"),
      dataIndex: "InsertDate",
      key: "InsertDate",
      sorter: (a: any, b: any) => {
        return dayjs(a.InsertDate).valueOf() - dayjs(b.InsertDate).valueOf();
      },
      render: (text: string) => dayjs(text).format('DD/MM/YYYY HH:mm'),
      width: "20%"
    }
  ];

  const handleChangePagination = (pageNum: number, pageSize: number) => {
    let newFilter = { ...filter, PageNumber: pageNum, PageSize: pageSize };
    dispatch(hanldleSetNotesFilter({ filter: newFilter }));
  };

  const handleRowClick = async (record: any) => {
    await setSelectedRecord(record);
    setSelectedCallId(record.CallId || record.Id);// CallId veya Id'yi kullan
    setSelectedRecord(record);
    setIsShowEditDrawer(true);
  };

  const handleCloseDrawer = () => {
    setIsShowEditDrawer(false);
    setSelectedRecord(null);
    setSelectedCallId(null);
  };

  return (
    <>
      <Table
        columns={columns}
        dataSource={ThreeCXQueus?.data?.Value}
        loading={ThreeCXQueus.isLoading || ThreeCXQueus.isFetching}
        onRow={(record) => {
          return {
            onClick: async (event) => {
              await handleRowClick(record);
            },
          };
        }}
        pagination={{
          position: ["bottomRight"],
          className: "!px-0",
          onChange: handleChangePagination,
          total: ThreeCXQueus.data?.FilteredCount || 0,
          current: ThreeCXQueus.data?.PageNumber,
          pageSize: ThreeCXQueus.data?.PageSize,
          showLessItems: true,
          size: "small",
          showSizeChanger: true,
          locale: { items_per_page: "" },
          showTotal: (e) => `${e}`,
        }}
        rowKey={"Id"}
      />

      <Drawer
        title={t("notes.details")} // Drawer başlığını çeviri ile ayarlayın
        placement="right"
        width="50%"
        onClose={handleCloseDrawer}
        open={isShowEditDrawer}
        destroyOnClose={true}
      >
        {selectedCallId && (
          <div>
            {/* Örnek component kullanımı */}
            <CallItems mode="customer" selectedNote={selectedRecord} callId={selectedCallId} />
          </div>
        )}
      </Drawer>
    </>
  );
};

export default ListItems;