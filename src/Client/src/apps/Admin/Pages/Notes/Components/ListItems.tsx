import { FC, useState } from "react";
import { Drawer, Modal, Card, Tag, Tooltip, Typography, Row, Col, Divider } from "antd";
import dayjs from "dayjs";
import duration from 'dayjs/plugin/duration';

import { useQueryClient } from "react-query";
import { useTranslation } from "react-i18next";
import { determineCallStatus } from "@/helpers/Call";
import { useGetCallDetail } from "../ServerSideStates";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";
import { ArrowDownOutlined, ArrowUpOutlined, EditTwoTone, EyeTwoTone, PhoneOutlined, UserOutlined, ClockCircleOutlined } from "@ant-design/icons";

interface CallProps {
  mode: "calling" | "customer";
  selectedNote: any | null,
  callId: any | null;
}

const Call: FC<CallProps> = ({ mode, callId, selectedNote }) => {
  const queryClient = useQueryClient();
  const dispatch = useDispatch();
  const { t } = useTranslation();
  const { Text, Title } = Typography;
  const [isDrawerVisible, setIsDrawerVisible] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<any | null>(null);

  const calls = useGetCallDetail(callId);
  dayjs.extend(duration);

  const callData = calls?.data?.Value;

  // callId olmadığında component'i render etmeyin veya loading gösterin
  if (!callId) {
    return <div className="p-4 text-center text-gray-500">No call selected</div>;
  }

  if (calls.isLoading || calls.isFetching) {
    return <div className="p-4 text-center">Loading call details...</div>;
  }

  if (!callData) {
    return <div className="p-4 text-center text-red-500">No call data found</div>;
  }

  // Duration calculation
  const calculateDuration = () => {
    if (callData.EndTime && callData.StartTime) {
      const diff = dayjs(callData.EndTime).diff(dayjs(callData.StartTime));
      const dur = dayjs.duration(diff);

      const hours = dur.hours();
      const minutes = dur.minutes();
      const seconds = dur.seconds();

      if (hours > 0) {
        return `${hours} saat${minutes > 0 ? ` ${minutes} dakika` : ''}${seconds > 0 ? ` ${seconds} saniye` : ''}`;
      } else if (minutes > 0) {
        return `${minutes} dakika${seconds > 0 ? ` ${seconds} saniye` : ''}`;
      } else {
        return `${seconds} saniye`;
      }
    }
    return "N/A";
  };

  // Direction icon
  const getDirectionIcon = () => {
    if (callData.Direction === 0) {
      return (
        <Tooltip title={t("customers.add.inbound")}>
          <ArrowDownOutlined className="text-green-500 rotate-45" />
        </Tooltip>
      );
    } else if (callData.Direction === 1) {
      return (
        <Tooltip title={t("customers.add.outbound")}>
          <ArrowUpOutlined className="text-blue-500 rotate-45" />
        </Tooltip>
      );
    }
    return null;
  };

  const LabelValue = ({ label, value, icon = null }: { label: string; value: any; icon?: any }) => (
    <div className="mb-4">
      <div className="flex items-center gap-2 mb-1">
        {icon}
        <Text className="text-sm font-medium text-gray-600">{label}</Text>
      </div>
      <div className="pl-6">
        <Text className="text-base">{value || "N/A"}</Text>
      </div>
    </div>
  );

  return (
    <div className="p-0">
      <Card
        title={
          <div className="flex items-center gap-2">
            <PhoneOutlined />
            <span>{t("notes.callDetail")}</span>
            {getDirectionIcon()}
          </div>
        }
        className="shadow-sm"
      >
        <Row gutter={[24, 16]}>
          <Col xs={24} md={12}>
            <LabelValue
              label={t("customers.add.agent")}
              value={
                <div className="flex items-center gap-2">
                  <Tooltip title={determineCallStatus("value", callData?.Status, t)}>
                    <div
                      className={`w-3 h-3 rounded-full`}
                      style={{
                        backgroundColor: determineCallStatus("color", callData?.Status, t)
                      }}
                    ></div>
                  </Tooltip>
                  <span>{selectedNote?.InsertUser || t("customers.unknowUser")}</span>
                </div>
              }
              icon={<UserOutlined />}
            />

            <LabelValue
              label={t("customers.list.customer")}
              value={callData?.Customer ? callData?.Customer : "-"}
              icon={<UserOutlined />}
            />

            <LabelValue
              label={t("customers.list.customerPhone")}
              value={
                <div className="flex items-center gap-2">
                  <span>{callData?.Phone}</span>
                </div>
              }
              icon={<PhoneOutlined />}
            />

            <LabelValue
              label={t("notes.callId")}
              value={callData?.CallId}
            />
          </Col>

          <Col xs={24} md={12}>
            <LabelValue
              label={t("customers.add.durations")}
              value={calculateDuration()}
              icon={<ClockCircleOutlined />}
            />

            <LabelValue
              label={t("pause.list.startDate")}
              value={callData?.StartTime ? dayjs(callData.StartTime).format("DD/MM/YYYY HH:mm:ss") : "N/A"}
              icon={<ClockCircleOutlined />}
            />

            <LabelValue
              label={t("task.list.endDate")}
              value={callData?.EndTime ? dayjs(callData.EndTime).format("DD/MM/YYYY HH:mm:ss") : "N/A"}
              icon={<ClockCircleOutlined />}
            />

            <LabelValue
              label="Status"
              value={determineCallStatus("value", callData?.Status, t)}
            />
          </Col>
        </Row>

        {/* Notes Section */}
        {callData?.Notes && callData.Notes.length > 0 && (
          <>
            <Divider />
            <Title level={5}>{t("notes.note")}</Title>
            <div className="space-y-3">
              {callData.Notes.map((note: any) => (
                <Card key={note.Id} size="small" className="bg-gray-50">
                  <div className="mb-2">
                    <Text className="text-sm text-gray-500">
                      {dayjs(note.InsertDate).format("DD/MM/YYYY HH:mm:ss")}
                    </Text>
                  </div>
                  <Text>{note.Content}</Text>
                </Card>
              ))}
            </div>
          </>
        )}
      </Card>

      {/* Modal for editing */}
      {/* <Modal
        title={t("notes.detail")}
        open={isDrawerVisible}
        onCancel={() => {
          setIsDrawerVisible(false);
        }}
        footer={false}
        width={"50%"}
      >
        <ShowNoteDetail selectedRecord={selectedRecord} />
      </Modal> */}
    </div>
  );
};

export default Call;