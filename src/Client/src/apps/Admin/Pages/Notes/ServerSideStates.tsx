import { useQuery } from "react-query";
import endpoints from "./EndPoints";
import { getThreeCXQueuListFilter, getCallDetails } from "./Services";




export const useGetThreeCXQueus = (filter: any) => {
  const query = useQuery(
    [endpoints.getThreeCXQueuListFilter, filter],
    () => {
      return getThreeCXQueuListFilter(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};

export const useGetCallDetail = (callId: any | null) => {
  const query = useQuery(
    [endpoints.getThreeCXQueuListFilter, callId],
    () => {
      return getCallDetails(callId);
    },
    {
      enabled: !!callId,
      refetchOnWindowFocus: false,
      refetchOnMount: false,
      retry: 1,
      onError: (error) => {
        console.error('Call detail fetch error:', error);
      }
    }
  );

  return query;
};

