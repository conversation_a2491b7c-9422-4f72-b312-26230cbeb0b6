import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from "antd";
import { useState } from "react";
import Search from "./Search";
import { MazakaButton } from "@/apps/Common/MazakaButton";
import { PlusOutlined } from "@ant-design/icons";
import AddOrUpdateClassification from "./AddOrUpdateClassification";
import { useTranslation } from "react-i18next";


const TopOptions = () => {
  const [isShowAddDrawer, setIsShowAddDrawer] = useState(false);
  const { t } = useTranslation()
  return (
    <>
      <Row>
        {/* <>
          <Col xs={24}>
            <Search />
          </Col>
          <Col xs={24} className="">
            <Divider className="!m-0" />
          </Col>
          
        </> */}

        <div className=" !py-1 !px-2">
          <MazakaButton
            icon={<PlusOutlined />}
            onClick={() => {
              setIsShowAddDrawer(true);
            }}
          >
            {t("classification.add")}
          </MazakaButton>
        </div>
        <Col xs={24}>
          <Divider className="!m-0" />
        </Col>
      </Row>
      <Drawer
        title={t("threecxqueues.addQueu")}
        open={isShowAddDrawer}
        onClose={() => {
          setIsShowAddDrawer(false);
        }}
      >
        <AddOrUpdateClassification
          onFinish={() => {
            setIsShowAddDrawer(false);
          }}
        />
      </Drawer>
    </>
  );
};

export default TopOptions;
