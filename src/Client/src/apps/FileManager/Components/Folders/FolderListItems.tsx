import React, { useEffect, useRef, useState } from "react";
import { DeleteOutlined, FolderFilled } from "@ant-design/icons";
import { Modal, Tree } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { useTranslation } from "react-i18next";

import { useGetFolders } from "../../ServerSideStates";
import { RootState } from "@/store/Reducers";
import {
  handleSetAllFolderData,
  handleSetFolderFilter,
  handleSetSelectedFolder,
} from "../../ClientSideStates";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import { deleteFolder } from "../../Services";
import AddOrUpdateFolderButton from "./AddOrUpdateFolderButton";


const FolderListItems: React.FC = () => {
  const dispatch = useDispatch();
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const [deletedFolderIds, setDeletedFolderIds] = useState<number[]>([]);
  const { t } = useTranslation();
 
  const { filter, selectedFolder, allFoldersData } = useSelector(
    (state: RootState) => state.folder
  );
  const allFoldersRef = useRef<any[]>(allFoldersData);

  useEffect(() => {
    allFoldersRef.current = allFoldersData;
    
  }, [allFoldersData]);

  

  const folders = useGetFolders(filter);

  const getExpandedKeys = (nodes: any[]): React.Key[] => {
    let keys: React.Key[] = [];

    for (const node of nodes) {
      if (node.children && node.children.length > 0) {
        keys.push(node.key);
        keys = keys.concat(getExpandedKeys(node.children));
      }
    }

    return keys;
  };

  const insertChildrenIfMatch = (
    nodes: any[],
    targetId: number | string,
    childrenToInsert: any[]
  ): any[] => {
    return nodes.map((node) => {
      if (node.key === targetId) {
        const existingChildKeys = new Set(
          (node.children || []).map((child: any) => child.key)
        );
        const newChildren = childrenToInsert
          .filter((child) => !existingChildKeys.has(child.key))
          .filter((child) => !deletedFolderIds.includes(child.key)); // 🔥 SİLİNENLERİ FİLTRELE

        return {
          ...node,
          children: [...(node.children || []), ...newChildren],
        };
      } else if (node.children?.length) {
        return {
          ...node,
          children: insertChildrenIfMatch(
            node.children,
            targetId,
            childrenToInsert
          ),
        };
      }
      return node;
    });
  };


  useEffect(() => {
    if (!folders.data) return;

    const folderList =
      folders.data.Value?.map((item: any) => ({
        title:item.Name,
        parentId: selectedFolder?.Id || null,
        parentName: selectedFolder?.Name || null,
        text: item?.Name || "",
        key: item.Id,
        icon: <FolderFilled className="!text-[#ffb121] !text-lg" />,
        children: [],
        Id:item.Id
      })) || [];

    // ✅ Silinmiş ID'leri filtrele
    const cleanedFolderList = folderList.filter(
      (item) => !deletedFolderIds.includes(item.key)
    );

    if (!selectedFolder?.Id) {
      dispatch(handleSetAllFolderData({ data: cleanedFolderList }));
      setExpandedKeys(getExpandedKeys(cleanedFolderList));
    } else {
      const updatedTree = insertChildrenIfMatch(
        allFoldersRef.current,
        selectedFolder.Id,
        cleanedFolderList
      );
      dispatch(handleSetAllFolderData({ data: updatedTree }));

      setExpandedKeys((prev) =>
        prev.includes(selectedFolder.Id)
          ? prev
          : [...prev, selectedFolder.Id]
      );
    }
  }, [folders?.data, deletedFolderIds]);



  
 

  const onSelect = (selectedKeys: React.Key[], info: any) => {
    const folder:any = { Name: info?.node?.text || "", Id: info?.node?.key };
    if(info?.node?.key==="root")
    {
      dispatch(handleSetSelectedFolder({ data: null }));
      let currentFilters = {...filter}
      delete currentFilters["parentFolderId"]
      dispatch(
        handleSetFolderFilter({
          filter:currentFilters,
        })
      );
    }
    else{

      dispatch(handleSetSelectedFolder({ data: folder }));
      dispatch(
        handleSetFolderFilter({
          filter: { ...filter, parentFolderId: info?.node?.key },
        })
      );
    }
  };

  const removeNodeById = (nodes: any[], targetId: number | string): any[] => {
    return nodes
      .filter((node) => node.key !== targetId)
      .map((node) => {
        if (node.children && node.children.length > 0) {
          return {
            ...node,
            children: removeNodeById(node.children, targetId),
          };
        }
        return node;
      });
  };

  const confirm = (record: any) => {
    Modal.confirm({
      title: t("fileManager.warning"),
      icon: null,
      content: t("fileManager.deleteModalDesc"),
      okText: t("fileManager.delete"),
      cancelText: t("fileManager.cancel"),
      onOk: async () => {
        try {
          await deleteFolder(record);

          const updatedFolders = removeNodeById(
            allFoldersRef.current,
            record.Id
          );

          // ✅ Silinen ID'yi listeye ekle
          setDeletedFolderIds((prev) => [...prev, record.Id]);

          openNotificationWithIcon("success", t("form.transactionSuccessful"));
          dispatch(handleSetAllFolderData({ data: updatedFolders }));
        } catch (error: any) {
          showErrorCatching(error, null, false, t);
        }
      },
    });
  };

  useEffect(() => {
    let folders = [...allFoldersData];
  
    const hasRoot = folders.some((f) => f.key === "root");
  
    if (selectedFolder) {
      // ✅ root yoksa ekle
      if (!hasRoot) {
        const rootFolder = {
          title: "root",
          parentId: null,
          parentName: null,
          key: "root",
          icon: <FolderFilled className="!text-[#ffb121] !text-lg" />,
          children: [],
          Id: "root",
        };
        folders = [rootFolder, ...folders];
      }
    } else {
      // ✅ root varsa sil
      if (hasRoot) {
        folders = folders.filter((f) => f.key !== "root");
      }
    }
  
    dispatch(handleSetAllFolderData({ data: folders }));
  }, [selectedFolder]);
  



  return (
    <Tree
      className="!mt-2"
      showLine={true}
      showIcon={true}
      onSelect={onSelect}
      treeData={allFoldersData}
      expandedKeys={expandedKeys}
      onExpand={(keys) => setExpandedKeys(keys)}
      titleRender={(nodeData: any) => (
        <div className="!flex justify-between items-center group !w-full">
          <span className="!text-xs">{nodeData.title}</span>
          <span
            className="flex gap-1 pr-2 opacity-0 scale-95 group-hover:opacity-100 group-hover:scale-100 
                        transition-all duration-200 ease-in-out"
          >
            <AddOrUpdateFolderButton
              mode="edit"
              selectedRecord={nodeData}
             
            />
            <DeleteOutlined
              className="!text-[#9da3af] !text-sm cursor-pointer hover:scale-110 transition-transform duration-200"
              onClick={(e) => {
                e.stopPropagation();
                confirm(nodeData);
              }}
            />
          </span>
        </div>
      )}
    />
  );
};

export default FolderListItems;