import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import useMazakaForm from "@/hooks/useMazakaForm";
import { Col, Form,  Modal,  Row } from "antd";
import { FC, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { createFolder,  deleteFolder,  renameFolderName,  } from "../../Services";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import { MazakaForm } from "@/apps/Common/MazakaForm";
import { MazakaInput } from "@/apps/Common/MazakaInput";
import { MazakaButton } from "@/apps/Common/MazakaButton";
import { useQueryClient } from "react-query";
import endPoints from "../../EndPoints";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";
import { handleSetAllFolderData, } from "../../ClientSideStates";
import AddOrUpdateFolderButton from "./AddOrUpdateFolderButton";
import { DeleteOutlined } from "@ant-design/icons";



const AddOrUpdateFolderName: FC<{
  onFinish: () => void;
  mode: "add" | "edit";
  selectedRecord: any;


}> = ({ onFinish, mode, selectedRecord }) => {
  const [form] = Form.useForm();
  const { selectedFolder ,allFoldersData} = useSelector((state: RootState) => state.folder);
  const { formActions, mazakaForm } = useMazakaForm(form);
  const queryClient = useQueryClient();


 




  const removeNodeById = (nodes: any[], targetId: number | string): any[] => {
    return nodes
      .filter((node) => node.key !== targetId)
      .map((node) => {
        if (node.children && node.children.length > 0) {
          return {
            ...node,
            children: removeNodeById(node.children, targetId),
          };
        }
        return node;
      });
  };

 


 

  const editNodeById = (nodes: any[], targetId: string | number, newName: string): any[] => {
    return nodes.map((node) => {
      if (node.key === targetId) {
        return { ...node, title: newName
        
        };
      } else if (node.children && node.children.length > 0) {
        return {
          ...node,
          children: editNodeById(node.children, targetId, newName),
        };
      }
      return node;
    });
  };
  

 
const dispatch = useDispatch()
  const { t } = useTranslation();
  const handleOnFinish = async () => {
  
    mazakaForm.setLoading();
    const formValues = form.getFieldsValue();
    formValues["ParentFolderId"] =
      mode == "add" ? selectedFolder?.Id : selectedRecord?.Id || null;

    try {
      if (mode === "edit") {
       
        formValues["Id"] = selectedRecord?.Id
        await renameFolderName(formValues);
        const updatedData = editNodeById(allFoldersData, formValues["Id"], formValues["Name"]);
        console.log("updated folder",updatedData)
            dispatch(handleSetAllFolderData({ data: updatedData }));
      } else {
        await createFolder(formValues);
        queryClient.resetQueries({
          queryKey: endPoints.getFolderListFilter,
          exact: false,
        });
      }
      mazakaForm.setSuccess(2000, () => t("form.transactionSuccessful"));
      openNotificationWithIcon("success", t("form.transactionSuccessful"));
      form.resetFields();

     
     
      onFinish();
    } catch (error) {
      showErrorCatching(error, mazakaForm, true, t);
    }
  };

  useEffect(() => {
    if (mode === "edit") {
     
      console.log(selectedRecord)
      
      form.setFieldValue("Name",selectedRecord?.title);
    }
  }, [selectedRecord, mode]);



  return (
    <>
      <Col xs={24}>
        <MazakaForm
          form={form}
          onFinish={handleOnFinish}
          submitButtonVisible={false}
          
        >
          <Row
            gutter={[0, 10]}
           
          >
            <MazakaInput
              label={t("fileManager.name")}
              placeholder={t("fileManager.name")}
              xs={24}
              name={"Name"}
            />
            <Col xs={24}>
              <MazakaButton
                htmlType="submit"
                processType={formActions.submitProcessType}
                status="save"
               
              >
                {t("fileManager.save")}
              </MazakaButton>
              
            </Col>
          </Row>
        </MazakaForm>
      </Col>
    </>
  );
};

export default AddOrUpdateFolderName;
