import { MazakaButton } from "@/apps/Common/MazakaButton";
import { FolderAddOutlined, FormOutlined } from "@ant-design/icons";
import { Modal } from "antd";
import { FC, useState } from "react";
import { useTranslation } from "react-i18next";
import AddOrUpdateFolderName from "./AddOrUpdateFolderName";

const AddOrUpdateFolderButton: FC<{
  mode: "add" | "edit";
  selectedRecord?: any;
 
}> = ({ mode, selectedRecord }) => {
  const [isShowAddOrUpdateFolderModal, setIsShowAddOrUpdateFolderModal] =
    useState(false);
  const { t } = useTranslation();

  return (
    <>
      {mode === "add" ? (
        <>
          <MazakaButton
            icon={<FolderAddOutlined />}
            status="save"
            onClick={() => {
             
              setIsShowAddOrUpdateFolderModal(true);
            }}
          >
            {t("fileManager.addFolder")}
          </MazakaButton>
        </>
      ) : (
       
          <FormOutlined
            className="!text-[#0096d1] !text-sm cursor-pointer hover:scale-110 transition-transform duration-200"
            onClick={async (e) => {
              e.stopPropagation()
              setIsShowAddOrUpdateFolderModal(true);
            }}
          />
       
      )}

    <div
    className="!hidden"
    onClick={(e)=>{
      e.stopPropagation()
    }}
    >

        <Modal
          title={selectedRecord?t("fileManager.editFolder"):t("fileManager.addFolder")}
          footer={false}
          open={isShowAddOrUpdateFolderModal}
          onCancel={(e) => {
           
            setIsShowAddOrUpdateFolderModal(false);
          }}
        >
          <AddOrUpdateFolderName
            onFinish={() => {
              setIsShowAddOrUpdateFolderModal(false);
            }}
            mode={mode}
            selectedRecord={selectedRecord}
           
          />
        </Modal>
    </div>
      
    </>
  );
};

export default AddOrUpdateFolderButton;
