import { RootState } from "@/store/Reducers";
import { AppstoreOutlined, TableOutlined } from "@ant-design/icons";
import { useDispatch, useSelector } from "react-redux";
import { handleSetFolderContentDetailsViewingMode } from "../../ClientSideStates";
import { Tooltip } from "antd";
import { useTranslation } from "react-i18next";

const ViewingModes = () => {
  const { activeViewingMode } = useSelector((state: RootState) => state.folder);
  const dispatch = useDispatch()
  const {t} = useTranslation()
  return (
    <>
      <div className="!flex gap-1 !pr-5">
        <span
        onClick={()=>{
            dispatch(handleSetFolderContentDetailsViewingMode({mode:"table"}))
        }}
          className={`!p-1  !border !border-gray-300 !rounded-sm ${
            activeViewingMode === "table" ? "!text-black" : "!text-gray-300"
          } cursor-pointer`}
        >
          <Tooltip
          title={t("fileManager.listView")}
          >

          <TableOutlined className="!text-lg " />
          </Tooltip>
        </span>
        <span
        onClick={()=>{
            dispatch(handleSetFolderContentDetailsViewingMode({mode:"card"}))
        }}
          className={`!p-1  !border !border-gray-300 !rounded-sm ${
            activeViewingMode === "card" ? "!text-black" : "!text-gray-300"
          } cursor-pointer`}
        >
          <Tooltip
          title={t("fileManager.cardView")}
          >

          <AppstoreOutlined className="!text-lg" />
          </Tooltip>
        </span>
      </div>
    </>
  );
};

export default ViewingModes;
