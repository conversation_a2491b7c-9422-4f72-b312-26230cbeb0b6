import { Col,  Row } from "antd";
import Title from "./Title";
import TopOptions from "./TopOptions";
import ListIndex from "./ListIndex";
import { RootState } from "@/store/Reducers";
import { useSelector } from "react-redux";
import UnSelectFolder from "../Folders/UnSelectFolder";
import { FC } from "react";

const ContentsIndex:FC<{onFinishSelectFile:(item:any|any[])=>void}>  = ({onFinishSelectFile}) => {
  const { selectedFolder,  } = useSelector(
    (state: RootState) => state.folder
  );
  return (
    <>
    {
      selectedFolder?<>
      
      <Col xs={24}>
        <Row>
          
          <Col xs={24} className="" >
          <TopOptions onFinishSelectFile={onFinishSelectFile}/>
          </Col>
         <Col xs={24} className="!px-2" >
            <ListIndex/>
         </Col>
        </Row>
      </Col>
      </>:<>
      <div>
        <UnSelectFolder/>
      </div>
      </>
    }
    </>
  );
};

export default ContentsIndex;
