import GeneralDeleteButton from "@/apps/Common/GeneralDeleteButton";
import DownloadFileButton from "./DownloadFileButton";
import UploadFileButton from "./UploadFileButton";
import { useTranslation } from "react-i18next";
import endPoints from "../../EndPoints"
import { bulkDeleteFile } from "../../Services";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";
import { handleResetAllFieldsFolder, handleSetFolderContentListSelectedItems } from "../../ClientSideStates";
import { FC } from "react";
import { MazakaButton } from "@/apps/Common/MazakaButton";

const ButtonOptions :FC<{onFinishSelectFile:(item:any|any[])=>void}> = ({onFinishSelectFile}) => {
    const {folderContentsListSelectedIds,folderContentsListSelectedItems} = useSelector((state:RootState)=>state.folder)
    const {t} = useTranslation()
    const dispatch = useDispatch()
    
    return ( <>
    <div className="!flex gap-1" >
        <UploadFileButton/>
        <DownloadFileButton
        
        />
        <GeneralDeleteButton
            selectedIds={folderContentsListSelectedIds}
            descriptions=""
            serviceFunc={bulkDeleteFile}
            title={t("users.deleteAllButton")}
            listEndPoint={endPoints.getContentDetailsFilter}
            actionFunc={handleSetFolderContentListSelectedItems}
          />

          {
           typeof onFinishSelectFile === "function"&&
            <>
            <MazakaButton
            onClick={()=>{
                onFinishSelectFile({...folderContentsListSelectedItems})
                dispatch(
                      handleResetAllFieldsFolder()
                    );
            }}
            >
                {t("workFlow.completeSelection")}
            </MazakaButton>
            </>
          }
    </div>
    </> );
}
 
export default ButtonOptions;