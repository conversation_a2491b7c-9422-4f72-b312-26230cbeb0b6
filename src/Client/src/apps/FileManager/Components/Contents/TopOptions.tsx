import { Col, <PERSON><PERSON><PERSON>, <PERSON> } from "antd";

import Search from "./Search";
import ButtonOptions from "./ButtonOptions";
import { FC } from "react";

const TopOptions:FC<{onFinishSelectFile:(item:any|any[])=>void}> = ({onFinishSelectFile}) => {
  return (
    <Row>
      <Col xs={24} className="!flex gap-2 !py-1 !px-2">
        <ButtonOptions onFinishSelectFile={onFinishSelectFile}  />
      </Col>
      <Col xs={24}>
        <Divider className="!m-0 text-gray-400" />
      </Col>
      <Col xs={24} className="!px-2">
        <Search />
      </Col>
      <Col xs={24}>
        <Divider className="!m-0 !text-gray-400" />
      </Col>
    </Row>
  );
};

export default TopOptions;
