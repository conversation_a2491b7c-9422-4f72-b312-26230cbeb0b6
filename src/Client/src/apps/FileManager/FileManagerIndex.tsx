import { FC, useEffect } from "react";
import { useQueryClient } from "react-query";
import endPoints from "./EndPoints";
import { Col, Row } from "antd";
import Title from "./Components/Title";
import FolderIndex from "./Components/Folders/FolderIndex";
import ContentsIndex from "./Components/Contents/ContentsIndex";

const FileManagerIndex:FC<{onFinishSelectFile:(item:any|any[])=>void}> = ({onFinishSelectFile}) => {
  const queryClient = useQueryClient();

  useEffect(() => {
    queryClient.resetQueries({
      queryKey: endPoints.getFolderListFilter,
      exact: false,
    });
  }, []);
  return (
    <>
      <Col xs={24}>
        <Row gutter={[0, 0]}>
          <Col xs={24}>
            <Title />
          </Col>
          <Col xs={24}>
            <Row>
              <Col xs={24} xl={6} >
                <FolderIndex />
              </Col>
              <Col xs={18}  >
                <ContentsIndex onFinishSelectFile={onFinishSelectFile} />
              </Col>
            </Row>
          </Col>
        </Row>
      </Col>
    </>
  );
};

export default FileManagerIndex;
