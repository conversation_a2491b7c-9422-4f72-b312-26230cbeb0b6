import { DataResponse } from "@/services/BaseResponseModel";
import endpoints from "./EndPoints";
import headers from "@/services/Headers.json";
import { get } from "@/services/Client";

export const getCallDetails = async (
  callId: string
): Promise<DataResponse<any>> => {
  const url = `${endpoints.getCallDetail}/${callId}`;

  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};

