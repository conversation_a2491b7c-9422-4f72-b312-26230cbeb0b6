import { FC, useState, useMemo, useEffect } from "react";
import { Drawer, Modal, Table, Tag, Tooltip, Typography, Dropdown, Button, Checkbox, Space, Popover } from "antd";
import dayjs from "dayjs";
import duration from 'dayjs/plugin/duration';

import { useQueryClient } from "react-query";
import { useTranslation } from "react-i18next";
import { determineCallStatus } from "@/helpers/Call";
import { useGetCustomerCalls } from "@/apps/Admin/Pages/Customers/ServerSideStates";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";
import { hanldleSetCallFilter } from "../ClientSideStates";
import { ArrowDownOutlined, ArrowUpOutlined, CommentOutlined, EditTwoTone, EyeTwoTone, SettingOutlined, EyeOutlined, EyeInvisibleOutlined } from "@ant-design/icons";
import ShowNoteDetail from "./ShowNoteDetail";

const Call: FC<{ mode: "calling" | "customer" }> = ({ mode }) => {
  const queryClient = useQueryClient();
  const dispatch = useDispatch();
  const { t } = useTranslation();
  const { Text } = Typography;
  const { filter } = useSelector((state: RootState) => state.call);
  const [isDrawerVisible, setIsDrawerVisible] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<any | null>(null);
  const calls = useGetCustomerCalls(filter);
  dayjs.extend(duration);

  // Kolon görünürlük kontrolü
  const [visibleColumns, setVisibleColumns] = useState<string[]>([
    'agent', 'customer', 'customerPhone', 'durations', 'phone', 'startDate', 'endDate'
  ]);

  // LocalStorage'dan kolon ayarlarını yükle
  useEffect(() => {
    const savedColumns = localStorage.getItem(`callTableColumns_${mode}`);
    if (savedColumns) {
      setVisibleColumns(JSON.parse(savedColumns));
    }
  }, [mode]);

  // Kolon ayarlarını kaydet
  useEffect(() => {
    localStorage.setItem(`callTableColumns_${mode}`, JSON.stringify(visibleColumns));
  }, [visibleColumns, mode]);

  // Tüm kolonlar
  const allColumns = [
    {
      title: t("customers.add.agent"),
      columnKey: 'agent',
      width: "20%",
      render: (_: string, record: any) => {
        return (
          <div className=" !flex gap-1 items-center">
            <Tooltip title={determineCallStatus("value", record?.Status, t)}>
              <div
                className={`
              w-[12px] h-[12px] !bg-[${determineCallStatus(
                  "color",
                  record?.Status,
                  t
                )}] transition-all duration-300 
            group-hover:w-[90px] group-hover:h-[20px] flex items-center justify-center overflow-hidden
              `}
              ></div>
            </Tooltip>

            <Text className="!text-xs">{`${record?.User || ""} `}</Text>
            {record?.Direction === 0 ? (
              <Tooltip title={t("customers.add.inbound")}>
                <ArrowDownOutlined className="!text-xs !text-green-500 !rotate-[45deg]" />
              </Tooltip>
            ) : record?.Direction === 1 ? (
              <>
                <Tooltip title={t("customers.add.outbound")}>
                  <ArrowUpOutlined className="!text-xs !text-[#0096d1] !rotate-[45deg]" />
                </Tooltip>
              </>
            ) : (
              <></>
            )}
          </div>
        );
      },
    },
    {
      title: t("customers.list.customer"),
      columnKey: 'customer',
      render: (_: string, record: any) => {
        return (
          <>
            {
              record?.NoteCount > 0 && (
                <CommentOutlined
                  className="!text-[#0096d1] me-1"
                  onClick={async () => {
                    await setSelectedRecord(record)
                    setIsDrawerVisible(true);
                  }}
                  style={{ cursor: 'pointer' }}
                />
              )
            }

            <Text className="!text-xs">{`${record?.Customer || ""} `}</Text>
          </>
        );
      },
    },
    {
      title: t("customers.list.customerPhone"),
      columnKey: 'customerPhone',
      render: (_: string, record: any) => {
        return (
          <>
            <Text className="!text-xs">{`${record?.Phone || ""} `}</Text>
          </>
        );
      },
    },
    {
      title: t("customers.add.durations"),
      columnKey: 'durations',
      render: (_: string, record: any) => {
        return (
          <>
            {record.EndTime && record?.StartTime && (
              <Text className="!text-xs">
                {(() => {
                  const diff = dayjs(record.EndTime).diff(dayjs(record.StartTime));
                  const dur = dayjs.duration(diff);

                  const hours = dur.hours();
                  const minutes = dur.minutes();
                  const seconds = dur.seconds();

                  if (hours > 0) {
                    return `${hours} saat${minutes > 0 ? ` ${minutes} dakika` : ''}${seconds > 0 ? ` ${seconds} saniye` : ''}`;
                  } else if (minutes > 0) {
                    return `${minutes} dakika${seconds > 0 ? ` ${seconds} saniye` : ''}`;
                  } else {
                    return `${seconds} saniye`;
                  }
                })()}
              </Text>
            )}
          </>
        );
      },
    },
    {
      title: t("customers.list.phone"),
      columnKey: 'phone',
      render: (_: string, record: any) => {
        return (
          <>
            <Text className="!text-xs">{`${record?.Phone || ""} `}</Text>
          </>
        );
      },
    },
    {
      title: t("pause.list.startDate"),
      columnKey: 'startDate',
      render: (value: string, record: any) => {
        return (
          <>
            <div className="!flex gap-3">
              {record?.StartTime && (
                <Text className="!text-xs">
                  {dayjs(record?.StartTime).format("YYYY-MM-DD HH:mm:ss")}
                </Text>
              )}
            </div>
          </>
        );
      },
    },
    {
      title: t("task.list.endDate"),
      columnKey: 'endDate',
      render: (value: string, record: any) => {
        return (
          <>
            <div className="!flex gap-3">
              {record?.EndTime && (
                <Text className="!text-xs">
                  {dayjs(record?.EndTime).format("YYYY-MM-DD HH:mm:ss")}
                </Text>
              )}
            </div>
          </>
        );
      },
    },
  ];

  // Kolon görünürlüğünü toggle etme
  const handleColumnToggle = (columnKey: string, checked: boolean) => {
    if (checked) {
      setVisibleColumns([...visibleColumns, columnKey]);
    } else {
      setVisibleColumns(visibleColumns.filter(key => key !== columnKey));
    }
  };

  // Tüm kolonları göster/gizle
  const toggleAllColumns = (show: boolean) => {
    if (show) {
      setVisibleColumns(allColumns.map(col => col.columnKey!));
    } else {
      setVisibleColumns([]);
    }
  };

  // Filtrelenmiş kolonlar
  const filteredColumns = useMemo(() => {
    return allColumns.filter(column => visibleColumns.includes(column.columnKey!));
  }, [visibleColumns, allColumns]);

  // Pagination handler
  const handleChangePagination = (pageNum: number, pageSize: number) => {
    let newFilter = { ...filter, PageNumber: pageNum, PageSize: pageSize };
    dispatch(hanldleSetCallFilter({ filter: newFilter }));
  };

  // Kolon kontrolü için popover içeriği
  const columnControlContent = (
    <div className="w-65">
      <div className="mb-3 flex gap-2">
        <Button
          size="small"
          icon={<EyeOutlined />}
          onClick={() => toggleAllColumns(true)}
          className="flex-1"
        >
          Tümünü Göster
        </Button>
        <Button
          size="small"
          icon={<EyeInvisibleOutlined />}
          onClick={() => toggleAllColumns(false)}
          className="flex-1"
        >
          Tümünü Gizle
        </Button>
      </div>

      <div className="space-y-2">
        {allColumns.map((column) => (
          <div key={column.columnKey} className="flex items-center">
            <Checkbox
              checked={visibleColumns.includes(column.columnKey!)}
              onChange={(e) => handleColumnToggle(column.columnKey!, e.target.checked)}
              className="w-full"
            >
              <span className="text-sm">{column.title}</span>
            </Checkbox>
          </div>
        ))}
      </div>

      <div className="mt-3 pt-3 border-t text-xs text-gray-500">
        Görünür: {visibleColumns.length}/{allColumns.length} kolon
      </div>
    </div>
  );

  console.log("call", filter);

  return (
    <>
      {/* Kolon Kontrol Başlığı */}
      {/* <div className="mb-3 flex justify-between items-center">
        <Popover
          content={columnControlContent}
          title="Kolon Ayarları"
          trigger="click"
          placement="bottomRight"
        >
          <Button
            icon={<SettingOutlined />}
            size="small"
          >
            Kolonlar ({visibleColumns.length})
          </Button>
        </Popover>
      </div> */}

      <Table
        columns={allColumns}
        loading={calls.isLoading || calls.isFetching}
        dataSource={calls?.data?.Value}
        className={`${mode === "calling" ? "small-table-title" : ""}`}
        pagination={{
          position: ["bottomRight"],
          className: "!px-0",
          onChange: handleChangePagination,
          total: calls.data?.FilteredCount || 0,
          current: calls.data?.PageNumber,
          pageSize: calls.data?.PageSize,
          showLessItems: true,
          size: "small",
          showSizeChanger: true,
          locale: { items_per_page: "" },
          showTotal: (e) => `${e}`,
        }}
        scroll={{ x: true }}
      />

      <Modal
        title={t("notes.detail")}
        open={isDrawerVisible}
        onCancel={() => {
          setIsDrawerVisible(false);
        }}
        footer={false}
        width={"50%"}
      >
        <ShowNoteDetail selectedRecord={selectedRecord} />
      </Modal>
    </>
  );
};

export default Call;