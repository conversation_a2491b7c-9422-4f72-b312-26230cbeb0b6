// CreateFormItem.tsx
import { FC, useEffect, useState } from "react";
import { MazakaInput } from "../Common/MazakaInput";
import { MazakaTextArea } from "../Common/MazakaTextarea";
import { MazakaSelect } from "../Common/MazakaSelect";
import MazakaInputNumber from "../Common/MazakaInputNumber";
import { Col, Form, FormInstance, message, Radio, Tag } from "antd";
import { MazakaCheckbox } from "../Common/MazakaCheckbox";
import GeneralUsers from "../Common/GenerralUsers";
import GeneralRoles from "../Common/GeneralRoles";
import GeneralPermissions from "../Common/GeneralPermission";
import { useTranslation } from "react-i18next";

const CreateFormItem: FC<{
  item: any;
  form?:FormInstance,
  watchedValues: any;
  xs?: number;
  sm?: number;
  md?: number;
  lg?: number;
  xl?: number;
}> = ({ item, watchedValues, xs, sm, md, lg, xl ,form}) => {
  const [visible, setVisible] = useState(true);
const {t} = useTranslation()
  useEffect(() => {
    if (item?.showTopField && Array.isArray(item?.showTopValue)) {
      const value = watchedValues?.[item.showTopField];
      setVisible(item.showTopValue.includes(value));
    } else {
      setVisible(true);
    }
  }, [watchedValues, item?.showTopField, item?.showTopValue]);

  if (!visible) return null;

  switch (item?.type) {
    case "TextBox":
      return (
        <MazakaInput
          name={item?.name}
          label={item?.label}
          placeholder={item?.label}
          xs={xs}
          md={md}
          xl={xl}
          rules={[{ required: item?.required, message: "" }]}
        />
      );
    case "TextArea":
      return (
        <MazakaTextArea
          name={item?.name}
          label={item?.label}
          placeholder={item?.placeholder||item.label}
          xs={xs}
          md={md}
          xl={xl}
          rules={[{ required: item?.required, message: "" }]}
        />
      );
    case "Dropdown":
      if (item?.options?.items?.length > 0) {
        return (
          <MazakaSelect
            name={item?.name}
            label={item?.label}
            placeholder={item?.label}
            xs={xs}
            md={md}
            xl={xl}
            mode={item.multiple ? "multiple" : undefined}
            options={item?.options?.items}
            rules={[{ required: item?.required, message: "" }]}
          />
        );
      }
      switch (item?.options?.dataSource) {
        case "roles":
          return (
            <GeneralRoles
              name={item?.name}
              label={item?.label}
              placeholder={item?.label}
              xs={xs}
              md={md}
              xl={xl}
              mode={item.multiple ? "multiple" : undefined}
              rules={[{ required: item?.required, message: "" }]}
            />
          );
        case "users":
          return (
            <GeneralUsers
              name={item?.name}
              label={item?.label}
              placeholder={item?.label}
              xs={xs}
              md={md}
              xl={xl}
              mode={item.multiple ? "multiple" : undefined}
              rules={[{ required: item?.required, message: "" }]}
            />
          );
        case "permissions":
          return (
            <GeneralPermissions
              name={item?.name}
              label={item?.label}
              placeholder={item?.label}
              xs={xs}
              md={md}
              xl={xl}
              multiple={item?.multiple}
              rules={[{ required: item?.required, message: "" }]}
            />
          );
      }
      return <></>;
    case "Number":
      return (
        <MazakaInputNumber
          name={item?.name}
          label={item?.label}
          placeholder={item?.label}
          xs={xs}
          md={md}
          xl={xl}
          rules={[{ required: item?.required, message: "" }]}
        />
      );
    case "Radio":
      return (
        <Form.Item
          name={item?.name}
          label={item?.label}
          rules={[{ required: item?.required, message: "" }]}
        >
          <Radio.Group
            options={item?.Data ? JSON.parse(item.Data) : []}
            optionType="default"
          />
        </Form.Item>
      );
    case "Checkbox":
      return (
        <MazakaCheckbox
          name={item?.Name}
          text={item?.label}
          xs={xs}
          md={md}
          xl={xl}
          rules={[{ required: item?.required, message: "" }]}
          onChange={(e)=>{
            let status = e.target.checked
            // form?.setFieldValue(item?.Name,status)
          }}
        />
        
      );
      case "Tag":
        return(
          <Col
          xs={xs}
          md={md}
          xl={xl}
          className="!flex gap-1 flex-wrap"
          >
           {
            item?.options?.items?.map((tag:any)=>{
              return(
                <>
                  <Tag
                  color="blue"
                  className="!cursor-pointer"
                  onClick={()=>{
                    const key = tag?.value
                    navigator.clipboard.writeText(key.toString()).then(() => {
                      message.success(t("workFlow.tagCopied"));
                    }).catch(() => {
                      message.error(t("form.transactionFaild"));
                    });
                  }}
                  >{tag?.label||""}</Tag>
                </>
              )
            })
           }
          </Col>
        )
    default:
      return <></>;
  }
};

export default CreateFormItem;
