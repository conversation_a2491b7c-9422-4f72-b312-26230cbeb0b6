import { FormInstance } from "antd";
import { ColProps } from "antd/lib/col";
import { FormItemProps } from "antd/lib/form";
import { DefaultOptionType, SelectProps } from "antd/lib/select";



export interface MazakaSelectProps extends Omit<FormItemProps<any>, "children" | "onReset" | "status">,
  Omit<ColProps, "defaultValue" | "onChange" | "onSelect" | "onBlur" | "onFocus" | "placeholder" | "prefix">,
  Omit<SelectProps<any, DefaultOptionType>, "prefix"> {
  colClassName?: any;
  inputClassName?: any;
  placeholder?: string;
  inputClass?: string;
  containerClass?: string;
  form?: FormInstance;
  country?: string,
  setInitialDataWithPhone?: boolean
  setInitialDataWithPhoneService?: any,
  setIsFindUser?: any;
  setIsExternalLading?: any,
  isExistingUser?: boolean,
  multiple?: boolean,
  pageType?: "users" | "customer"
  labelCol?: ColProps;
  wrapperCol?: ColProps;

}
