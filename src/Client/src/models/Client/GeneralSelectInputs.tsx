import { ColProps, FormInstance } from "antd";
import { SizeType } from "antd/lib/config-provider/SizeContext";


export interface GeneralSelectInputs {
  xs?: number;
  sm?: number;
  md?: number;
  lg?: number;
  xl?: number;
  label?: string;
  placeholder?: string;
  rules?: any;

  name?: string;
  multiple?: boolean;
  mode?: "tags" | "multiple" | undefined;
  onDeselect?: Function;
  className?: string;
  onChange?: any;
  acceptedStatusLocalizerList?: string[];
  defaultValue?: any;
  setOptionId?: any;
  value?: any;
  size?: SizeType;
  disabled?: boolean;
  isLoading?: boolean;
  bordered?: boolean;
  span?: number;
  tooltip?: string;
  filter?: any;
  pageType?: string;
  wrapperXs?: number;
  wrapperSm?: number;
  wrapperMd?: number;
  wrapperLg?: number;
  wrapperXl?: number;
  style?: any;
  isConvertToObj?: boolean;
  externalLoading?: boolean;
  externalRefetch?: boolean;
  errorCallBack?: any;
  externalValueId?: any;
  isShowAddComponentOptions?: boolean;
  onClear?: any;
  required?: boolean;
  containerClass?: string;
  inputClass?: string;
  form?: FormInstance | any;
  setExternalRefetch?: any;
  allowClear?: boolean;
  excludeIds?: any[]
  labelCol?: ColProps;
  wrapperCol?: ColProps;
}
