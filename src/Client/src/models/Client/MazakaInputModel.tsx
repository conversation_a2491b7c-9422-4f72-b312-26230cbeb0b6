import { ColProps, InputProps } from "antd";
import { FormItemProps } from "antd/lib/form";
import { HTMLAttributes } from "react";

export interface MazakaInputProps
  extends Omit<ColProps, keyof HTMLAttributes<HTMLDivElement>>,
  Omit<FormItemProps, "children" | "onReset" | "status">,
  Omit<InputProps, "name"> {
  colClassName?: any;
  inputClassName?: string;
  onInputNumberChange?: any,
  isShowDescriptionPass?: boolean,
  form?: any,
  formattedInputNumber?: boolean
  isUseDesicmalSperator?: boolean;
  isResetPassValidation?: boolean
  setIsResetPassValidation?: any
  labelCol?: ColProps;
  wrapperCol?: ColProps;


}
