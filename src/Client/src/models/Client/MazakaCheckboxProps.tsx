import { CheckboxProps, ColProps } from "antd";
import { FormItemProps } from "antd/lib/form";
import { HTMLAttributes, ReactNode } from "react";

export interface MazakaCheckboxProps extends
  Omit<ColProps, keyof HTMLAttributes<HTMLDivElement>>,
  Omit<FormItemProps, "children">,
  Omit<CheckboxProps, "name"> {
  text: ReactNode | string;
  checkboxRequired?: boolean
  labelCol?: ColProps;
  wrapperCol?: ColProps;
}