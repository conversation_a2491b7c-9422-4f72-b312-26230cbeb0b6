<Project>
  <PropertyGroup>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
  </PropertyGroup>
  <ItemGroup>
    <PackageVersion Include="ClosedXML" Version="0.105.0" />
    <PackageVersion Include="EPPlus" Version="6.0.8" />
    <PackageVersion Include="SonarAnalyzer.CSharp" Version="10.7.0.110445" />
    <PackageVersion Include="AspNetCore.HealthChecks.UI.Client" Version="9.0.0" />
    <PackageVersion Include="Microsoft.AspNetCore.SpaProxy" Version="9.0.6" />
    <PackageVersion Include="Microsoft.AspNetCore.OpenApi" Version="9.0.6" />
    <PackageVersion Include="Scalar.AspNetCore" Version="2.5.5" />
    <PackageVersion Include="Swashbuckle.AspNetCore" Version="9.0.1" />
    <PackageVersion Include="MiniProfiler.AspNetCore.Mvc" Version="4.5.4" />
    <PackageVersion Include="MiniProfiler.EntityFrameworkCore" Version="4.5.4" />
    <PackageVersion Include="MediatR" Version="12.5.0" />
    <PackageVersion Include="MediatR.Contracts" Version="2.0.1" />
    <PackageVersion Include="Serilog" Version="4.3.0" />
    <PackageVersion Include="Serilog.AspNetCore" Version="9.0.0" />
    <PackageVersion Include="Serilog.Sinks.Seq" Version="9.0.0" />
    <PackageVersion Include="Serilog.Sinks.File" Version="7.0.0" />
    <PackageVersion Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.6" />
    <PackageVersion Include="FluentValidation" Version="12.0.0" />
    <PackageVersion Include="FluentValidation.DependencyInjectionExtensions" Version="12.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.6" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore" Version="9.0.6" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.6" />
    <PackageVersion Include="Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore" Version="9.0.6" />
    <PackageVersion Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="9.0.6" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.6" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.6" />
    <PackageVersion Include="MailKit" Version="4.13.0" />
    <PackageVersion Include="Mimekit" Version="4.13.0" />
    <PackageVersion Include="Microsoft.Extensions.Caching.Hybrid" Version="9.6.0" />
    <PackageVersion Include="Microsoft.AspNetCore.OData" Version="9.3.2" />
    <PackageVersion Include="Microsoft.OData.ModelBuilder" Version="2.0.0" />
    <PackageVersion Include="libphonenumber-csharp" Version="9.0.8" />
  </ItemGroup>
</Project>